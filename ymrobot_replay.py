#!/usr/bin/env python3
import rclpy
from rclpy.node import Node
from geometry_msgs.msg import Pose, Point, Quaternion
from ymrobot import YMrobot
from sensor_msgs.msg import JointState
import math
import time
import numpy as np

import argparse
import math
import os
import signal
import subprocess
import sys
import threading
import time
from typing import Optional

from rclpy.qos import QoSProfile, ReliabilityPolicy, HistoryPolicy, DurabilityPolicy
from std_msgs.msg import Float64MultiArray

# 用户自有机器人类
from ymrobot import YMrobot


class RosbagPlayer:
    """简单封装 ros2 bag play 的子进程，负责启动/停止与退出清理。"""
    def __init__(self, bag_path: str, rate: Optional[float] = None, loop: bool = False):
        self.bag_path = bag_path
        self.rate = rate
        self.loop = loop
        self.proc: Optional[subprocess.Popen] = None
        self._stop_evt = threading.Event()

    def start(self):
        if not os.path.isdir(self.bag_path):
            raise FileNotFoundError(f"Bag directory not found: {self.bag_path}")

        cmd = ["ros2", "bag", "play", self.bag_path]
        # 可选：调速
        if self.rate and self.rate > 0:
            cmd += ["-r", str(self.rate)]
        # 可选：循环播放
        if self.loop:
            cmd += ["-l"]

        # 对一些录包/放包环境，添加 --clock 可以发布 /clock（若需要仿真时钟）
        # cmd += ["--clock"]

        # 可选：覆盖QoS（如果录包时QoS不匹配）
        # cmd += ["--qos-profile-overrides-path", "/path/to/qos_override.yaml"]

        # 使用新会话，方便整体杀掉
        self.proc = subprocess.Popen(
            cmd,
            preexec_fn=os.setsid,  # 仅类Unix
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            bufsize=1,
        )

        # 后台打印bag输出（可选）
        threading.Thread(target=self._pump_output, daemon=True).start()

    def _pump_output(self):
        if not self.proc or not self.proc.stdout:
            return
        for line in self.proc.stdout:
            if self._stop_evt.is_set():
                break
            # 需要可见时再打印
            # print(f"[rosbag] {line}", end="")

    def stop(self):
        self._stop_evt.set()
        if self.proc and self.proc.poll() is None:
            try:
                # 整个进程组SIGINT，再过期后SIGKILL
                os.killpg(os.getpgid(self.proc.pid), signal.SIGINT)
            except ProcessLookupError:
                pass
            # 最多等2秒
            try:
                self.proc.wait(timeout=2.0)
            except subprocess.TimeoutExpired:
                try:
                    os.killpg(os.getpgid(self.proc.pid), signal.SIGKILL)
                except ProcessLookupError:
                    pass

    def is_running(self) -> bool:
        return self.proc is not None and self.proc.poll() is None

class GripperBridge(Node):
    """
    订阅 /gripper_commands (Float64MultiArray)，驱动 YMrobot 夹爪。
    data[0] -> left, data[1] -> right
    """
    def __init__(self, robot: YMrobot, topic: str = "/gripper_commands",
                 min_period_s: float = 0.0):
        super().__init__("gripper_bridge")

        self.robot = robot
        self.min_period_s = max(0.0, float(min_period_s))
        self._last_call_t = 0.0

        qos = QoSProfile(
            reliability=ReliabilityPolicy.RELIABLE,
            history=HistoryPolicy.KEEP_LAST,
            depth=10,
            durability=DurabilityPolicy.VOLATILE,
        )
        self.sub = self.create_subscription(
            Float64MultiArray, topic, self._on_msg, qos
        )

        self.get_logger().info(
            f"Subscribed to {topic} (Float64MultiArray). "
            f"Throttling: {self.min_period_s:.3f}s"
        )

    def _is_valid_num(self, x: float) -> bool:
        return x is not None and not (math.isnan(x) or math.isinf(x))

    def _on_msg(self, msg: Float64MultiArray):
        now = time.monotonic()
        if self.min_period_s > 0.0 and (now - self._last_call_t) < self.min_period_s:
            return  # 节流

        data = msg.data or []
        if len(data) < 2:
            self.get_logger().warn(
                f"/gripper_commands expects 2 values, got {len(data)}; ignoring."
            )
            return

        left_cmd, right_cmd = data[0], data[1]
        if not (self._is_valid_num(left_cmd) and self._is_valid_num(right_cmd)):
            self.get_logger().warn("Received NaN/Inf gripper command; ignoring.")
            return

        # 这里假设数值即为目标开合位置或角度；如需限幅，可在此 clamp
        try:
            self.robot.move_gripper_to_position("left", int(left_cmd))
        except Exception as e:
            self.get_logger().error(f"Left gripper command failed: {e}")

        try:
            self.robot.move_gripper_to_position("right", int(right_cmd))
        except Exception as e:
            self.get_logger().error(f"Right gripper command failed: {e}")

        self._last_call_t = now


def main():
    parser = argparse.ArgumentParser(
        description="Play a ROS 2 bag and drive robot grippers from /gripper_commands."
    )
    parser.add_argument("bag_path", type=str, help="Path to recorded rosbag2 directory")
    parser.add_argument("--topic", type=str, default="/gripper_commands",
                        help="Topic name (Float64MultiArray)")
    parser.add_argument("--rate", type=float, default=None,
                        help="Playback rate, e.g., 0.5, 1.0, 2.0")
    parser.add_argument("--loop", action="store_true",
                        help="Loop the bag")
    parser.add_argument("--throttle", type=float, default=0.0,
                        help="Min seconds between command forwards (default 0 = no throttle)")
    args = parser.parse_args()

    

    # 初始化 ROS
    rclpy.init(args=None)

    # 初始化机器人
    robot = YMrobot()

    node = GripperBridge(robot, topic=args.topic, min_period_s=args.throttle)

    # 启动 rosbag 播放
    player = RosbagPlayer(args.bag_path, rate=args.rate, loop=args.loop)
    try:
        player.start()
    except Exception as e:
        print(f"Failed to start rosbag player: {e}", file=sys.stderr)
        rclpy.shutdown()
        return

    # 优雅退出
    stop_event = threading.Event()

    def _handle_sig(signum, frame):
        stop_event.set()

    signal.signal(signal.SIGINT, _handle_sig)
    signal.signal(signal.SIGTERM, _handle_sig)

    try:
        while not stop_event.is_set():
            rclpy.spin_once(node, timeout_sec=0.2)
            # 如果没有循环播放且播放结束，则自动退出
            if not args.loop and not player.is_running():
                break
    finally:
        player.stop()
        node.get_logger().info("Shutting down...")
        node.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()