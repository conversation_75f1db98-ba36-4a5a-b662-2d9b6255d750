# 夹爪状态实时发布功能

## 功能概述

新增了夹爪状态实时发布功能，通过ROS2话题实时发布左右夹爪的位置状态。

## 功能特性

- **实时发布**: 以10Hz频率发布夹爪状态
- **双夹爪支持**: 同时发布左右夹爪状态
- **标准格式**: 使用Float64MultiArray消息类型
- **容错处理**: 包含异常处理和默认值

## 话题信息

- **话题名称**: `/gripper_states`
- **消息类型**: `std_msgs/Float64MultiArray`
- **发布频率**: 10Hz
- **数据格式**: `[left_gripper_position, right_gripper_position]`

## 数据说明

- **位置值范围**: 0-255
  - 0 = 夹爪完全闭合
  - 255 = 夹爪完全张开
  - 中间值 = 对应的开合程度

## 使用方法

### 1. 启动机器人节点

```python
import rclpy
from ymrobot import YMrobot

rclpy.init()
robot = YMrobot()  # 夹爪状态发布器自动启动

# 保持节点运行
rclpy.spin(robot)
```

### 2. 订阅夹爪状态

```python
import rclpy
from rclpy.node import Node
from std_msgs.msg import Float64MultiArray

class GripperStateListener(Node):
    def __init__(self):
        super().__init__('gripper_listener')
        self.subscription = self.create_subscription(
            Float64MultiArray,
            '/gripper_states',
            self.callback,
            10
        )
    
    def callback(self, msg):
        left_pos = msg.data[0]
        right_pos = msg.data[1]
        print(f"Left: {left_pos}, Right: {right_pos}")
```

### 3. 命令行监听

```bash
# 监听夹爪状态话题
ros2 topic echo /gripper_states

# 查看话题信息
ros2 topic info /gripper_states

# 查看发布频率
ros2 topic hz /gripper_states
```

## 测试脚本

### 监听器测试
```bash
python3 test_gripper_publisher.py
```

### 完整功能测试
```bash
python3 gripper_state_example.py
```

## 代码修改说明

### 1. 添加发布器
在`YMrobot.__init__()`中添加了夹爪状态发布器：
```python
self.gripper_state_pub = self.create_publisher(
    Float64MultiArray, '/gripper_states', 10)
```

### 2. 添加定时器
创建10Hz定时器定期发布状态：
```python
self.gripper_state_timer = self.create_timer(0.1, self.publish_gripper_states)
```

### 3. 发布方法
新增`publish_gripper_states()`方法：
- 获取左右夹爪状态
- 提取position值
- 发布为Float64MultiArray消息

## 错误处理

- 如果夹爪状态获取失败，使用默认值0.0
- 包含异常捕获，避免程序崩溃
- 记录警告日志便于调试

## 性能考虑

- 10Hz发布频率平衡了实时性和性能
- 异步获取状态避免阻塞主线程
- 轻量级消息格式减少网络负载

## 注意事项

1. 确保夹爪硬件连接正常
2. 检查串口权限和设备路径
3. 监控日志输出排查问题
4. 可根据需要调整发布频率
