# 夹爪指令发布功能使用指南

## 快速开始

### 1. 启动UDP控制器（带夹爪指令发布）
```bash
python3 ymrobot_vr_withUDP.py
```

### 2. 监听夹爪指令（新终端）
```bash
python3 test_gripper_commands_listener.py
```

### 3. 演示功能（可选，新终端）
```bash
python3 gripper_commands_demo.py
```

## 功能验证

### 方法1: 使用VR控制器
1. 启动UDP控制器
2. 启动监听器
3. 操作VR控制器的夹爪控制
4. 观察监听器输出的指令信息

### 方法2: 使用演示脚本
1. 启动监听器
2. 启动演示脚本
3. 观察模拟的夹爪指令变化

### 方法3: 命令行验证
```bash
# 查看话题列表
ros2 topic list | grep gripper

# 监听指令话题
ros2 topic echo /gripper_commands

# 查看话题信息
ros2 topic info /gripper_commands

# 查看发布频率
ros2 topic hz /gripper_commands
```

## 数据解读

### 输出格式
```
Gripper Commands - Left: 45.2% -> 140, Right: 78.9% -> 54
  Left Gripper:  Input=  45.2%  Command=140/255
  Right Gripper: Input=  78.9%  Command= 54/255
```

### 数值含义
- **Input**: VR控制器输入值 (0-100%)
- **Command**: 发送给夹爪的指令值 (0-255)
- **转换关系**: Command = 255 - (Input/100 * 255)

### 夹爪状态对应
- Input=0% → Command=255 → 夹爪完全张开
- Input=50% → Command=127 → 夹爪半开
- Input=100% → Command=0 → 夹爪完全闭合

## 集成到其他系统

### Python订阅示例
```python
import rclpy
from rclpy.node import Node
from std_msgs.msg import Float64MultiArray

class GripperMonitor(Node):
    def __init__(self):
        super().__init__('gripper_monitor')
        self.subscription = self.create_subscription(
            Float64MultiArray,
            '/gripper_commands',
            self.callback,
            10
        )
    
    def callback(self, msg):
        left_input, right_input, left_cmd, right_cmd = msg.data
        # 处理夹爪指令数据
        print(f"Left: {left_input}% -> {left_cmd}")
        print(f"Right: {right_input}% -> {right_cmd}")
```

### C++订阅示例
```cpp
#include "rclcpp/rclcpp.hpp"
#include "std_msgs/msg/float64_multi_array.hpp"

class GripperMonitor : public rclcpp::Node {
public:
    GripperMonitor() : Node("gripper_monitor") {
        subscription_ = this->create_subscription<std_msgs::msg::Float64MultiArray>(
            "/gripper_commands", 10,
            std::bind(&GripperMonitor::callback, this, std::placeholders::_1));
    }

private:
    void callback(const std_msgs::msg::Float64MultiArray::SharedPtr msg) {
        if (msg->data.size() >= 4) {
            double left_input = msg->data[0];
            double right_input = msg->data[1];
            double left_cmd = msg->data[2];
            double right_cmd = msg->data[3];
            // 处理数据
        }
    }
    
    rclcpp::Subscription<std_msgs::msg::Float64MultiArray>::SharedPtr subscription_;
};
```

## 故障排除

### 问题1: 话题不存在
**症状**: `ros2 topic list`中没有`/gripper_commands`
**解决**: 确保UDP控制器正常启动且继承了Node类

### 问题2: 无数据发布
**症状**: 话题存在但无数据
**解决**: 确认VR控制器有夹爪输入，检查`control_gripper`是否被调用

### 问题3: 数据格式错误
**症状**: 数组长度不是4
**解决**: 检查`publish_gripper_commands`方法的实现

### 问题4: ROS2连接问题
**症状**: 无法订阅话题
**解决**: 检查ROS2环境变量和网络配置

## 性能优化

### 发布频率
- 当前: 每次夹爪控制时发布
- 优化: 可添加频率限制避免过度发布

### 数据压缩
- 当前: 使用Float64数组
- 优化: 可使用自定义消息类型减少带宽

### 错误处理
- 当前: 包含异常处理
- 优化: 可添加重试机制和状态监控

## 扩展功能

### 1. 添加时间戳
```python
# 在消息中添加时间戳
msg.data = [left_input, right_input, left_cmd, right_cmd, time.time()]
```

### 2. 添加状态信息
```python
# 添加夹爪状态信息
gripper_status = self.robot.get_gripper_state("left")
if gripper_status:
    msg.data.extend([gripper_status.get('position', 0)])
```

### 3. 数据记录
```python
# 记录到文件
with open('gripper_commands.log', 'a') as f:
    f.write(f"{time.time()},{left_input},{right_input},{left_cmd},{right_cmd}\n")
```
