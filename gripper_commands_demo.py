#!/usr/bin/env python3
"""
夹爪指令发布功能演示脚本
模拟VR控制器输入，展示夹爪指令发布功能
"""
import rclpy
from rclpy.node import Node
from std_msgs.msg import Float64MultiArray
import time
import math

class GripperCommandDemo(Node):
    def __init__(self):
        super().__init__('gripper_command_demo')
        
        # 创建夹爪指令发布器
        self.gripper_cmd_pub = self.create_publisher(
            Float64MultiArray, '/gripper_commands', 10)
        
        # 创建定时器，模拟VR控制器输入
        self.timer = self.create_timer(0.1, self.demo_callback)  # 10Hz
        
        self.start_time = time.time()
        self.get_logger().info("Gripper command demo started")
        self.get_logger().info("Publishing to /gripper_commands at 10Hz")
        
    def demo_callback(self):
        """模拟VR控制器输入并发布夹爪指令"""
        current_time = time.time() - self.start_time
        
        # 模拟左右夹爪的正弦波输入 (0-100%)
        left_input = 50 + 40 * math.sin(current_time * 0.5)  # 慢速变化
        right_input = 50 + 30 * math.sin(current_time * 0.8)  # 稍快变化
        
        # 确保值在0-100范围内
        left_input = max(0, min(100, left_input))
        right_input = max(0, min(100, right_input))
        
        # 计算夹爪指令值 (翻转转换)
        left_cmd = 255 - (left_input / 100 * 255)
        right_cmd = 255 - (right_input / 100 * 255)
        
        # 发布指令
        msg = Float64MultiArray()
        msg.data = [left_input, right_input, left_cmd, right_cmd]
        self.gripper_cmd_pub.publish(msg)
        
        # 每秒打印一次状态
        if int(current_time) % 1 == 0 and current_time - int(current_time) < 0.1:
            self.get_logger().info(
                f"Demo: Left={left_input:.1f}%->{left_cmd:.0f}, "
                f"Right={right_input:.1f}%->{right_cmd:.0f}"
            )

def main():
    rclpy.init()
    
    demo = GripperCommandDemo()
    
    try:
        print("=" * 60)
        print("夹爪指令发布演示")
        print("模拟VR控制器输入，发布夹爪指令到 /gripper_commands")
        print("左夹爪: 慢速正弦波变化")
        print("右夹爪: 稍快正弦波变化")
        print("=" * 60)
        print("按 Ctrl+C 退出...")
        print()
        
        rclpy.spin(demo)
    except KeyboardInterrupt:
        print("\n演示结束")
    finally:
        demo.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
