#!/usr/bin/env python3
"""
测试夹爪指令发布功能的监听器
"""
import rclpy
from rclpy.node import Node
from std_msgs.msg import Float64MultiArray
import time

class GripperCommandListener(Node):
    def __init__(self):
        super().__init__('gripper_command_listener')
        
        # 订阅夹爪指令
        self.subscription = self.create_subscription(
            Float64MultiArray,
            '/gripper_commands',
            self.gripper_command_callback,
            10
        )
        self.get_logger().info("Gripper command listener started")
        self.get_logger().info("Listening to /gripper_commands topic")
        self.get_logger().info("Data format: [left_input(0-100), right_input(0-100), left_cmd(0-255), right_cmd(0-255)]")
        
    def gripper_command_callback(self, msg):
        """处理夹爪指令消息"""
        if len(msg.data) >= 4:
            left_input = msg.data[0]
            right_input = msg.data[1]
            left_cmd = msg.data[2]
            right_cmd = msg.data[3]
            
            self.get_logger().info(
                f"Gripper Commands - "
                f"Left: {left_input:.1f}% -> {left_cmd:.0f}, "
                f"Right: {right_input:.1f}% -> {right_cmd:.0f}"
            )
            
            # 显示详细信息
            print(f"  Left Gripper:  Input={left_input:6.1f}%  Command={left_cmd:3.0f}/255")
            print(f"  Right Gripper: Input={right_input:6.1f}%  Command={right_cmd:3.0f}/255")
            print("-" * 50)
            
        else:
            self.get_logger().warn(f"Invalid gripper command message: {msg.data}")

def main():
    rclpy.init()
    
    listener = GripperCommandListener()
    
    try:
        print("=" * 60)
        print("夹爪指令监听器已启动")
        print("话题: /gripper_commands")
        print("数据格式: [左输入值, 右输入值, 左指令值, 右指令值]")
        print("输入值范围: 0-100% (VR控制器输入)")
        print("指令值范围: 0-255 (发送给夹爪的实际指令)")
        print("=" * 60)
        print("按 Ctrl+C 退出...")
        print()
        
        rclpy.spin(listener)
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    finally:
        listener.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
