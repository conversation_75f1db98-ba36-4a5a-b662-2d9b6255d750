#!/usr/bin/env python3
"""
CAN夹爪连接诊断脚本
用于诊断CAN夹爪连接问题
"""

import time
import sys
import os
from ctypes import *

# 添加父目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.Gripper_can import GripperCAN, VCI_USBCAN2, STATUS_OK, VCI_INIT_CONFIG, VCI_CAN_OBJ


def check_permissions():
    """检查权限"""
    print("=" * 50)
    print("1. 检查权限")
    print("=" * 50)
    
    if os.geteuid() == 0:
        print("✓ 以root权限运行")
        return True
    else:
        print("✗ 未以root权限运行")
        print("请使用: sudo python3 diagnose_can.py")
        return False


def check_can_library():
    """检查CAN库"""
    print("\n" + "=" * 50)
    print("2. 检查CAN库")
    print("=" * 50)

    try:
        # 尝试不同的路径
        lib_paths = [
            './libcontrolcan.so',
            '../libcontrolcan.so',
            '/home/<USER>/workspace/gripper/libcontrolcan.so'
        ]

        canDLL = None
        for lib_path in lib_paths:
            try:
                canDLL = cdll.LoadLibrary(lib_path)
                print(f"✓ CAN库加载成功: {lib_path}")
                break
            except:
                continue

        if canDLL is None:
            raise Exception("无法找到libcontrolcan.so文件")
        # 检查关键函数
        functions = ['VCI_OpenDevice', 'VCI_InitCAN', 'VCI_StartCAN', 
                    'VCI_Transmit', 'VCI_Receive', 'VCI_CloseDevice']
        
        for func_name in functions:
            if hasattr(canDLL, func_name):
                print(f"✓ 函数 {func_name} 存在")
            else:
                print(f"✗ 函数 {func_name} 不存在")
                return False
        
        return True
        
    except Exception as e:
        print(f"✗ CAN库加载失败: {e}")
        return False


def get_can_library():
    """获取CAN库"""
    lib_paths = [
        './libcontrolcan.so',
        '../libcontrolcan.so',
        '/home/<USER>/workspace/gripper/libcontrolcan.so'
    ]

    for lib_path in lib_paths:
        try:
            return cdll.LoadLibrary(lib_path)
        except:
            continue

    raise Exception("无法找到libcontrolcan.so文件")


def check_can_device():
    """检查CAN设备"""
    print("\n" + "=" * 50)
    print("3. 检查CAN设备")
    print("=" * 50)

    try:
        canDLL = get_can_library()
        
        print("尝试打开CAN设备...")
        ret = canDLL.VCI_OpenDevice(VCI_USBCAN2, 0, 0)
        
        if ret == STATUS_OK:
            print("✓ CAN设备打开成功")
            
            # 测试不同通道
            channels = [0, 1]
            success_channels = []
            
            for channel in channels:
                print(f"测试通道 {channel}...")
                
                # 1Mbps, 80%采样点配置
                vci_initconfig = VCI_INIT_CONFIG(
                    0x80000008,  # AccCode
                    0xFFFFFFFF,  # AccMask
                    0,           # Reserved
                    0,           # Filter
                    0x00,        # Timing0 - 1Mbps
                    0x14,        # Timing1 - 80%采样点
                    0            # Mode
                )
                
                ret = canDLL.VCI_InitCAN(VCI_USBCAN2, 0, channel, byref(vci_initconfig))
                if ret == STATUS_OK:
                    print(f"  ✓ 通道 {channel} 初始化成功")
                    
                    ret = canDLL.VCI_StartCAN(VCI_USBCAN2, 0, channel)
                    if ret == STATUS_OK:
                        print(f"  ✓ 通道 {channel} 启动成功")
                        success_channels.append(channel)
                    else:
                        print(f"  ✗ 通道 {channel} 启动失败")
                else:
                    print(f"  ✗ 通道 {channel} 初始化失败")
            
            canDLL.VCI_CloseDevice(VCI_USBCAN2, 0)
            
            if success_channels:
                print(f"✓ 可用通道: {success_channels}")
                return success_channels
            else:
                print("✗ 没有可用通道")
                return []
        else:
            print(f"✗ CAN设备打开失败，返回值: {ret}")
            return []
            
    except Exception as e:
        print(f"✗ CAN设备检查失败: {e}")
        return []


def test_gripper_communication(channels):
    """测试夹爪通信"""
    print("\n" + "=" * 50)
    print("4. 测试夹爪通信")
    print("=" * 50)
    
    node_ids = [1, 2, 3, 4, 5]  # 常见的节点ID
    
    for channel in channels:
        print(f"\n测试通道 {channel}:")
        
        for node_id in node_ids:
            print(f"  测试节点ID {node_id}...")
            
            try:
                gripper = GripperCAN(node_id=node_id, channel=channel)
                print(f"    ✓ 夹爪对象创建成功")
                
                # 发送测试指令
                result = gripper.send_command(pos_cmd=0x80, timeout=2.0)
                
                if result:
                    print(f"    ✓ 通信成功! 状态: {result}")
                    gripper.close()
                    return (channel, node_id)
                else:
                    print(f"    ⚠ 无回复")
                
                gripper.close()
                
            except Exception as e:
                print(f"    ✗ 失败: {e}")
    
    return None


def test_continuous_monitoring(channel, node_id):
    """测试连续监控"""
    print("\n" + "=" * 50)
    print("5. 测试连续监控")
    print("=" * 50)
    
    try:
        print(f"使用通道 {channel}, 节点ID {node_id}")
        gripper = GripperCAN(node_id=node_id, channel=channel)
        
        status_count = 0
        def status_callback(status):
            nonlocal status_count
            status_count += 1
            pos = status.get('position', 0)
            force = status.get('force', 0)
            state = status.get('state', 0)
            print(f"    [状态 #{status_count}] 位置:0x{pos:02X} 力矩:{force} 状态:{state}")
        
        gripper.set_status_callback(status_callback)
        
        print("监控10秒，同时发送测试指令...")
        
        commands = [
            (0xFF, "打开"),
            (0x00, "闭合"),
            (0x7F, "中间"),
        ]
        
        for i, (pos, desc) in enumerate(commands):
            print(f"\n  发送指令 {i+1}: {desc} (0x{pos:02X})")
            result = gripper.send_command(pos_cmd=pos)
            if result:
                print(f"    指令成功: {result}")
            else:
                print(f"    指令失败")
            
            time.sleep(3)
        
        print(f"\n✓ 监控完成，总共收到 {status_count} 次状态更新")
        
        gripper.close()
        return status_count > 0
        
    except Exception as e:
        print(f"✗ 连续监控失败: {e}")
        return False


def main():
    """主诊断函数"""
    print("CAN夹爪连接诊断工具")
    print("此工具将逐步检查CAN夹爪连接问题")
    print()
    
    # 1. 检查权限
    if not check_permissions():
        return
    
    # 2. 检查CAN库
    if not check_can_library():
        print("\n❌ CAN库检查失败，无法继续")
        return
    
    # 3. 检查CAN设备
    channels = check_can_device()
    if not channels:
        print("\n❌ CAN设备检查失败")
        print("可能的原因:")
        print("- CAN设备未连接")
        print("- 设备驱动问题")
        print("- 硬件故障")
        return
    
    # 4. 测试夹爪通信
    result = test_gripper_communication(channels)
    if not result:
        print("\n❌ 夹爪通信测试失败")
        print("可能的原因:")
        print("- 夹爪未上电")
        print("- 节点ID配置错误")
        print("- CAN总线连接问题")
        print("- 终端电阻问题")
        print("- 波特率不匹配")
        return
    
    channel, node_id = result
    print(f"\n✅ 找到工作的夹爪: 通道{channel}, 节点ID{node_id}")
    
    # 5. 测试连续监控
    if test_continuous_monitoring(channel, node_id):
        print("\n🎉 CAN夹爪连接完全正常!")
        print(f"推荐配置: 通道={channel}, 节点ID={node_id}")
        print("\n使用方法:")
        print(f"gripper = GripperCAN(node_id={node_id}, channel={channel})")
    else:
        print("\n⚠️ 基本通信正常，但连续监控有问题")
    
    print("\n诊断完成!")


if __name__ == '__main__':
    main()
