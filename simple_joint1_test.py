#!/usr/bin/env python3
import rclpy
from ymrobot import YMrobot
import time
import math

def simple_joint1_test():
    """简单测试关节1控制"""
    print("=== 关节1控制测试 ===")
    
    # 初始化
    rclpy.init()
    robot = YMrobot()
    time.sleep(2.0)
    
    try:
        print("1. 设置关节1为0度")
        robot.control_right_arm_joint_position_ros2controller([0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0])
        time.sleep(2.0)
        
        print("2. 设置关节1为10度")
        robot.control_right_arm_joint_position_ros2controller([math.radians(10), 0.0, 0.0, 0.0, 0.0, 0.0, 0.0])
        time.sleep(2.0)
        
        print("3. 设置关节1为-10度")
        robot.control_right_arm_joint_position_ros2controller([math.radians(-10), 0.0, 0.0, 0.0, 0.0, 0.0, 0.0])
        time.sleep(2.0)
        
        print("4. 回到0度")
        robot.control_right_arm_joint_position_ros2controller([0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0])
        time.sleep(2.0)
        
        print("测试完成")
        
    except Exception as e:
        print(f"测试出错: {e}")
    finally:
        robot.shutdown()
        rclpy.shutdown()

if __name__ == "__main__":
    simple_joint1_test() 