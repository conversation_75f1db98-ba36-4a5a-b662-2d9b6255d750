#!/usr/bin/env python3
"""
夹爪状态发布功能使用示例
"""
import rclpy
from ymrobot import YMrobot
import time

def main():
    # 初始化ROS2
    rclpy.init()
    
    try:
        # 创建机器人实例
        robot = YMrobot()
        
        print("机器人初始化完成，夹爪状态发布器已启动")
        print("夹爪状态将以10Hz频率发布到 /gripper_states 话题")
        print("数据格式: [left_gripper_position, right_gripper_position]")
        print("位置值范围: 0-255 (0=闭合, 255=张开)")
        print("\n按 Ctrl+C 退出...")
        
        # 测试夹爪控制
        print("\n测试夹爪控制...")
        
        # 打开左夹爪
        print("打开左夹爪...")
        robot.open_gripper("left")
        time.sleep(2)
        
        # 关闭左夹爪
        print("关闭左夹爪...")
        robot.close_gripper("left")
        time.sleep(2)
        
        # 打开右夹爪
        print("打开右夹爪...")
        robot.open_gripper("right")
        time.sleep(2)
        
        # 关闭右夹爪
        print("关闭右夹爪...")
        robot.close_gripper("right")
        time.sleep(2)
        
        # 移动到特定位置
        print("移动左夹爪到位置 128...")
        robot.move_gripper_to_position("left", 128)
        time.sleep(2)
        
        print("移动右夹爪到位置 128...")
        robot.move_gripper_to_position("right", 128)
        time.sleep(2)
        
        print("\n夹爪测试完成，继续发布状态...")
        
        # 保持运行以继续发布状态
        while rclpy.ok():
            rclpy.spin_once(robot, timeout_sec=0.1)
            
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"发生错误: {e}")
    finally:
        # 清理资源
        if 'robot' in locals():
            robot.shutdown()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
