2025-07-22 15:37:31 [INFO] logger_config.py:102 - 日志系统初始化完成，日志文件: /home/<USER>/Spatial_AI/logs/gripper_485/gripper_485_1_20250722_153731.log
2025-07-22 15:37:31 [INFO] Gripper_485.py:53 - 初始化RS485夹爪 - 端口: /dev/ttyUSB1, 节点ID: 1, 波特率: 115200
2025-07-22 15:37:31 [DEBUG] Gripper_485.py:92 - 启动RS485消息接收线程
2025-07-22 15:37:31 [INFO] Gripper_485.py:96 - RS485消息接收线程已启动
2025-07-22 15:37:31 [INFO] Gripper_485.py:53 - 初始化RS485夹爪 - 端口: /dev/ttyUSB2, 节点ID: 1, 波特率: 115200
2025-07-22 15:37:31 [DEBUG] Gripper_485.py:92 - 启动RS485消息接收线程
2025-07-22 15:37:31 [INFO] Gripper_485.py:96 - RS485消息接收线程已启动
2025-07-22 15:37:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:37:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:37:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:37:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:37:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:37:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:37:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:37:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:37:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:37:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:37:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:37:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:37:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:37:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:37:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:37:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:37:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:37:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:37:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:37:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:37:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:33 [INFO] Gripper_485.py:372 - 执行关闭夹爪指令
2025-07-22 15:37:33 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7E
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7D
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7B
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x79
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x77
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x75
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x72
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x6F
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x6C
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x69
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x65
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x61
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x5D
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x58
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x53
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x4E
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x49
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x45
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x40
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x3B
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x37
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x32
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x2D
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x28
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x24
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x1F
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x1B
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x17
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x14
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x10
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x0D
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x0B
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x08
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x06
2025-07-22 15:37:33 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x00, 实际=0x06, 误差=6
2025-07-22 15:37:33 [INFO] Gripper_485.py:372 - 执行关闭夹爪指令
2025-07-22 15:37:33 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x04
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x03
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x02
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x01
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7E
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7D
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7C
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7A
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x78
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x76
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x74
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x71
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x6E
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x6B
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x67
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x63
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x5F
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x5A
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x56
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x51
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x4C
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x47
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x42
2025-07-22 15:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x3D
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x38
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x33
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x2E
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x29
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x24
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x20
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x1C
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x18
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x14
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x11
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x0E
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x0B
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x09
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x07
2025-07-22 15:37:34 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x00, 实际=0x07, 误差=7
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x05
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x03
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x02
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x01
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [INFO] Gripper_485.py:358 - 执行打开夹爪指令
2025-07-22 15:37:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:40 [WARNING] Gripper_485.py:255 - 等待回复超时，节点ID: 1
2025-07-22 15:37:40 [WARNING] Gripper_485.py:257 - 最后状态: 目标=0xFF, 实际=0x00
2025-07-22 15:37:40 [INFO] Gripper_485.py:358 - 执行打开夹爪指令
2025-07-22 15:37:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 15:37:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 15:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:45 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:45 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:45 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:45 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:45 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:45 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:45 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:45 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:45 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:45 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:45 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:45 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:45 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:45 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:45 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:45 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:37:45 [WARNING] Gripper_485.py:255 - 等待回复超时，节点ID: 1
2025-07-22 15:37:45 [WARNING] Gripper_485.py:257 - 最后状态: 目标=0xFF, 实际=0x00
