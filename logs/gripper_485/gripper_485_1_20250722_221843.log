2025-07-22 22:18:43 [INFO] logger_config.py:102 - 日志系统初始化完成，日志文件: /home/<USER>/Spatial_AI/logs/gripper_485/gripper_485_1_20250722_221843.log
2025-07-22 22:18:43 [INFO] Gripper_485.py:53 - 初始化RS485夹爪 - 端口: /dev/ttyUSB1, 节点ID: 1, 波特率: 115200
2025-07-22 22:18:43 [DEBUG] Gripper_485.py:92 - 启动RS485消息接收线程
2025-07-22 22:18:43 [INFO] Gripper_485.py:96 - RS485消息接收线程已启动
2025-07-22 22:18:43 [INFO] Gripper_485.py:53 - 初始化RS485夹爪 - 端口: /dev/ttyUSB2, 节点ID: 1, 波特率: 115200
2025-07-22 22:18:43 [DEBUG] Gripper_485.py:92 - 启动RS485消息接收线程
2025-07-22 22:18:43 [INFO] Gripper_485.py:96 - RS485消息接收线程已启动
2025-07-22 22:18:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:18:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:18:56 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:18:56 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 10, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:18:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:18:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:18:56 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:18:56 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 10, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:18:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:18:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:18:56 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:18:56 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 10, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:18:56 [INFO] Gripper_485.py:386 - 移动到位置 0xF7
2025-07-22 22:18:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF7, 力矩=0xFF
2025-07-22 22:18:56 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:18:56 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 10, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:18:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:18:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:18:57 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:18:57 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 10, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:18:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:18:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:18:57 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:18:57 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 10, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:18:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:18:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:18:57 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:18:57 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 10, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:18:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:18:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:18:57 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:18:57 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 10, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:18:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:18:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:18:57 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:18:57 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 10, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:18:57 [INFO] Gripper_485.py:386 - 移动到位置 0xBE
2025-07-22 22:18:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xBE, 力矩=0xFF
2025-07-22 22:18:57 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:18:57 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 10, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:18:57 [INFO] Gripper_485.py:386 - 移动到位置 0x79
2025-07-22 22:18:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x79, 力矩=0xFF
2025-07-22 22:18:57 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:18:57 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 10, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:18:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:18:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:18:57 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:18:57 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 10, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:18:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:18:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:18:57 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:18:57 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 10, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:18:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:18:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:18:57 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:18:57 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 10, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:18:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:18:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:18:57 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:18:57 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 10, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:18:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:18:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:18:57 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:18:57 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 10, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:18:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:18:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:18:57 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:18:57 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 10, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:18:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:18:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:18:57 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:18:57 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 10, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:18:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:18:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:18:57 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:18:57 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 10, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:18:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:18:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:18:57 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:18:57 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 10, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:18:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:18:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:18:57 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:18:57 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 10, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:18:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:18:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:18:57 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:18:57 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 10, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:18:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:18:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:18:57 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:18:57 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 10, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:18:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:18:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:18:57 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:18:57 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 10, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:18:57 [INFO] Gripper_485.py:386 - 移动到位置 0xEC
2025-07-22 22:18:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xEC, 力矩=0xFF
2025-07-22 22:18:57 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:18:57 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 10, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:18:58 [INFO] Gripper_485.py:386 - 移动到位置 0xF4
2025-07-22 22:18:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF4, 力矩=0xFF
2025-07-22 22:18:58 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:18:58 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 10, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:18:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:18:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:18:58 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:18:58 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 10, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:18:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:18:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:18:58 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:18:58 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 10, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:18:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:18:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:18:58 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:18:58 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 10, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:18:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:18:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:18:58 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:18:58 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 10, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:18:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:18:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:18:58 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:18:58 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 10, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:18:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:18:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:18:58 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:18:58 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 10, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:18:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:18:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:18:58 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:18:58 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 10, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:18:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:18:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:18:58 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:18:58 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 10, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:18:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:18:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:18:58 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:18:58 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 10, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:18:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:18:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:18:58 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:18:58 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 10, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:18:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:18:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:18:58 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:18:58 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 10, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:18:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:18:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:18:58 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:18:58 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 10, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:18:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:18:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:18:58 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:18:58 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 10, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:18:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:18:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:18:58 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:18:58 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 10, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:18:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:18:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:18:58 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:18:58 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 10, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:18:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:18:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:18:58 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:18:58 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 10, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:18:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:18:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:18:58 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:18:58 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 10, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:18:59 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:18:59 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:18:59 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:18:59 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 10, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:18:59 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:18:59 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:18:59 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:18:59 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 10, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:18:59 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:18:59 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:18:59 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:18:59 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 10, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:18:59 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:18:59 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:18:59 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:18:59 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 10, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:18:59 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:18:59 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:18:59 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:18:59 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 10, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:18:59 [INFO] Gripper_485.py:386 - 移动到位置 0x8E
2025-07-22 22:18:59 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x8E, 力矩=0xFF
2025-07-22 22:18:59 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:18:59 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 10, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:18:59 [INFO] Gripper_485.py:386 - 移动到位置 0xD6
2025-07-22 22:18:59 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xD6, 力矩=0xFF
2025-07-22 22:18:59 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:18:59 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 10, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:06 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:06 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:06 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:06 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 10, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:06 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:06 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:06 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:06 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 10, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:06 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:06 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:06 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:06 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 10, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:06 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:06 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:06 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:06 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 10, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:06 [INFO] Gripper_485.py:386 - 移动到位置 0x8E
2025-07-22 22:19:06 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x8E, 力矩=0xFF
2025-07-22 22:19:06 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:06 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 10, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:06 [INFO] Gripper_485.py:386 - 移动到位置 0xF0
2025-07-22 22:19:06 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF0, 力矩=0xFF
2025-07-22 22:19:06 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:06 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 10, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:06 [INFO] Gripper_485.py:386 - 移动到位置 0xEA
2025-07-22 22:19:06 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xEA, 力矩=0xFF
2025-07-22 22:19:06 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:06 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 10, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:06 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:06 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:06 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:06 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 10, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:06 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:06 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:06 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:06 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 10, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:06 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:06 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:06 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:06 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 10, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:06 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:06 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:06 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:06 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 10, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:06 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:06 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:06 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:06 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 10, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:07 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:07 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 10, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:07 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:07 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 10, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:07 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:07 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 10, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:07 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:07 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 10, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:07 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:07 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 10, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:07 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:07 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 10, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:07 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:07 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 200, 'velocity': 248, 'force': 160, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:19:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:07 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:07 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 190, 'velocity': 254, 'force': 51, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:19:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:07 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:07 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 190, 'velocity': 254, 'force': 51, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:19:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:07 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:07 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 190, 'velocity': 254, 'force': 51, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:19:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:07 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:07 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 190, 'velocity': 254, 'force': 51, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:19:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:07 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:07 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 190, 'velocity': 254, 'force': 51, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:19:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:07 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:07 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 190, 'velocity': 254, 'force': 51, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:19:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:07 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:07 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 190, 'velocity': 254, 'force': 51, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:19:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:07 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:07 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 190, 'velocity': 254, 'force': 51, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:19:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:07 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:07 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 190, 'velocity': 254, 'force': 51, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:19:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:07 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:07 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 190, 'velocity': 254, 'force': 51, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:19:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:07 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:07 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 190, 'velocity': 254, 'force': 51, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:19:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:07 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:07 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 190, 'velocity': 254, 'force': 51, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:19:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:07 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:07 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 190, 'velocity': 254, 'force': 51, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:19:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:07 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:07 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 190, 'velocity': 254, 'force': 51, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:19:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:07 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:07 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 190, 'velocity': 254, 'force': 51, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:19:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:07 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:07 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 190, 'velocity': 254, 'force': 51, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:19:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:07 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:07 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 190, 'velocity': 254, 'force': 51, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:19:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:07 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:07 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 190, 'velocity': 254, 'force': 51, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:19:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:07 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:07 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 190, 'velocity': 254, 'force': 51, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:19:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:07 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:07 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 190, 'velocity': 254, 'force': 51, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:19:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:07 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:07 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 190, 'velocity': 254, 'force': 51, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:19:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:07 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:07 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 190, 'velocity': 254, 'force': 51, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:19:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:07 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:07 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 190, 'velocity': 254, 'force': 51, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:19:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:07 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:07 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 190, 'velocity': 254, 'force': 51, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:19:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:08 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:08 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 190, 'velocity': 254, 'force': 51, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:19:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:08 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:08 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 190, 'velocity': 254, 'force': 51, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:19:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:08 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:08 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 190, 'velocity': 254, 'force': 51, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:19:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:08 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:08 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 65, 'fault_code': 1, 'state': 0, 'position': 0, 'velocity': 0, 'force': 0, 'reserved1': 190, 'reserved2': 255, 'reserved3': 0, 'fault_description': '过温警报', 'state_description': '已达到目标位置'}
2025-07-22 22:19:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:08 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:08 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 65, 'fault_code': 1, 'state': 0, 'position': 0, 'velocity': 0, 'force': 0, 'reserved1': 190, 'reserved2': 255, 'reserved3': 0, 'fault_description': '过温警报', 'state_description': '已达到目标位置'}
2025-07-22 22:19:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:08 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:08 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 65, 'fault_code': 1, 'state': 0, 'position': 0, 'velocity': 0, 'force': 0, 'reserved1': 190, 'reserved2': 255, 'reserved3': 0, 'fault_description': '过温警报', 'state_description': '已达到目标位置'}
2025-07-22 22:19:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:08 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:08 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 65, 'fault_code': 1, 'state': 0, 'position': 0, 'velocity': 0, 'force': 0, 'reserved1': 190, 'reserved2': 255, 'reserved3': 0, 'fault_description': '过温警报', 'state_description': '已达到目标位置'}
2025-07-22 22:19:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:08 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:08 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 65, 'fault_code': 1, 'state': 0, 'position': 0, 'velocity': 0, 'force': 0, 'reserved1': 190, 'reserved2': 255, 'reserved3': 0, 'fault_description': '过温警报', 'state_description': '已达到目标位置'}
2025-07-22 22:19:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:08 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:08 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 65, 'fault_code': 1, 'state': 0, 'position': 0, 'velocity': 0, 'force': 0, 'reserved1': 190, 'reserved2': 255, 'reserved3': 0, 'fault_description': '过温警报', 'state_description': '已达到目标位置'}
2025-07-22 22:19:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:08 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:08 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 65, 'fault_code': 1, 'state': 0, 'position': 0, 'velocity': 0, 'force': 0, 'reserved1': 190, 'reserved2': 255, 'reserved3': 0, 'fault_description': '过温警报', 'state_description': '已达到目标位置'}
2025-07-22 22:19:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:08 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:08 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 65, 'fault_code': 1, 'state': 0, 'position': 0, 'velocity': 0, 'force': 0, 'reserved1': 190, 'reserved2': 255, 'reserved3': 0, 'fault_description': '过温警报', 'state_description': '已达到目标位置'}
2025-07-22 22:19:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:08 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:08 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 65, 'fault_code': 1, 'state': 0, 'position': 0, 'velocity': 0, 'force': 0, 'reserved1': 190, 'reserved2': 255, 'reserved3': 0, 'fault_description': '过温警报', 'state_description': '已达到目标位置'}
2025-07-22 22:19:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:08 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:08 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 65, 'fault_code': 1, 'state': 0, 'position': 0, 'velocity': 0, 'force': 0, 'reserved1': 190, 'reserved2': 255, 'reserved3': 0, 'fault_description': '过温警报', 'state_description': '已达到目标位置'}
2025-07-22 22:19:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:08 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:08 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 65, 'fault_code': 1, 'state': 0, 'position': 0, 'velocity': 0, 'force': 0, 'reserved1': 190, 'reserved2': 255, 'reserved3': 0, 'fault_description': '过温警报', 'state_description': '已达到目标位置'}
2025-07-22 22:19:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:08 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:08 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 65, 'fault_code': 1, 'state': 0, 'position': 0, 'velocity': 0, 'force': 0, 'reserved1': 190, 'reserved2': 255, 'reserved3': 0, 'fault_description': '过温警报', 'state_description': '已达到目标位置'}
2025-07-22 22:19:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:08 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:08 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 65, 'fault_code': 1, 'state': 0, 'position': 0, 'velocity': 0, 'force': 0, 'reserved1': 190, 'reserved2': 255, 'reserved3': 0, 'fault_description': '过温警报', 'state_description': '已达到目标位置'}
2025-07-22 22:19:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:08 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:08 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:08 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:08 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:08 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:08 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:08 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:08 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:08 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:08 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:08 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:08 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:08 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:08 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:08 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:08 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:08 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:08 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:08 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:08 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:08 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:08 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:08 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:08 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:09 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:09 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:09 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:09 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 65, 'fault_code': 1, 'state': 0, 'position': 0, 'velocity': 0, 'force': 0, 'reserved1': 190, 'reserved2': 255, 'reserved3': 0, 'fault_description': '过温警报', 'state_description': '已达到目标位置'}
2025-07-22 22:19:09 [INFO] Gripper_485.py:386 - 移动到位置 0x94
2025-07-22 22:19:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x94, 力矩=0xFF
2025-07-22 22:19:09 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:09 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:10 [INFO] Gripper_485.py:386 - 移动到位置 0x1B
2025-07-22 22:19:10 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x1B, 力矩=0xFF
2025-07-22 22:19:10 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:10 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:10 [INFO] Gripper_485.py:386 - 移动到位置 0x1B
2025-07-22 22:19:10 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x1B, 力矩=0xFF
2025-07-22 22:19:10 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:10 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:10 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:10 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:10 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:10 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:10 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:10 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:10 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:10 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:10 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:10 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:10 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:10 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:10 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:10 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:10 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:10 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:10 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:10 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:10 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:10 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:11 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:11 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:11 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:11 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:11 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:11 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:11 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:11 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:11 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:11 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:11 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:11 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:11 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:11 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:11 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:11 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:11 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:11 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:11 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:11 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 68, 'velocity': 255, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:19:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:11 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:11 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 63, 'velocity': 254, 'force': 65, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:19:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:11 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:11 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 42, 'velocity': 254, 'force': 73, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:19:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:11 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:11 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 40, 'velocity': 254, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:19:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:11 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:11 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 40, 'velocity': 254, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:19:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:11 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:11 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 40, 'velocity': 254, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:19:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:11 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:11 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 40, 'velocity': 254, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:19:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:11 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:11 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 40, 'velocity': 254, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:19:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:11 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:11 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 40, 'velocity': 254, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:19:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:11 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:11 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 40, 'velocity': 254, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:19:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:11 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:11 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 40, 'velocity': 254, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:19:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:11 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:11 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 40, 'velocity': 254, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:19:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:11 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:11 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 40, 'velocity': 254, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:19:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:11 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:11 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 40, 'velocity': 254, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:19:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:11 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:11 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 40, 'velocity': 254, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:19:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:11 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:11 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 40, 'velocity': 254, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:19:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:11 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:11 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 40, 'velocity': 254, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:19:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:11 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:11 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 40, 'velocity': 254, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:19:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:11 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:11 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 40, 'velocity': 254, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:19:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:11 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:11 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 40, 'velocity': 254, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:19:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:11 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:11 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 40, 'velocity': 254, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:19:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:11 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:11 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 40, 'velocity': 254, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:19:12 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:12 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:12 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:12 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 40, 'velocity': 254, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:19:12 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:12 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:12 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:12 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 40, 'velocity': 254, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:19:12 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:12 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:12 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:12 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 40, 'velocity': 254, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:19:12 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:12 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:12 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:12 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 40, 'velocity': 254, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:19:12 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:12 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:12 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:12 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 40, 'velocity': 254, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:19:12 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:12 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:12 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:12 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 40, 'velocity': 254, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:19:12 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:12 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:12 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:12 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 40, 'velocity': 254, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:19:12 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:12 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:12 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:12 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 40, 'velocity': 254, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:19:12 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:12 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:12 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:12 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 40, 'velocity': 254, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:19:12 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:12 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:12 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:12 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 40, 'velocity': 254, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:19:12 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:12 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:12 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:12 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 40, 'velocity': 254, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:19:12 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:12 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:12 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:12 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 40, 'velocity': 254, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:19:12 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:12 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:12 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:12 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 40, 'velocity': 254, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:19:12 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:12 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:12 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:12 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 40, 'velocity': 254, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:19:12 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:12 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:12 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:12 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 40, 'velocity': 254, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:19:12 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:12 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:12 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:12 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 40, 'velocity': 254, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:19:12 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:12 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:12 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:12 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 40, 'velocity': 254, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:19:12 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:12 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:12 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:12 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 40, 'velocity': 254, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:19:12 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:12 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:12 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:12 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 40, 'velocity': 254, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:19:12 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:12 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:12 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:12 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 40, 'velocity': 254, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:19:12 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:12 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:12 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:12 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 40, 'velocity': 254, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:19:12 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:12 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:12 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:12 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 40, 'velocity': 254, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:19:12 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:12 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:12 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:12 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 40, 'velocity': 254, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:19:12 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:12 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:12 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:12 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:12 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:12 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:12 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:12 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 190, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:12 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:12 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:12 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:12 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 190, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:12 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:12 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:12 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:12 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 190, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:12 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:12 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:12 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:12 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 190, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:12 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:12 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:12 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:12 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 190, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:12 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:12 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:12 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:12 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 65, 'fault_code': 1, 'state': 0, 'position': 0, 'velocity': 0, 'force': 0, 'reserved1': 190, 'reserved2': 255, 'reserved3': 0, 'fault_description': '过温警报', 'state_description': '已达到目标位置'}
2025-07-22 22:19:13 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:13 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:13 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:13 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 65, 'fault_code': 1, 'state': 0, 'position': 0, 'velocity': 0, 'force': 0, 'reserved1': 190, 'reserved2': 255, 'reserved3': 0, 'fault_description': '过温警报', 'state_description': '已达到目标位置'}
2025-07-22 22:19:13 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:13 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:13 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:13 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 65, 'fault_code': 1, 'state': 0, 'position': 0, 'velocity': 0, 'force': 0, 'reserved1': 190, 'reserved2': 255, 'reserved3': 0, 'fault_description': '过温警报', 'state_description': '已达到目标位置'}
2025-07-22 22:19:13 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:13 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:13 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:13 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 65, 'fault_code': 1, 'state': 0, 'position': 0, 'velocity': 0, 'force': 0, 'reserved1': 190, 'reserved2': 255, 'reserved3': 0, 'fault_description': '过温警报', 'state_description': '已达到目标位置'}
2025-07-22 22:19:13 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:13 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:13 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:13 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 65, 'fault_code': 1, 'state': 0, 'position': 0, 'velocity': 0, 'force': 0, 'reserved1': 190, 'reserved2': 255, 'reserved3': 0, 'fault_description': '过温警报', 'state_description': '已达到目标位置'}
2025-07-22 22:19:13 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:13 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:13 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:13 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 65, 'fault_code': 1, 'state': 0, 'position': 0, 'velocity': 0, 'force': 0, 'reserved1': 190, 'reserved2': 255, 'reserved3': 0, 'fault_description': '过温警报', 'state_description': '已达到目标位置'}
2025-07-22 22:19:13 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:13 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:13 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:13 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 65, 'fault_code': 1, 'state': 0, 'position': 0, 'velocity': 0, 'force': 0, 'reserved1': 190, 'reserved2': 255, 'reserved3': 0, 'fault_description': '过温警报', 'state_description': '已达到目标位置'}
2025-07-22 22:19:13 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:13 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:13 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:13 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:13 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:13 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:13 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:13 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:13 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:13 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:13 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:13 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 189, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:13 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:13 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:13 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:13 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:13 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:13 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:13 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:13 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:13 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:13 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:13 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:13 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:13 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:13 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:13 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:13 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 190, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:13 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:13 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:13 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:13 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 190, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:13 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:13 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:13 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:13 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 190, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:13 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:13 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:13 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:13 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 190, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:13 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:13 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:13 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:13 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 190, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:13 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:13 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:13 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:13 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 190, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:13 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:13 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:13 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:13 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 190, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:13 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:13 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:13 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:13 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 190, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:13 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:13 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:13 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:13 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 190, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:13 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:13 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:13 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:13 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 190, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:13 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:13 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:13 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:13 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 190, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:13 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:13 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:13 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:13 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 190, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:13 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:13 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:13 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:13 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 190, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:13 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:13 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:13 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:13 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 190, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:13 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:13 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:13 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:13 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 190, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:13 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:13 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:13 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:13 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 190, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:13 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:13 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:13 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:13 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 190, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:14 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:14 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:14 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 190, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:14 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:14 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:14 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 190, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:14 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:14 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:14 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 190, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:14 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:14 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:14 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 190, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:14 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:14 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:14 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 192, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:14 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:14 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:14 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:14 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:14 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:14 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:14 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:14 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:14 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:14 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:14 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:14 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:14 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:14 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:14 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 190, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:14 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:14 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:14 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:14 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:14 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:14 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 189, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:14 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:14 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:14 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 189, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:14 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:14 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:14 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 189, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:14 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:14 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:14 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 189, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:14 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:14 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:14 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 189, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:14 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:14 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:14 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 189, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:14 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:14 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:14 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 189, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:14 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:14 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:14 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:14 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:14 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:14 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 190, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:14 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:14 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:14 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 190, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:14 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:14 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:14 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:14 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:14 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:14 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:14 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:14 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:14 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:14 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:14 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:14 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:14 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:14 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:14 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:14 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:14 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:14 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:14 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:14 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:14 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:14 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:14 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:14 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 190, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:14 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:14 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:14 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 190, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:15 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:15 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:15 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:15 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:15 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:15 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 190, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:15 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:15 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:15 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:15 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:15 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:15 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:15 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:15 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:15 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:15 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:15 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:15 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:15 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:15 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:15 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:15 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:15 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:15 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:15 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:15 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:15 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:15 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:15 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:15 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:15 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:15 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:15 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:15 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:15 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:15 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:15 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:15 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:15 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:15 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:15 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:15 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:15 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:15 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:15 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:15 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:15 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:15 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:15 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:15 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:15 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:15 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:15 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:15 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:15 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:15 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 190, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:15 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:15 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 190, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:15 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:15 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 190, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:16 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:16 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:16 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:16 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 190, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:16 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:16 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:16 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:16 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 190, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:16 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:16 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:16 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:16 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 190, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:16 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:16 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:16 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:16 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 190, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:16 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:16 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:16 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:16 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 190, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:16 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:16 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:16 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:16 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 190, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:16 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:16 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:16 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:16 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 190, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:16 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:16 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:16 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:16 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 190, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:16 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:16 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:16 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:16 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 190, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:16 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:16 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:16 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:16 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 190, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:16 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:16 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:16 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:16 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 190, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:16 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:16 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:16 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:16 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 190, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:16 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:16 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:16 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:16 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 190, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:16 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:16 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:16 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:16 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 190, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:16 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:16 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:16 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:16 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 190, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:16 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:16 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:16 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:16 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 190, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:16 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:16 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:16 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:16 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 190, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:16 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:16 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:16 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:16 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 190, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:16 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:16 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:16 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:16 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 190, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:16 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:16 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:16 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:16 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:16 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:16 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:16 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:16 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:16 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:19:16 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:19:16 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:16 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:16 [INFO] Gripper_485.py:386 - 移动到位置 0x14
2025-07-22 22:19:16 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x14, 力矩=0xFF
2025-07-22 22:19:16 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:16 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:16 [INFO] Gripper_485.py:386 - 移动到位置 0x6A
2025-07-22 22:19:16 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x6A, 力矩=0xFF
2025-07-22 22:19:16 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:16 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:19:16 [INFO] Gripper_485.py:386 - 移动到位置 0xF2
2025-07-22 22:19:16 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF2, 力矩=0xFF
2025-07-22 22:19:16 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:19:16 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 0, 'velocity': 0, 'force': 191, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
