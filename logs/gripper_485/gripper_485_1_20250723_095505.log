2025-07-23 09:55:05 [INFO] logger_config.py:102 - 日志系统初始化完成，日志文件: /home/<USER>/Spatial_AI/logs/gripper_485/gripper_485_1_20250723_095505.log
2025-07-23 09:55:05 [INFO] Gripper_485.py:53 - 初始化RS485夹爪 - 端口: /dev/ttyUSB1, 节点ID: 1, 波特率: 115200
2025-07-23 09:55:05 [DEBUG] Gripper_485.py:92 - 启动RS485消息接收线程
2025-07-23 09:55:05 [INFO] Gripper_485.py:96 - RS485消息接收线程已启动
2025-07-23 09:55:05 [INFO] Gripper_485.py:53 - 初始化RS485夹爪 - 端口: /dev/ttyUSB2, 节点ID: 1, 波特率: 115200
2025-07-23 09:55:05 [DEBUG] Gripper_485.py:92 - 启动RS485消息接收线程
2025-07-23 09:55:05 [INFO] Gripper_485.py:96 - RS485消息接收线程已启动
2025-07-23 10:09:03 [INFO] Gripper_485.py:386 - 移动到位置 0x23
2025-07-23 10:09:03 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x23, 力矩=0xFF
2025-07-23 10:09:03 [INFO] Gripper_485.py:386 - 移动到位置 0x88
2025-07-23 10:09:03 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x88, 力矩=0xFF
2025-07-23 10:09:03 [INFO] Gripper_485.py:386 - 移动到位置 0xE6
2025-07-23 10:09:03 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xE6, 力矩=0xFF
2025-07-23 10:09:04 [INFO] Gripper_485.py:386 - 移动到位置 0x92
2025-07-23 10:09:04 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x92, 力矩=0xFF
2025-07-23 10:09:04 [INFO] Gripper_485.py:386 - 移动到位置 0x04
2025-07-23 10:09:04 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x04, 力矩=0xFF
2025-07-23 10:09:04 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:09:04 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:09:04 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:09:04 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:09:04 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:09:04 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:09:04 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:09:04 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:09:04 [INFO] Gripper_485.py:386 - 移动到位置 0xD1
2025-07-23 10:09:04 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xD1, 力矩=0xFF
2025-07-23 10:09:04 [INFO] Gripper_485.py:386 - 移动到位置 0x91
2025-07-23 10:09:04 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x91, 力矩=0xFF
2025-07-23 10:09:04 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:09:04 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:09:04 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:09:04 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:09:04 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:09:04 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:09:05 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:09:05 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:09:05 [INFO] Gripper_485.py:386 - 移动到位置 0x2B
2025-07-23 10:09:05 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x2B, 力矩=0xFF
2025-07-23 10:09:05 [INFO] Gripper_485.py:386 - 移动到位置 0xC9
2025-07-23 10:09:05 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xC9, 力矩=0xFF
2025-07-23 10:09:05 [INFO] Gripper_485.py:386 - 移动到位置 0xEF
2025-07-23 10:09:05 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xEF, 力矩=0xFF
2025-07-23 10:09:05 [INFO] Gripper_485.py:386 - 移动到位置 0xC9
2025-07-23 10:09:05 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xC9, 力矩=0xFF
2025-07-23 10:09:05 [INFO] Gripper_485.py:386 - 移动到位置 0x85
2025-07-23 10:09:05 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x85, 力矩=0xFF
2025-07-23 10:09:05 [INFO] Gripper_485.py:386 - 移动到位置 0x09
2025-07-23 10:09:05 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x09, 力矩=0xFF
2025-07-23 10:09:05 [INFO] Gripper_485.py:386 - 移动到位置 0x2E
2025-07-23 10:09:05 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x2E, 力矩=0xFF
2025-07-23 10:09:05 [INFO] Gripper_485.py:386 - 移动到位置 0x8A
2025-07-23 10:09:05 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x8A, 力矩=0xFF
2025-07-23 10:09:05 [INFO] Gripper_485.py:386 - 移动到位置 0xDF
2025-07-23 10:09:05 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xDF, 力矩=0xFF
2025-07-23 10:09:06 [INFO] Gripper_485.py:386 - 移动到位置 0xEE
2025-07-23 10:09:06 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xEE, 力矩=0xFF
2025-07-23 10:09:06 [INFO] Gripper_485.py:386 - 移动到位置 0xC5
2025-07-23 10:09:06 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xC5, 力矩=0xFF
2025-07-23 10:09:06 [INFO] Gripper_485.py:386 - 移动到位置 0x83
2025-07-23 10:09:06 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x83, 力矩=0xFF
2025-07-23 10:09:06 [INFO] Gripper_485.py:386 - 移动到位置 0x27
2025-07-23 10:09:06 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x27, 力矩=0xFF
2025-07-23 10:09:06 [INFO] Gripper_485.py:386 - 移动到位置 0x3B
2025-07-23 10:09:06 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x3B, 力矩=0xFF
2025-07-23 10:09:06 [INFO] Gripper_485.py:386 - 移动到位置 0xB9
2025-07-23 10:09:06 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xB9, 力矩=0xFF
2025-07-23 10:09:06 [INFO] Gripper_485.py:386 - 移动到位置 0xEB
2025-07-23 10:09:06 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xEB, 力矩=0xFF
2025-07-23 10:09:06 [INFO] Gripper_485.py:386 - 移动到位置 0xEE
2025-07-23 10:09:06 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xEE, 力矩=0xFF
2025-07-23 10:09:06 [INFO] Gripper_485.py:386 - 移动到位置 0xF3
2025-07-23 10:09:06 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF3, 力矩=0xFF
2025-07-23 10:09:06 [INFO] Gripper_485.py:386 - 移动到位置 0xFA
2025-07-23 10:09:06 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFA, 力矩=0xFF
2025-07-23 10:09:06 [INFO] Gripper_485.py:386 - 移动到位置 0x62
2025-07-23 10:09:06 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x62, 力矩=0xFF
2025-07-23 10:09:06 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:09:06 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:09:06 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:09:06 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:09:06 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:09:06 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:09:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:09:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:09:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:09:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:09:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:09:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:09:07 [INFO] Gripper_485.py:386 - 移动到位置 0x9E
2025-07-23 10:09:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x9E, 力矩=0xFF
2025-07-23 10:09:07 [INFO] Gripper_485.py:386 - 移动到位置 0xE9
2025-07-23 10:09:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xE9, 力矩=0xFF
2025-07-23 10:09:07 [INFO] Gripper_485.py:386 - 移动到位置 0xEF
2025-07-23 10:09:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xEF, 力矩=0xFF
2025-07-23 10:09:07 [INFO] Gripper_485.py:386 - 移动到位置 0x9A
2025-07-23 10:09:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x9A, 力矩=0xFF
2025-07-23 10:09:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:09:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:09:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:09:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:09:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:09:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:09:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:09:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:09:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:09:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:09:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:09:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:09:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:09:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:09:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:09:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:09:07 [INFO] Gripper_485.py:386 - 移动到位置 0x01
2025-07-23 10:09:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x01, 力矩=0xFF
2025-07-23 10:09:07 [INFO] Gripper_485.py:386 - 移动到位置 0x7B
2025-07-23 10:09:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x7B, 力矩=0xFF
2025-07-23 10:09:07 [INFO] Gripper_485.py:386 - 移动到位置 0xD6
2025-07-23 10:09:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xD6, 力矩=0xFF
2025-07-23 10:10:16 [INFO] Gripper_485.py:386 - 移动到位置 0xF3
2025-07-23 10:10:16 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF3, 力矩=0xFF
2025-07-23 10:10:16 [INFO] Gripper_485.py:386 - 移动到位置 0xEA
2025-07-23 10:10:16 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xEA, 力矩=0xFF
2025-07-23 10:10:16 [INFO] Gripper_485.py:386 - 移动到位置 0xE5
2025-07-23 10:10:16 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xE5, 力矩=0xFF
2025-07-23 10:10:16 [INFO] Gripper_485.py:386 - 移动到位置 0xE9
2025-07-23 10:10:16 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xE9, 力矩=0xFF
2025-07-23 10:10:16 [INFO] Gripper_485.py:386 - 移动到位置 0xEE
2025-07-23 10:10:16 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xEE, 力矩=0xFF
2025-07-23 10:10:16 [INFO] Gripper_485.py:386 - 移动到位置 0xE2
2025-07-23 10:10:16 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xE2, 力矩=0xFF
2025-07-23 10:10:16 [INFO] Gripper_485.py:386 - 移动到位置 0xDA
2025-07-23 10:10:16 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xDA, 力矩=0xFF
2025-07-23 10:10:16 [INFO] Gripper_485.py:386 - 移动到位置 0xD7
2025-07-23 10:10:16 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xD7, 力矩=0xFF
2025-07-23 10:10:16 [INFO] Gripper_485.py:386 - 移动到位置 0xD6
2025-07-23 10:10:16 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xD6, 力矩=0xFF
2025-07-23 10:10:16 [INFO] Gripper_485.py:386 - 移动到位置 0xD6
2025-07-23 10:10:16 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xD6, 力矩=0xFF
2025-07-23 10:10:16 [INFO] Gripper_485.py:386 - 移动到位置 0x95
2025-07-23 10:10:16 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x95, 力矩=0xFF
2025-07-23 10:10:16 [INFO] Gripper_485.py:386 - 移动到位置 0x95
2025-07-23 10:10:16 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x95, 力矩=0xFF
2025-07-23 10:10:16 [INFO] Gripper_485.py:386 - 移动到位置 0x93
2025-07-23 10:10:16 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x93, 力矩=0xFF
2025-07-23 10:10:16 [INFO] Gripper_485.py:386 - 移动到位置 0xB2
2025-07-23 10:10:16 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xB2, 力矩=0xFF
2025-07-23 10:10:16 [INFO] Gripper_485.py:386 - 移动到位置 0xC1
2025-07-23 10:10:16 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xC1, 力矩=0xFF
2025-07-23 10:10:16 [INFO] Gripper_485.py:386 - 移动到位置 0xC1
2025-07-23 10:10:16 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xC1, 力矩=0xFF
2025-07-23 10:10:16 [INFO] Gripper_485.py:386 - 移动到位置 0xC3
2025-07-23 10:10:16 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xC3, 力矩=0xFF
2025-07-23 10:10:16 [INFO] Gripper_485.py:386 - 移动到位置 0xDC
2025-07-23 10:10:16 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xDC, 力矩=0xFF
2025-07-23 10:10:16 [INFO] Gripper_485.py:386 - 移动到位置 0xDC
2025-07-23 10:10:16 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xDC, 力矩=0xFF
2025-07-23 10:10:26 [INFO] Gripper_485.py:386 - 移动到位置 0xFC
2025-07-23 10:10:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFC, 力矩=0xFF
2025-07-23 10:10:26 [INFO] Gripper_485.py:386 - 移动到位置 0xFC
2025-07-23 10:10:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFC, 力矩=0xFF
2025-07-23 10:10:26 [INFO] Gripper_485.py:386 - 移动到位置 0xF7
2025-07-23 10:10:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF7, 力矩=0xFF
2025-07-23 10:10:26 [INFO] Gripper_485.py:386 - 移动到位置 0xAB
2025-07-23 10:10:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xAB, 力矩=0xFF
2025-07-23 10:10:26 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:10:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:10:26 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:10:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:10:26 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:10:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:10:26 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:10:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:10:26 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:10:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:10:26 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:10:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:10:26 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:10:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:10:26 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:10:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:10:26 [INFO] Gripper_485.py:386 - 移动到位置 0x49
2025-07-23 10:10:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x49, 力矩=0xFF
2025-07-23 10:10:26 [INFO] Gripper_485.py:386 - 移动到位置 0xBA
2025-07-23 10:10:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xBA, 力矩=0xFF
2025-07-23 10:10:27 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:10:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:10:27 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:10:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:10:27 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:10:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:10:27 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:10:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:10:27 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:10:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:10:27 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:10:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:10:27 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:10:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:10:27 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:10:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:10:27 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:10:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:10:27 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:10:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:10:27 [INFO] Gripper_485.py:386 - 移动到位置 0x7C
2025-07-23 10:10:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x7C, 力矩=0xFF
2025-07-23 10:10:27 [INFO] Gripper_485.py:386 - 移动到位置 0xE9
2025-07-23 10:10:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xE9, 力矩=0xFF
2025-07-23 10:10:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFC
2025-07-23 10:10:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFC, 力矩=0xFF
2025-07-23 10:10:41 [INFO] Gripper_485.py:386 - 移动到位置 0xF7
2025-07-23 10:10:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF7, 力矩=0xFF
2025-07-23 10:10:41 [INFO] Gripper_485.py:386 - 移动到位置 0xF1
2025-07-23 10:10:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF1, 力矩=0xFF
2025-07-23 10:10:41 [INFO] Gripper_485.py:386 - 移动到位置 0xD2
2025-07-23 10:10:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xD2, 力矩=0xFF
2025-07-23 10:10:41 [INFO] Gripper_485.py:386 - 移动到位置 0x9E
2025-07-23 10:10:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x9E, 力矩=0xFF
2025-07-23 10:10:41 [INFO] Gripper_485.py:386 - 移动到位置 0x9E
2025-07-23 10:10:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x9E, 力矩=0xFF
2025-07-23 10:10:41 [INFO] Gripper_485.py:386 - 移动到位置 0x87
2025-07-23 10:10:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x87, 力矩=0xFF
2025-07-23 10:10:41 [INFO] Gripper_485.py:386 - 移动到位置 0x80
2025-07-23 10:10:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x80, 力矩=0xFF
2025-07-23 10:10:41 [INFO] Gripper_485.py:386 - 移动到位置 0x80
2025-07-23 10:10:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x80, 力矩=0xFF
2025-07-23 10:10:41 [INFO] Gripper_485.py:386 - 移动到位置 0x85
2025-07-23 10:10:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x85, 力矩=0xFF
2025-07-23 10:10:41 [INFO] Gripper_485.py:386 - 移动到位置 0x84
2025-07-23 10:10:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x84, 力矩=0xFF
2025-07-23 10:10:41 [INFO] Gripper_485.py:386 - 移动到位置 0x84
2025-07-23 10:10:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x84, 力矩=0xFF
2025-07-23 10:10:41 [INFO] Gripper_485.py:386 - 移动到位置 0x5F
2025-07-23 10:10:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x5F, 力矩=0xFF
2025-07-23 10:10:41 [INFO] Gripper_485.py:386 - 移动到位置 0x14
2025-07-23 10:10:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x14, 力矩=0xFF
2025-07-23 10:10:41 [INFO] Gripper_485.py:386 - 移动到位置 0x01
2025-07-23 10:10:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x01, 力矩=0xFF
2025-07-23 10:10:41 [INFO] Gripper_485.py:386 - 移动到位置 0x05
2025-07-23 10:10:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x05, 力矩=0xFF
2025-07-23 10:10:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:10:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:10:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:10:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:10:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:10:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:10:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:10:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:10:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:10:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:10:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:10:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:10:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:10:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:10:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:10:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:10:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:10:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:10:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:10:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:10:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:10:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:10:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:10:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:10:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:10:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:10:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:10:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:10:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:10:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:10:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:10:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:10:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:10:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:10:41 [INFO] Gripper_485.py:386 - 移动到位置 0x31
2025-07-23 10:10:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x31, 力矩=0xFF
2025-07-23 10:10:41 [INFO] Gripper_485.py:386 - 移动到位置 0x31
2025-07-23 10:10:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x31, 力矩=0xFF
2025-07-23 10:10:41 [INFO] Gripper_485.py:386 - 移动到位置 0x94
2025-07-23 10:10:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x94, 力矩=0xFF
2025-07-23 10:10:41 [INFO] Gripper_485.py:386 - 移动到位置 0xF7
2025-07-23 10:10:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF7, 力矩=0xFF
2025-07-23 10:10:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFC
2025-07-23 10:10:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFC, 力矩=0xFF
2025-07-23 10:10:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFB
2025-07-23 10:10:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFB, 力矩=0xFF
2025-07-23 10:10:45 [INFO] Gripper_485.py:386 - 移动到位置 0xF8
2025-07-23 10:10:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF8, 力矩=0xFF
2025-07-23 10:10:45 [INFO] Gripper_485.py:386 - 移动到位置 0xC0
2025-07-23 10:10:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xC0, 力矩=0xFF
2025-07-23 10:10:45 [INFO] Gripper_485.py:386 - 移动到位置 0xC0
2025-07-23 10:10:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xC0, 力矩=0xFF
2025-07-23 10:10:45 [INFO] Gripper_485.py:386 - 移动到位置 0x94
2025-07-23 10:10:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x94, 力矩=0xFF
2025-07-23 10:10:45 [INFO] Gripper_485.py:386 - 移动到位置 0x1C
2025-07-23 10:10:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x1C, 力矩=0xFF
2025-07-23 10:10:45 [INFO] Gripper_485.py:386 - 移动到位置 0x1C
2025-07-23 10:10:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x1C, 力矩=0xFF
2025-07-23 10:10:45 [INFO] Gripper_485.py:386 - 移动到位置 0x0B
2025-07-23 10:10:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0B, 力矩=0xFF
2025-07-23 10:10:45 [INFO] Gripper_485.py:386 - 移动到位置 0x0A
2025-07-23 10:10:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0A, 力矩=0xFF
2025-07-23 10:10:45 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:10:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:10:45 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:10:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:10:45 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:10:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:10:45 [INFO] Gripper_485.py:386 - 移动到位置 0x13
2025-07-23 10:10:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x13, 力矩=0xFF
2025-07-23 10:10:45 [INFO] Gripper_485.py:386 - 移动到位置 0x5A
2025-07-23 10:10:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x5A, 力矩=0xFF
2025-07-23 10:10:45 [INFO] Gripper_485.py:386 - 移动到位置 0xE9
2025-07-23 10:10:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xE9, 力矩=0xFF
2025-07-23 10:10:46 [INFO] Gripper_485.py:386 - 移动到位置 0xF4
2025-07-23 10:10:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF4, 力矩=0xFF
2025-07-23 10:10:46 [INFO] Gripper_485.py:386 - 移动到位置 0xE5
2025-07-23 10:10:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xE5, 力矩=0xFF
2025-07-23 10:10:46 [INFO] Gripper_485.py:386 - 移动到位置 0xB9
2025-07-23 10:10:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xB9, 力矩=0xFF
2025-07-23 10:10:46 [INFO] Gripper_485.py:386 - 移动到位置 0x63
2025-07-23 10:10:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x63, 力矩=0xFF
2025-07-23 10:10:46 [INFO] Gripper_485.py:386 - 移动到位置 0x2B
2025-07-23 10:10:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x2B, 力矩=0xFF
2025-07-23 10:10:47 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:10:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:10:47 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:10:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:10:47 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:10:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:10:47 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:10:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:10:47 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:10:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:10:47 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:10:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:10:47 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:10:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:10:47 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:10:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:10:47 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:10:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:10:47 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:10:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:10:47 [INFO] Gripper_485.py:386 - 移动到位置 0x58
2025-07-23 10:10:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x58, 力矩=0xFF
2025-07-23 10:10:47 [INFO] Gripper_485.py:386 - 移动到位置 0xAB
2025-07-23 10:10:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xAB, 力矩=0xFF
2025-07-23 10:10:47 [INFO] Gripper_485.py:386 - 移动到位置 0xDB
2025-07-23 10:10:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xDB, 力矩=0xFF
2025-07-23 10:10:47 [INFO] Gripper_485.py:386 - 移动到位置 0xF7
2025-07-23 10:10:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF7, 力矩=0xFF
2025-07-23 10:10:48 [INFO] Gripper_485.py:386 - 移动到位置 0xD5
2025-07-23 10:10:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xD5, 力矩=0xFF
2025-07-23 10:10:48 [INFO] Gripper_485.py:386 - 移动到位置 0xD5
2025-07-23 10:10:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xD5, 力矩=0xFF
2025-07-23 10:10:48 [INFO] Gripper_485.py:386 - 移动到位置 0x94
2025-07-23 10:10:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x94, 力矩=0xFF
2025-07-23 10:10:48 [INFO] Gripper_485.py:386 - 移动到位置 0x6A
2025-07-23 10:10:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x6A, 力矩=0xFF
2025-07-23 10:10:48 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:10:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:10:48 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:10:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:10:48 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:10:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:10:48 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:10:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:10:48 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:10:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:10:48 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:10:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:10:48 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:10:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:10:48 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:10:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:10:48 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:10:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:10:48 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:10:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:10:48 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:10:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:10:48 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:10:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:10:49 [INFO] Gripper_485.py:386 - 移动到位置 0x76
2025-07-23 10:10:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x76, 力矩=0xFF
2025-07-23 10:10:49 [INFO] Gripper_485.py:386 - 移动到位置 0xBC
2025-07-23 10:10:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xBC, 力矩=0xFF
2025-07-23 10:10:50 [INFO] Gripper_485.py:386 - 移动到位置 0xFC
2025-07-23 10:10:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFC, 力矩=0xFF
2025-07-23 10:10:50 [INFO] Gripper_485.py:386 - 移动到位置 0xF4
2025-07-23 10:10:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF4, 力矩=0xFF
2025-07-23 10:10:50 [INFO] Gripper_485.py:386 - 移动到位置 0xC4
2025-07-23 10:10:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xC4, 力矩=0xFF
2025-07-23 10:10:50 [INFO] Gripper_485.py:386 - 移动到位置 0x71
2025-07-23 10:10:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x71, 力矩=0xFF
2025-07-23 10:10:50 [INFO] Gripper_485.py:386 - 移动到位置 0x34
2025-07-23 10:10:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x34, 力矩=0xFF
2025-07-23 10:10:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:10:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:10:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:10:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:10:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:10:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:10:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:10:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:10:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:10:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:10:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:10:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:10:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:10:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:10:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:10:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:10:51 [INFO] Gripper_485.py:386 - 移动到位置 0x19
2025-07-23 10:10:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x19, 力矩=0xFF
2025-07-23 10:10:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:10:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:10:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:10:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:10:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:10:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:10:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:10:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:10:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:10:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:10:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:10:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:10:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:10:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:10:51 [INFO] Gripper_485.py:386 - 移动到位置 0x0A
2025-07-23 10:10:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0A, 力矩=0xFF
2025-07-23 10:10:51 [INFO] Gripper_485.py:386 - 移动到位置 0x4F
2025-07-23 10:10:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x4F, 力矩=0xFF
2025-07-23 10:10:51 [INFO] Gripper_485.py:386 - 移动到位置 0xD7
2025-07-23 10:10:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xD7, 力矩=0xFF
2025-07-23 10:10:51 [INFO] Gripper_485.py:386 - 移动到位置 0xF1
2025-07-23 10:10:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF1, 力矩=0xFF
2025-07-23 10:13:50 [INFO] Gripper_485.py:386 - 移动到位置 0x9A
2025-07-23 10:13:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x9A, 力矩=0xFF
2025-07-23 10:13:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:13:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:13:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:13:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:13:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:13:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:13:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:13:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:13:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:13:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:13:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:13:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:13:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:13:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:13:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:13:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:13:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:13:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:13:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:13:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:13:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:13:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:13:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:13:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:13:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:13:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:13:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:13:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:13:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:13:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:13:50 [INFO] Gripper_485.py:386 - 移动到位置 0xDA
2025-07-23 10:13:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xDA, 力矩=0xFF
2025-07-23 10:13:51 [INFO] Gripper_485.py:386 - 移动到位置 0xF8
2025-07-23 10:13:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF8, 力矩=0xFF
2025-07-23 10:13:51 [INFO] Gripper_485.py:386 - 移动到位置 0xE7
2025-07-23 10:13:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xE7, 力矩=0xFF
2025-07-23 10:13:51 [INFO] Gripper_485.py:386 - 移动到位置 0xCA
2025-07-23 10:13:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xCA, 力矩=0xFF
2025-07-23 10:13:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:13:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:13:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:13:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:13:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:13:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:13:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:13:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:13:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:13:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:13:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:13:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:13:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:13:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:13:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:13:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:13:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:13:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:13:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:13:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:13:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:13:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:13:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:13:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:13:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:13:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:13:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:13:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:13:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:13:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:13:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:13:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:13:51 [INFO] Gripper_485.py:386 - 移动到位置 0x0A
2025-07-23 10:13:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0A, 力矩=0xFF
2025-07-23 10:13:51 [INFO] Gripper_485.py:386 - 移动到位置 0x33
2025-07-23 10:13:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x33, 力矩=0xFF
2025-07-23 10:13:51 [INFO] Gripper_485.py:386 - 移动到位置 0xCE
2025-07-23 10:13:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xCE, 力矩=0xFF
2025-07-23 10:13:52 [INFO] Gripper_485.py:386 - 移动到位置 0xAA
2025-07-23 10:13:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xAA, 力矩=0xFF
2025-07-23 10:13:53 [INFO] Gripper_485.py:386 - 移动到位置 0xAA
2025-07-23 10:13:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xAA, 力矩=0xFF
2025-07-23 10:13:53 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:13:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:13:53 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:13:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:13:53 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:13:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:13:53 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:13:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:13:53 [INFO] Gripper_485.py:386 - 移动到位置 0x61
2025-07-23 10:13:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x61, 力矩=0xFF
2025-07-23 10:13:53 [INFO] Gripper_485.py:386 - 移动到位置 0xF5
2025-07-23 10:13:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF5, 力矩=0xFF
2025-07-23 10:13:53 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:13:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:13:53 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:13:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:13:53 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:13:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:13:53 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:13:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:13:53 [INFO] Gripper_485.py:386 - 移动到位置 0xDF
2025-07-23 10:13:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xDF, 力矩=0xFF
2025-07-23 10:13:53 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:13:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:13:53 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:13:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:13:53 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:13:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:13:53 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:13:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:13:53 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:13:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:13:53 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:13:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:13:53 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:13:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:13:53 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:13:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:13:53 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:13:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:13:53 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:13:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:13:53 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:13:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:13:53 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:13:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:13:53 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:13:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:13:53 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:13:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:13:53 [INFO] Gripper_485.py:386 - 移动到位置 0x04
2025-07-23 10:13:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x04, 力矩=0xFF
2025-07-23 10:13:53 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:13:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:13:53 [INFO] Gripper_485.py:386 - 移动到位置 0xBA
2025-07-23 10:13:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xBA, 力矩=0xFF
2025-07-23 10:13:53 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:13:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:13:53 [INFO] Gripper_485.py:386 - 移动到位置 0xF0
2025-07-23 10:13:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF0, 力矩=0xFF
2025-07-23 10:13:53 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:13:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:13:53 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:13:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:13:53 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:13:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:13:53 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:13:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:13:53 [INFO] Gripper_485.py:386 - 移动到位置 0x6D
2025-07-23 10:13:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x6D, 力矩=0xFF
2025-07-23 10:13:54 [INFO] Gripper_485.py:386 - 移动到位置 0xE2
2025-07-23 10:13:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xE2, 力矩=0xFF
2025-07-23 10:13:54 [INFO] Gripper_485.py:386 - 移动到位置 0xEC
2025-07-23 10:13:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xEC, 力矩=0xFF
2025-07-23 10:13:54 [INFO] Gripper_485.py:386 - 移动到位置 0xEC
2025-07-23 10:13:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xEC, 力矩=0xFF
2025-07-23 10:13:54 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:13:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:13:54 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:13:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:13:54 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:13:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:13:54 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:13:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:13:54 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:13:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:13:54 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:13:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:13:54 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:13:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:13:54 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:13:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:13:54 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:13:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:13:54 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:13:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:13:54 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:13:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:13:54 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:13:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:13:54 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:13:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:13:54 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:13:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:13:55 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:13:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:13:55 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:13:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:13:55 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:13:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:13:55 [INFO] Gripper_485.py:386 - 移动到位置 0x0F
2025-07-23 10:13:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0F, 力矩=0xFF
2025-07-23 10:13:55 [INFO] Gripper_485.py:386 - 移动到位置 0x86
2025-07-23 10:13:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x86, 力矩=0xFF
2025-07-23 10:13:55 [INFO] Gripper_485.py:386 - 移动到位置 0xDC
2025-07-23 10:13:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xDC, 力矩=0xFF
