2025-07-18 10:22:45 [INFO] logger_config.py:102 - 日志系统初始化完成，日志文件: /home/<USER>/Spatial_AI/logs/gripper_485/gripper_485_1_20250718_102245.log
2025-07-18 10:22:45 [INFO] Gripper_485.py:53 - 初始化RS485夹爪 - 端口: /dev/ttyUSB1, 节点ID: 1, 波特率: 115200
2025-07-18 10:22:45 [DEBUG] Gripper_485.py:92 - 启动RS485消息接收线程
2025-07-18 10:22:45 [INFO] Gripper_485.py:96 - RS485消息接收线程已启动
2025-07-18 10:22:45 [INFO] Gripper_485.py:53 - 初始化RS485夹爪 - 端口: /dev/ttyUSB2, 节点ID: 1, 波特率: 115200
2025-07-18 10:22:45 [DEBUG] Gripper_485.py:92 - 启动RS485消息接收线程
2025-07-18 10:22:45 [INFO] Gripper_485.py:96 - RS485消息接收线程已启动
2025-07-18 10:22:45 [INFO] Gripper_485.py:449 - 正在关闭RS485夹爪连接...
2025-07-18 10:22:45 [DEBUG] Gripper_485.py:454 - 接收线程已停止
2025-07-18 10:22:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-18 10:22:45 [INFO] Gripper_485.py:458 - 串口连接已关闭
2025-07-18 10:22:45 [INFO] Gripper_485.py:462 - RS485夹爪连接已关闭
2025-07-18 10:22:45 [INFO] Gripper_485.py:449 - 正在关闭RS485夹爪连接...
2025-07-18 10:22:45 [DEBUG] Gripper_485.py:454 - 接收线程已停止
2025-07-18 10:22:45 [INFO] Gripper_485.py:458 - 串口连接已关闭
2025-07-18 10:22:45 [INFO] Gripper_485.py:462 - RS485夹爪连接已关闭
2025-07-18 10:22:47 [INFO] Gripper_485.py:372 - 执行关闭夹爪指令
2025-07-18 10:22:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
