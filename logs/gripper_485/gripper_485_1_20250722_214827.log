2025-07-22 21:48:27 [INFO] logger_config.py:102 - 日志系统初始化完成，日志文件: /home/<USER>/Spatial_AI/logs/gripper_485/gripper_485_1_20250722_214827.log
2025-07-22 21:48:27 [INFO] Gripper_485.py:53 - 初始化RS485夹爪 - 端口: /dev/ttyUSB1, 节点ID: 1, 波特率: 115200
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:92 - 启动RS485消息接收线程
2025-07-22 21:48:27 [INFO] Gripper_485.py:96 - RS485消息接收线程已启动
2025-07-22 21:48:27 [INFO] Gripper_485.py:53 - 初始化RS485夹爪 - 端口: /dev/ttyUSB2, 节点ID: 1, 波特率: 115200
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:92 - 启动RS485消息接收线程
2025-07-22 21:48:27 [INFO] Gripper_485.py:96 - RS485消息接收线程已启动
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xBC
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xBE
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xBE
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xBF
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xC0
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xC0
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xC0
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xC0
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xC0
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xBF
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xBE
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xBD
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xBC
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xBB
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xBA
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xB8
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xB6
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xB4
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xB1
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xAE
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xAB
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xA8
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xA4
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xA0
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x9C
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x98
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x95
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x92
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x8F
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x8C
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x8B
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x89
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x87
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x85
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x84
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x82
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x81
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
