2025-07-22 16:20:48 [INFO] logger_config.py:102 - 日志系统初始化完成，日志文件: /home/<USER>/Spatial_AI/logs/gripper_485/gripper_485_1_20250722_162048.log
2025-07-22 16:20:48 [INFO] Gripper_485.py:53 - 初始化RS485夹爪 - 端口: /dev/ttyUSB1, 节点ID: 1, 波特率: 115200
2025-07-22 16:20:48 [DEBUG] Gripper_485.py:92 - 启动RS485消息接收线程
2025-07-22 16:20:48 [INFO] Gripper_485.py:96 - RS485消息接收线程已启动
2025-07-22 16:20:48 [INFO] Gripper_485.py:53 - 初始化RS485夹爪 - 端口: /dev/ttyUSB2, 节点ID: 1, 波特率: 115200
2025-07-22 16:20:48 [DEBUG] Gripper_485.py:92 - 启动RS485消息接收线程
2025-07-22 16:20:48 [INFO] Gripper_485.py:96 - RS485消息接收线程已启动
2025-07-22 16:20:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:20:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:20:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 16:20:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x01
2025-07-22 16:20:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x03
2025-07-22 16:20:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x07
2025-07-22 16:20:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x0A
2025-07-22 16:20:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x0D
2025-07-22 16:20:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x12
2025-07-22 16:20:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x17
2025-07-22 16:20:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x1C
2025-07-22 16:20:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x22
2025-07-22 16:20:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x29
2025-07-22 16:20:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x30
2025-07-22 16:20:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x37
2025-07-22 16:20:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x3D
2025-07-22 16:20:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x44
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x4B
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x52
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x59
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x60
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x65
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x6B
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x6F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x73
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x77
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7A
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7C
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7E
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x6F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x6C
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x69
2025-07-22 16:20:50 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x69, 误差=5
2025-07-22 16:20:50 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x68
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:50 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x68, 误差=4
2025-07-22 16:20:50 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x67
2025-07-22 16:20:50 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x67, 误差=3
2025-07-22 16:20:50 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x66
2025-07-22 16:20:50 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x66, 误差=2
2025-07-22 16:20:50 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x66
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:50 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x66, 误差=2
2025-07-22 16:20:50 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x65
2025-07-22 16:20:50 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x65, 误差=1
2025-07-22 16:20:50 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x65
2025-07-22 16:20:50 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x65, 误差=1
2025-07-22 16:20:50 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x64
2025-07-22 16:20:50 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x64, 误差=0
2025-07-22 16:20:50 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x64
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x64, 误差=0
2025-07-22 16:20:50 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x64
2025-07-22 16:20:50 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x64, 误差=0
2025-07-22 16:20:50 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x64
2025-07-22 16:20:50 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x64, 误差=0
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x64
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x63
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x63
2025-07-22 16:20:50 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x63
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x63
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x63
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x63
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:51 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x63
2025-07-22 16:20:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x63
2025-07-22 16:20:51 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x63
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x63
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x63
2025-07-22 16:20:51 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x63
2025-07-22 16:20:51 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:51 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x63
2025-07-22 16:20:51 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x63
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x63
2025-07-22 16:20:51 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:51 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:51 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:51 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:20:51 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:51 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:51 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:51 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:20:51 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:20:51 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:51 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:51 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:51 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:51 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:51 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:51 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:51 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:51 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:51 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:51 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:51 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:51 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:51 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:51 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:51 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:51 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:51 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:51 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:51 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:51 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:51 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:51 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:51 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:51 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:51 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:51 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:51 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:51 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:51 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:51 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:51 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:51 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:52 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:52 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:52 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:52 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:52 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:20:52 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:52 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:52 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:52 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:52 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:52 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:52 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:52 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:52 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:52 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:52 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:52 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:52 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:52 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:52 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:52 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:20:52 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:52 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:52 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:52 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:52 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:52 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:52 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:52 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:52 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:52 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:52 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:52 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:52 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:52 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:52 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:52 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:52 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:52 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:52 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:52 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:52 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:52 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:52 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:52 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:52 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:52 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:53 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:53 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:53 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:53 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:53 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:53 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:20:53 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:20:53 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:20:53 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:20:53 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:20:53 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:20:53 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:20:53 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:53 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:53 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:53 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:53 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:53 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:53 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:53 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:53 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:53 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:53 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:20:53 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:53 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:53 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:20:53 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:53 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:53 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:53 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:53 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:53 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:53 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:53 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:53 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:53 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:53 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:53 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:53 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:54 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:54 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:54 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:54 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:54 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:54 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:54 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:54 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:54 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:54 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:54 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:54 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:54 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:54 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:54 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:54 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:54 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:20:54 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:54 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:54 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:20:54 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:54 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:54 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:54 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:54 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:54 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:54 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:54 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:54 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:54 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:54 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:54 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:54 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:54 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:20:54 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:20:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:54 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:54 [INFO] Gripper_485.py:386 - 移动到位置 0x62
2025-07-22 16:20:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x62, 力矩=0xFF
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:20:54 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x62, 实际=0x63, 误差=1
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:54 [INFO] Gripper_485.py:386 - 移动到位置 0x51
2025-07-22 16:20:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x51, 力矩=0xFF
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:55 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x51, 当前=0x63, 误差=18
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:59 [WARNING] Gripper_485.py:255 - 等待回复超时，节点ID: 1
2025-07-22 16:20:59 [WARNING] Gripper_485.py:257 - 最后状态: 目标=0x51, 实际=0x63
2025-07-22 16:20:59 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 16:20:59 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:20:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:04 [WARNING] Gripper_485.py:255 - 等待回复超时，节点ID: 1
2025-07-22 16:21:04 [WARNING] Gripper_485.py:257 - 最后状态: 目标=0x00, 实际=0x63
2025-07-22 16:21:04 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 16:21:04 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:09 [WARNING] Gripper_485.py:255 - 等待回复超时，节点ID: 1
2025-07-22 16:21:09 [WARNING] Gripper_485.py:257 - 最后状态: 目标=0x00, 实际=0x63
2025-07-22 16:21:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 16:21:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:14 [WARNING] Gripper_485.py:255 - 等待回复超时，节点ID: 1
2025-07-22 16:21:14 [WARNING] Gripper_485.py:257 - 最后状态: 目标=0x00, 实际=0x63
2025-07-22 16:21:14 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 16:21:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:19 [WARNING] Gripper_485.py:255 - 等待回复超时，节点ID: 1
2025-07-22 16:21:19 [WARNING] Gripper_485.py:257 - 最后状态: 目标=0x00, 实际=0x63
2025-07-22 16:21:19 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 16:21:19 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:20 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:21 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:22 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:21:23 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:23 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:21:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
