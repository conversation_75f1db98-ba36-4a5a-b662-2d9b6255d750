2025-07-22 15:44:57 [INFO] logger_config.py:102 - 日志系统初始化完成，日志文件: /home/<USER>/Spatial_AI/logs/gripper_485/gripper_485_1_20250722_154457.log
2025-07-22 15:44:57 [INFO] Gripper_485.py:53 - 初始化RS485夹爪 - 端口: /dev/ttyUSB1, 节点ID: 1, 波特率: 115200
2025-07-22 15:44:57 [DEBUG] Gripper_485.py:92 - 启动RS485消息接收线程
2025-07-22 15:44:57 [INFO] Gripper_485.py:96 - RS485消息接收线程已启动
2025-07-22 15:44:57 [INFO] Gripper_485.py:53 - 初始化RS485夹爪 - 端口: /dev/ttyUSB2, 节点ID: 1, 波特率: 115200
2025-07-22 15:44:57 [DEBUG] Gripper_485.py:92 - 启动RS485消息接收线程
2025-07-22 15:44:57 [INFO] Gripper_485.py:96 - RS485消息接收线程已启动
2025-07-22 15:44:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x64
2025-07-22 15:44:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x64
2025-07-22 15:44:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x65
2025-07-22 15:44:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x66
2025-07-22 15:44:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x68
2025-07-22 15:44:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x6B
2025-07-22 15:44:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x6E
2025-07-22 15:44:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x72
2025-07-22 15:44:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x76
2025-07-22 15:44:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x79
2025-07-22 15:44:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7B
2025-07-22 15:44:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7D
2025-07-22 15:44:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 15:44:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 15:44:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 15:44:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 15:44:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 15:44:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 15:44:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 15:44:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 15:44:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-22 15:44:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 15:44:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 15:44:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 15:44:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 15:44:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 15:44:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 15:44:59 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7E
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7D
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7B
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7A
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x78
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x76
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x73
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x71
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x6E
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x6A
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x67
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x63
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x5F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x5B
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x56
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x52
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x4D
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x48
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x44
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x3F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x3A
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x36
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x31
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x2C
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x28
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x23
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x1F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x1B
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x17
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x14
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x10
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x0E
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x0B
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x09
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x06
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x00, 实际=0x06, 误差=6
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x05
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x03
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x02
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x01
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:44:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 15:45:00 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 15:45:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:02 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:05 [WARNING] Gripper_485.py:255 - 等待回复超时，节点ID: 1
2025-07-22 15:45:05 [WARNING] Gripper_485.py:257 - 最后状态: 目标=0x64, 实际=0x00
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:06 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 15:45:06 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 15:45:06 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 15:45:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-22 15:45:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:11 [WARNING] Gripper_485.py:255 - 等待回复超时，节点ID: 1
2025-07-22 15:45:11 [WARNING] Gripper_485.py:257 - 最后状态: 目标=0xFF, 实际=0x00
