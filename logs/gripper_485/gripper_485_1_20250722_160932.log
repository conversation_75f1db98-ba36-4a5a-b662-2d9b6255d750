2025-07-22 16:09:32 [INFO] logger_config.py:102 - 日志系统初始化完成，日志文件: /home/<USER>/Spatial_AI/logs/gripper_485/gripper_485_1_20250722_160932.log
2025-07-22 16:09:32 [INFO] Gripper_485.py:53 - 初始化RS485夹爪 - 端口: /dev/ttyUSB1, 节点ID: 1, 波特率: 115200
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:92 - 启动RS485消息接收线程
2025-07-22 16:09:32 [INFO] Gripper_485.py:96 - RS485消息接收线程已启动
2025-07-22 16:09:32 [INFO] Gripper_485.py:53 - 初始化RS485夹爪 - 端口: /dev/ttyUSB2, 节点ID: 1, 波特率: 115200
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:92 - 启动RS485消息接收线程
2025-07-22 16:09:32 [INFO] Gripper_485.py:96 - RS485消息接收线程已启动
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x64
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x65
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x66
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x68
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x6B
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x6E
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x72
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x76
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x79
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7C
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7D
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:34 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 16:09:34 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x70
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x6B
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x66
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x60
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x5A
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x53
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x4C
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x45
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x3E
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x37
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x31
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x2A
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x23
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x1D
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x17
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x12
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x0E
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x0A
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:34 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x00, 实际=0x0A, 误差=10
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x07
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x04
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x02
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x01
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 16:09:34 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:09:34 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x01
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x02
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x04
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x05
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x07
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x09
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x0C
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x0E
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x11
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x15
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x18
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x1C
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x20
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x24
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x29
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x2D
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x32
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x37
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x3C
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x40
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x45
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x49
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x4C
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x50
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x53
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x56
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x58
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x5A
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x5C
2025-07-22 16:09:35 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x5C, 误差=8
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x5E
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x60
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x61
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x62
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x63
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x63
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x63
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x63
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x63
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:35 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 16:09:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x63
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x63, 误差=99
2025-07-22 16:09:40 [WARNING] Gripper_485.py:255 - 等待回复超时，节点ID: 1
2025-07-22 16:09:40 [WARNING] Gripper_485.py:257 - 最后状态: 目标=0x00, 实际=0x63
2025-07-22 16:09:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:40 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:09:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:09:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:09:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:40 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:09:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
