2025-07-22 19:39:19 [INFO] logger_config.py:102 - 日志系统初始化完成，日志文件: /home/<USER>/Spatial_AI/logs/gripper_485/gripper_485_1_20250722_193919.log
2025-07-22 19:39:19 [INFO] Gripper_485.py:53 - 初始化RS485夹爪 - 端口: /dev/ttyUSB1, 节点ID: 1, 波特率: 115200
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:92 - 启动RS485消息接收线程
2025-07-22 19:39:19 [INFO] Gripper_485.py:96 - RS485消息接收线程已启动
2025-07-22 19:39:19 [INFO] Gripper_485.py:53 - 初始化RS485夹爪 - 端口: /dev/ttyUSB2, 节点ID: 1, 波特率: 115200
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:92 - 启动RS485消息接收线程
2025-07-22 19:39:19 [INFO] Gripper_485.py:96 - RS485消息接收线程已启动
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:23 [INFO] Gripper_485.py:372 - 执行关闭夹爪指令
2025-07-22 19:39:23 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7E
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7D
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7C
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7B
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x79
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x77
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x74
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x71
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x6E
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x6B
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x67
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x63
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x5F
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x5B
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x57
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x52
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x4D
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x48
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x44
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x3F
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x3A
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x35
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x30
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x2B
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x26
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x22
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x1E
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x1A
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x16
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x13
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x10
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x0D
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x0A
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x08
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:24 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x00, 实际=0x08, 误差=8
2025-07-22 19:39:24 [INFO] Gripper_485.py:358 - 执行打开夹爪指令
2025-07-22 19:39:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x06
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:28 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:29 [WARNING] Gripper_485.py:255 - 等待回复超时，节点ID: 1
2025-07-22 19:39:29 [WARNING] Gripper_485.py:257 - 最后状态: 目标=0xFF, 实际=0x06
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:29 [INFO] Gripper_485.py:372 - 执行关闭夹爪指令
2025-07-22 19:39:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:29 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x00, 实际=0x06, 误差=6
2025-07-22 19:39:29 [INFO] Gripper_485.py:358 - 执行打开夹爪指令
2025-07-22 19:39:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:34 [WARNING] Gripper_485.py:255 - 等待回复超时，节点ID: 1
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:34 [WARNING] Gripper_485.py:257 - 最后状态: 目标=0xFF, 实际=0x06
2025-07-22 19:39:34 [INFO] Gripper_485.py:372 - 执行关闭夹爪指令
2025-07-22 19:39:34 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 19:39:34 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x00, 实际=0x06, 误差=6
2025-07-22 19:39:34 [INFO] Gripper_485.py:358 - 执行打开夹爪指令
2025-07-22 19:39:34 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:39 [WARNING] Gripper_485.py:255 - 等待回复超时，节点ID: 1
2025-07-22 19:39:39 [WARNING] Gripper_485.py:257 - 最后状态: 目标=0xFF, 实际=0x06
2025-07-22 19:39:39 [INFO] Gripper_485.py:372 - 执行关闭夹爪指令
2025-07-22 19:39:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:39 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x00, 实际=0x06, 误差=6
2025-07-22 19:39:39 [INFO] Gripper_485.py:358 - 执行打开夹爪指令
2025-07-22 19:39:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:41 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:44 [WARNING] Gripper_485.py:255 - 等待回复超时，节点ID: 1
2025-07-22 19:39:44 [WARNING] Gripper_485.py:257 - 最后状态: 目标=0xFF, 实际=0x06
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:44 [INFO] Gripper_485.py:372 - 执行关闭夹爪指令
2025-07-22 19:39:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 19:39:44 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x00, 实际=0x06, 误差=6
2025-07-22 19:39:44 [INFO] Gripper_485.py:358 - 执行打开夹爪指令
2025-07-22 19:39:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:45 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:46 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:47 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x06, 误差=249
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:49 [WARNING] Gripper_485.py:255 - 等待回复超时，节点ID: 1
2025-07-22 19:39:49 [WARNING] Gripper_485.py:257 - 最后状态: 目标=0xFF, 实际=0x06
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 19:39:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 19:39:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
