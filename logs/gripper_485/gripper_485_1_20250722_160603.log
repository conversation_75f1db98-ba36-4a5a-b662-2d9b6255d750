2025-07-22 16:06:03 [INFO] logger_config.py:102 - 日志系统初始化完成，日志文件: /home/<USER>/Spatial_AI/logs/gripper_485/gripper_485_1_20250722_160603.log
2025-07-22 16:06:03 [INFO] Gripper_485.py:53 - 初始化RS485夹爪 - 端口: /dev/ttyUSB1, 节点ID: 1, 波特率: 115200
2025-07-22 16:06:03 [DEBUG] Gripper_485.py:92 - 启动RS485消息接收线程
2025-07-22 16:06:03 [INFO] Gripper_485.py:96 - RS485消息接收线程已启动
2025-07-22 16:06:03 [INFO] Gripper_485.py:53 - 初始化RS485夹爪 - 端口: /dev/ttyUSB2, 节点ID: 1, 波特率: 115200
2025-07-22 16:06:03 [DEBUG] Gripper_485.py:92 - 启动RS485消息接收线程
2025-07-22 16:06:03 [INFO] Gripper_485.py:96 - RS485消息接收线程已启动
2025-07-22 16:06:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:05 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 16:06:05 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7E
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7C
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7B
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x79
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x77
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x75
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x73
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x70
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x6D
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x6A
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x66
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x62
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x5E
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x5A
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x55
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x51
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x4C
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x47
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x43
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x3E
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x39
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x35
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x30
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x2B
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x27
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x22
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x1E
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x1A
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x16
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x13
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x10
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x0D
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x0B
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x08
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x06
2025-07-22 16:06:05 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x00, 实际=0x06, 误差=6
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x05
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x03
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x02
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x01
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:06:06 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:06:06 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:11 [WARNING] Gripper_485.py:255 - 等待回复超时，节点ID: 1
2025-07-22 16:06:11 [WARNING] Gripper_485.py:257 - 最后状态: 目标=0x64, 实际=0x00
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:06:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:06:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
