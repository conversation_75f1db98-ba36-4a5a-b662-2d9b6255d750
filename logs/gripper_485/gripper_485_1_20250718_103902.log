2025-07-18 10:39:02 [INFO] logger_config.py:102 - 日志系统初始化完成，日志文件: /home/<USER>/Spatial_AI/logs/gripper_485/gripper_485_1_20250718_103902.log
2025-07-18 10:39:02 [INFO] Gripper_485.py:53 - 初始化RS485夹爪 - 端口: /dev/ttyUSB1, 节点ID: 1, 波特率: 115200
2025-07-18 10:39:02 [DEBUG] Gripper_485.py:92 - 启动RS485消息接收线程
2025-07-18 10:39:02 [INFO] Gripper_485.py:96 - RS485消息接收线程已启动
2025-07-18 10:39:02 [INFO] Gripper_485.py:53 - 初始化RS485夹爪 - 端口: /dev/ttyUSB2, 节点ID: 1, 波特率: 115200
2025-07-18 10:39:02 [DEBUG] Gripper_485.py:92 - 启动RS485消息接收线程
2025-07-18 10:39:02 [INFO] Gripper_485.py:96 - RS485消息接收线程已启动
2025-07-18 10:39:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-18 10:39:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:39:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:39:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:39:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFD
2025-07-18 10:39:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFD
2025-07-18 10:39:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFC
2025-07-18 10:39:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xFC
2025-07-18 10:39:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFA
2025-07-18 10:39:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xFA
2025-07-18 10:39:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xF7
2025-07-18 10:39:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xF7
2025-07-18 10:39:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xF3
2025-07-18 10:39:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xF3
2025-07-18 10:39:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xEF
2025-07-18 10:39:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xEF
2025-07-18 10:39:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xEB
2025-07-18 10:39:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xEB
2025-07-18 10:39:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xE6
2025-07-18 10:39:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xE5
2025-07-18 10:39:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xE0
2025-07-18 10:39:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xDF
2025-07-18 10:39:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xDA
2025-07-18 10:39:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xD8
2025-07-18 10:39:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xD3
2025-07-18 10:39:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xD1
2025-07-18 10:39:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xCD
2025-07-18 10:39:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xCA
2025-07-18 10:39:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xC6
2025-07-18 10:39:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xC4
2025-07-18 10:39:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xBF
2025-07-18 10:39:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xBD
2025-07-18 10:39:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xB9
2025-07-18 10:39:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xB5
2025-07-18 10:39:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xB2
2025-07-18 10:39:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xAE
2025-07-18 10:39:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xAB
2025-07-18 10:39:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xA7
2025-07-18 10:39:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xA5
2025-07-18 10:39:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xA0
2025-07-18 10:39:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x9E
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x99
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x97
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x93
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x92
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x8F
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x8D
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x8B
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x89
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x87
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x86
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x85
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x84
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x82
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x82
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x81
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [INFO] Gripper_485.py:372 - 执行关闭夹爪指令
2025-07-18 10:39:04 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7E
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7D
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7B
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7A
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x78
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x75
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x73
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x70
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x6D
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x69
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x65
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x61
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x5D
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x59
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x54
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x4F
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x4A
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x46
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x41
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x3C
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x38
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x33
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x2E
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x2A
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x25
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x21
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x1C
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x17
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x13
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x10
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x0E
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x0B
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x09
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x06
2025-07-18 10:39:04 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x00, 实际=0x06, 误差=6
2025-07-18 10:39:04 [INFO] Gripper_485.py:372 - 执行关闭夹爪指令
2025-07-18 10:39:04 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-18 10:39:04 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x05
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x03
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x02
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x01
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7E
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7D
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7B
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x79
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x77
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x75
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x73
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x70
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x6D
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x6A
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x66
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x62
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x5E
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x5A
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x56
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x51
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x4C
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x48
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x43
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x3F
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x3A
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x35
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x31
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x2C
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x28
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x23
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x1F
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x1A
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x16
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x12
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x10
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x0D
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x0A
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x08
2025-07-18 10:39:05 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x00, 实际=0x08, 误差=8
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x06
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x04
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x03
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x02
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x01
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [INFO] Gripper_485.py:358 - 执行打开夹爪指令
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:11 [WARNING] Gripper_485.py:255 - 等待回复超时，节点ID: 1
2025-07-18 10:39:11 [WARNING] Gripper_485.py:257 - 最后状态: 目标=0xFF, 实际=0x00
2025-07-18 10:39:11 [INFO] Gripper_485.py:358 - 执行打开夹爪指令
2025-07-18 10:39:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-18 10:39:11 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-18 10:39:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:39:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:39:16 [WARNING] Gripper_485.py:255 - 等待回复超时，节点ID: 1
2025-07-18 10:39:16 [WARNING] Gripper_485.py:257 - 最后状态: 目标=0xFF, 实际=0x00
