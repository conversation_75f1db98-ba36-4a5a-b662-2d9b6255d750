2025-07-22 15:26:38 [INFO] logger_config.py:102 - 日志系统初始化完成，日志文件: /home/<USER>/Spatial_AI/logs/gripper_485/gripper_485_1_20250722_152638.log
2025-07-22 15:26:38 [INFO] Gripper_485.py:53 - 初始化RS485夹爪 - 端口: /dev/ttyUSB1, 节点ID: 1, 波特率: 115200
2025-07-22 15:26:38 [DEBUG] Gripper_485.py:92 - 启动RS485消息接收线程
2025-07-22 15:26:38 [INFO] Gripper_485.py:96 - RS485消息接收线程已启动
2025-07-22 15:26:38 [INFO] Gripper_485.py:53 - 初始化RS485夹爪 - 端口: /dev/ttyUSB2, 节点ID: 1, 波特率: 115200
2025-07-22 15:26:38 [DEBUG] Gripper_485.py:92 - 启动RS485消息接收线程
2025-07-22 15:26:38 [INFO] Gripper_485.py:96 - RS485消息接收线程已启动
2025-07-22 15:26:40 [INFO] Gripper_485.py:372 - 执行关闭夹爪指令
2025-07-22 15:26:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 15:26:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 15:26:45 [WARNING] Gripper_485.py:255 - 等待回复超时，节点ID: 1
2025-07-22 15:26:47 [INFO] Gripper_485.py:358 - 执行打开夹爪指令
2025-07-22 15:26:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 15:26:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 15:26:52 [WARNING] Gripper_485.py:255 - 等待回复超时，节点ID: 1
2025-07-22 15:26:54 [INFO] Gripper_485.py:372 - 执行关闭夹爪指令
2025-07-22 15:26:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 15:26:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 15:26:59 [WARNING] Gripper_485.py:255 - 等待回复超时，节点ID: 1
2025-07-22 15:27:02 [INFO] Gripper_485.py:53 - 初始化RS485夹爪 - 端口: /dev/ttyUSB1, 节点ID: 1, 波特率: 115200
2025-07-22 15:27:02 [DEBUG] Gripper_485.py:92 - 启动RS485消息接收线程
2025-07-22 15:27:02 [INFO] Gripper_485.py:96 - RS485消息接收线程已启动
2025-07-22 15:27:02 [INFO] Gripper_485.py:53 - 初始化RS485夹爪 - 端口: /dev/ttyUSB2, 节点ID: 1, 波特率: 115200
2025-07-22 15:27:02 [DEBUG] Gripper_485.py:92 - 启动RS485消息接收线程
2025-07-22 15:27:02 [INFO] Gripper_485.py:96 - RS485消息接收线程已启动
2025-07-22 15:27:04 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 15:27:04 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 15:27:04 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 15:27:09 [WARNING] Gripper_485.py:255 - 等待回复超时，节点ID: 1
2025-07-22 15:27:12 [WARNING] Gripper_485.py:276 - 获取状态超时 (1.0s)
2025-07-22 15:27:13 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 15:27:13 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 15:27:13 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 15:27:18 [WARNING] Gripper_485.py:255 - 等待回复超时，节点ID: 1
2025-07-22 15:27:21 [WARNING] Gripper_485.py:276 - 获取状态超时 (1.0s)
