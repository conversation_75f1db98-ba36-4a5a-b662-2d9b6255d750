2025-07-22 21:48:25 [INFO] logger_config.py:102 - 日志系统初始化完成，日志文件: /home/<USER>/Spatial_AI/logs/gripper_485/gripper_485_1_20250722_214825.log
2025-07-22 21:48:25 [INFO] Gripper_485.py:53 - 初始化RS485夹爪 - 端口: /dev/ttyUSB1, 节点ID: 1, 波特率: 115200
2025-07-22 21:48:25 [DEBUG] Gripper_485.py:92 - 启动RS485消息接收线程
2025-07-22 21:48:25 [INFO] Gripper_485.py:96 - RS485消息接收线程已启动
2025-07-22 21:48:25 [INFO] Gripper_485.py:53 - 初始化RS485夹爪 - 端口: /dev/ttyUSB2, 节点ID: 1, 波特率: 115200
2025-07-22 21:48:25 [DEBUG] Gripper_485.py:92 - 启动RS485消息接收线程
2025-07-22 21:48:25 [INFO] Gripper_485.py:96 - RS485消息接收线程已启动
2025-07-22 21:48:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:48:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:48:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:48:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFD
2025-07-22 21:48:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFD
2025-07-22 21:48:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xFC
2025-07-22 21:48:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFC
2025-07-22 21:48:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xFA
2025-07-22 21:48:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFA
2025-07-22 21:48:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xF7
2025-07-22 21:48:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xF7
2025-07-22 21:48:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xF4
2025-07-22 21:48:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xF4
2025-07-22 21:48:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xF0
2025-07-22 21:48:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xF0
2025-07-22 21:48:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xEC
2025-07-22 21:48:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xEC
2025-07-22 21:48:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xE7
2025-07-22 21:48:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xE7
2025-07-22 21:48:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xE2
2025-07-22 21:48:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xE2
2025-07-22 21:48:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xDC
2025-07-22 21:48:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xDC
2025-07-22 21:48:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xD5
2025-07-22 21:48:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xD5
2025-07-22 21:48:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xCE
2025-07-22 21:48:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xCE
2025-07-22 21:48:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xC7
2025-07-22 21:48:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xC7
2025-07-22 21:48:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xC1
2025-07-22 21:48:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xC0
2025-07-22 21:48:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xBA
2025-07-22 21:48:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xBA
2025-07-22 21:48:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xB3
2025-07-22 21:48:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xB3
2025-07-22 21:48:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xAC
2025-07-22 21:48:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xAC
2025-07-22 21:48:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xA5
2025-07-22 21:48:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xA5
2025-07-22 21:48:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x9F
2025-07-22 21:48:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x9F
2025-07-22 21:48:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x99
2025-07-22 21:48:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x99
2025-07-22 21:48:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x94
2025-07-22 21:48:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x94
2025-07-22 21:48:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x8F
2025-07-22 21:48:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x8F
2025-07-22 21:48:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x8B
2025-07-22 21:48:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x8B
2025-07-22 21:48:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x88
2025-07-22 21:48:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x88
2025-07-22 21:48:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x85
2025-07-22 21:48:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x85
2025-07-22 21:48:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x83
2025-07-22 21:48:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x83
2025-07-22 21:48:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x81
2025-07-22 21:48:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x81
2025-07-22 21:48:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-22 21:48:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-22 21:48:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-22 21:48:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-22 21:48:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-22 21:48:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:48:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:48:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:48:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:48:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:48:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:48:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:48:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:48:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:48:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:48:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:48:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:48:27 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:27 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 5, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:27 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:27 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:27 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:27 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 5, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:27 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:27 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:27 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:27 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 5, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:27 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:27 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:27 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:27 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 5, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:27 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:27 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:27 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:27 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 5, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:27 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:27 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:27 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:27 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 5, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:27 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:27 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:27 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:28 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:28 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 5, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:28 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:28 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:28 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:28 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 5, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:28 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:28 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:28 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:28 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 5, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:28 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:28 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:28 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:28 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:28 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:28 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:28 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:28 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 5, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:28 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:28 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:28 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:28 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 5, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:28 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:28 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:28 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:28 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 5, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:28 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:28 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:28 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:28 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 5, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:28 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:28 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:28 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:28 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 5, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:28 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:28 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:28 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:28 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 5, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:28 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:28 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:28 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:29 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:29 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:29 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 5, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:29 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:29 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:29 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:29 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:29 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:29 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 5, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:29 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:29 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:29 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:29 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:29 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:29 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 5, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:29 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:29 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:29 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:29 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:29 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:29 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 5, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:29 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:29 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:29 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:29 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:29 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:29 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 5, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:29 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:29 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:29 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:29 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:29 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:29 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 5, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:29 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:29 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:29 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:29 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:29 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:29 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 5, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:29 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:29 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:29 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:29 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:29 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:29 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 5, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:29 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:29 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:29 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:48:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:48:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:48:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:48:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:29 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:29 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:29 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:29 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:29 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:29 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:48:29 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:29 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:29 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:29 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:29 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:29 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:29 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:29 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:29 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:29 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:29 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:29 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:29 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:29 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:29 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:29 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:29 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:29 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:29 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:29 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:29 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:29 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:29 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:29 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:30 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:30 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:30 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:30 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:30 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:30 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:30 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:30 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:30 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:30 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:30 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:30 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:30 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:30 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:30 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:30 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:30 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:30 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:30 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:30 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:30 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:30 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:30 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:30 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:30 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:30 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:30 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:30 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:30 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:30 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:30 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:30 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:30 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:30 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:30 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:30 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:30 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:30 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:30 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:30 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:30 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:30 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:30 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:30 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:30 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:30 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:30 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:30 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:30 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:30 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:30 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:30 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:30 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:30 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:30 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:30 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:30 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:30 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:30 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:30 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:30 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:30 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:30 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:30 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:30 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:30 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:30 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:30 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:30 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:30 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:30 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:30 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:30 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:30 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:30 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:30 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:30 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:30 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:30 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:30 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:30 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:30 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:30 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:30 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:30 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:30 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:30 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:30 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:30 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:30 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:30 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:30 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:30 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:30 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:30 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:30 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:30 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:30 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:30 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:30 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:30 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:30 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:30 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:30 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:30 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:30 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:30 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:30 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:30 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:30 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:30 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:30 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:30 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:30 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:30 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:30 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:30 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:30 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:30 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:30 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:30 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:30 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:30 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:30 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:30 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:30 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:30 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:30 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:30 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:30 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:30 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:30 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:31 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:31 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:31 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:31 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:31 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:31 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:31 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:31 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:31 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:31 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:31 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:31 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:31 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:31 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:31 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:31 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:31 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:31 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:31 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:31 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:31 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:31 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:31 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:31 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:31 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:31 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:31 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:31 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:31 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:31 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:31 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:31 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:31 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:31 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:31 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:31 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:31 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:31 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:31 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:35 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:48:36 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:36 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:36 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:36 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:36 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:36 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:36 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:36 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:36 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:36 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:36 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:36 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:36 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:36 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:36 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:36 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:36 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:36 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:36 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:36 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:36 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:36 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:36 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:36 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:36 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:36 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:36 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:36 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:36 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:36 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:36 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:36 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:36 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:36 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:36 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:36 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:36 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:36 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:36 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:36 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:36 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:36 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:36 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:36 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:36 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:36 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:36 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:36 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:36 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:36 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:36 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:36 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:36 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:36 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:36 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:36 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:36 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:36 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:36 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:36 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:36 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:36 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:36 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:36 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:36 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:36 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:36 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:36 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:36 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:36 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:36 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:36 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:36 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:36 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:36 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:36 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:36 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:36 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:36 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:36 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:36 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:36 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:36 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:36 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:36 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:36 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:36 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:36 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:48:36 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:36 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 10, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:36 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:36 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:36 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:36 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 4, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:36 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:36 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:48:36 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:36 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:36 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:36 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:36 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:36 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:36 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:36 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:36 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:37 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:37 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:37 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:37 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:37 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:37 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:37 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:37 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:37 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:37 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:37 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:37 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:37 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:37 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:37 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:37 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:37 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:37 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:37 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:37 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:37 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:37 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:37 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:37 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:37 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:37 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:37 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:37 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:37 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:37 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:37 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:37 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:37 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:37 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:37 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:37 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:37 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:37 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:37 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:37 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:37 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:37 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:37 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:37 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:37 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:37 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:37 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:37 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:37 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:37 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:37 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:37 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:37 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:37 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:37 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:37 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:37 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:37 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:37 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:37 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:37 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:37 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:37 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:37 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:37 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:37 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:37 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:37 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:37 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:37 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:37 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:37 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:37 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:37 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:37 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:37 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:37 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:37 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:38 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:38 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:38 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:38 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:38 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:38 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:38 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:38 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:38 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:38 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:38 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:38 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:38 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:38 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:38 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:38 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:38 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:38 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:38 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:38 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:38 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:38 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:38 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:38 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:38 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:38 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:38 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:38 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:38 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:38 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:38 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:38 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:38 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:38 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:38 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:38 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:38 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:38 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:38 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:38 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:38 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:38 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:38 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:38 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:38 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:38 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:38 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:38 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:38 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:38 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:38 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:38 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:38 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:38 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:38 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:38 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:38 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:38 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:38 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:38 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:38 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:38 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:38 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:38 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:38 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:38 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:38 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:38 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:38 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:38 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:38 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:38 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:38 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:38 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:38 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:38 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:38 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:38 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:38 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:38 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:38 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:38 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:38 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:38 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:38 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:38 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:38 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:38 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:38 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:38 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:38 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:38 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:38 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:38 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:38 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:38 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:39 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:39 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:39 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:39 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:39 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:39 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:39 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:39 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:39 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:39 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:39 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:39 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:39 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:39 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:39 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:39 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:39 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:39 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:39 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:39 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:39 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:39 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:39 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:39 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:39 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:39 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:39 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:39 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:39 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:39 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:39 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:39 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:39 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:39 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:39 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:39 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:39 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:39 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:39 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:39 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:39 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:39 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:39 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:39 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:39 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:39 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:39 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:39 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:39 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:39 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:39 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:39 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:39 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:39 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:48:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:39 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:39 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:39 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:39 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:39 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:39 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:48:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:48:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:48:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:39 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:39 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:39 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:39 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:39 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:39 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:48:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:48:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:48:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:48:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:48:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:48:39 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:39 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:39 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:39 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:39 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:39 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:39 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:39 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:39 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:39 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:39 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:39 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:39 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:39 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:39 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:39 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:39 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:39 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:48:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:48:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:48:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:48:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:48:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:48:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:48:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:48:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:48:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:48:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:48:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:48:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:48:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:48:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:48:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:42 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:42 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:42 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:42 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:42 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:42 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:42 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:42 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:42 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:42 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:42 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:42 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:42 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:42 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:42 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:42 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:42 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:42 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:42 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:42 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:42 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:42 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:42 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:42 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:42 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:42 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:42 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:42 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:42 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:42 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:42 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:42 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:42 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:42 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:42 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:42 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:42 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:42 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:42 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:42 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:42 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:42 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:42 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:42 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:42 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:42 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:42 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:42 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:42 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:42 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:42 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:42 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:48:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:48:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:48:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:48:42 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:42 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:42 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:42 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:48:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:48:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:48:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:48:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:48:42 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:42 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:42 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:42 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:48:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:48:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:48:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:48:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:48:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:48:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:48:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:48:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:48:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:48:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:48:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:48:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:48:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:48:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:48:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:48:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:48:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:48:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 5, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:48:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:48:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:48:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:48:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:48:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:48:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
