2025-07-22 16:09:11 [INFO] logger_config.py:102 - 日志系统初始化完成，日志文件: /home/<USER>/Spatial_AI/logs/gripper_485/gripper_485_1_20250722_160911.log
2025-07-22 16:09:11 [INFO] Gripper_485.py:53 - 初始化RS485夹爪 - 端口: /dev/ttyUSB1, 节点ID: 1, 波特率: 115200
2025-07-22 16:09:11 [DEBUG] Gripper_485.py:92 - 启动RS485消息接收线程
2025-07-22 16:09:11 [INFO] Gripper_485.py:96 - RS485消息接收线程已启动
2025-07-22 16:09:11 [INFO] Gripper_485.py:53 - 初始化RS485夹爪 - 端口: /dev/ttyUSB2, 节点ID: 1, 波特率: 115200
2025-07-22 16:09:11 [DEBUG] Gripper_485.py:92 - 启动RS485消息接收线程
2025-07-22 16:09:11 [INFO] Gripper_485.py:96 - RS485消息接收线程已启动
2025-07-22 16:09:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x64
2025-07-22 16:09:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x64
2025-07-22 16:09:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x65
2025-07-22 16:09:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x66
2025-07-22 16:09:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x68
2025-07-22 16:09:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x6B
2025-07-22 16:09:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x6E
2025-07-22 16:09:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x72
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x76
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x79
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7B
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7D
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7E
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 16:09:13 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7E
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7C
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7B
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x79
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x77
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x75
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x73
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x70
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x6C
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x69
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x66
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x62
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x5D
2025-07-22 16:09:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x59
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x54
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x4F
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x4B
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x46
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x41
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x3D
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x38
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x33
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x2E
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x2A
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x25
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x21
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x1D
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x19
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x15
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x12
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x0F
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x0C
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x0A
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x08
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:14 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x00, 实际=0x08, 误差=8
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x06
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x04
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x03
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x02
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x01
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:14 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:09:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x01
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x02
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x04
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x05
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x07
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x0A
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x0C
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x0F
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x12
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x15
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x19
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x1D
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x21
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x25
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x2A
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x2F
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x33
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x38
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x3D
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x41
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x45
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x49
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x4D
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x50
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x53
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x56
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x59
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x5B
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x5D
2025-07-22 16:09:14 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x5D, 误差=7
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x5F
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x60
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x61
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x62
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x63
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x63
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x63
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x63
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x63
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x63
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:14 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 16:09:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x63
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x63
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x63
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x63
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x63
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x63
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x62
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x61
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x60
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x5E
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x5C
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x5A
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x58
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x55
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x52
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x4F
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x4B
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x47
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x43
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x3F
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x3B
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x36
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x31
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x2C
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x28
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x23
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x1F
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x1B
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x17
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x14
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x11
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x0E
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x0B
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x09
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x07
2025-07-22 16:09:15 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x00, 实际=0x07, 误差=7
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x05
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x03
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x02
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x01
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:15 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:09:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:20 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:20 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:20 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:20 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:20 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:20 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:20 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:20 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:20 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:20 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:20 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:20 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:20 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:20 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:20 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:20 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:20 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:20 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:20 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:20 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:20 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:20 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x00, 误差=100
2025-07-22 16:09:20 [WARNING] Gripper_485.py:255 - 等待回复超时，节点ID: 1
2025-07-22 16:09:20 [WARNING] Gripper_485.py:257 - 最后状态: 目标=0x64, 实际=0x00
2025-07-22 16:09:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:09:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:09:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
