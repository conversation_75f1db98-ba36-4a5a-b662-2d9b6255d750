2025-07-23 10:52:37 [INFO] logger_config.py:102 - 日志系统初始化完成，日志文件: /home/<USER>/Spatial_AI/logs/gripper_485/gripper_485_1_20250723_105237.log
2025-07-23 10:52:37 [INFO] Gripper_485.py:53 - 初始化RS485夹爪 - 端口: /dev/ttyUSB2, 节点ID: 1, 波特率: 115200
2025-07-23 10:52:37 [DEBUG] Gripper_485.py:92 - 启动RS485消息接收线程
2025-07-23 10:52:37 [INFO] Gripper_485.py:96 - RS485消息接收线程已启动
2025-07-23 10:52:37 [INFO] Gripper_485.py:53 - 初始化RS485夹爪 - 端口: /dev/ttyUSB1, 节点ID: 1, 波特率: 115200
2025-07-23 10:52:37 [DEBUG] Gripper_485.py:92 - 启动RS485消息接收线程
2025-07-23 10:52:37 [INFO] Gripper_485.py:96 - RS485消息接收线程已启动
2025-07-23 10:52:44 [INFO] Gripper_485.py:386 - 移动到位置 0xF9
2025-07-23 10:52:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF9, 力矩=0xFF
2025-07-23 10:52:44 [INFO] Gripper_485.py:386 - 移动到位置 0xF7
2025-07-23 10:52:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF7, 力矩=0xFF
2025-07-23 10:52:44 [INFO] Gripper_485.py:386 - 移动到位置 0xEB
2025-07-23 10:52:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xEB, 力矩=0xFF
2025-07-23 10:52:44 [INFO] Gripper_485.py:386 - 移动到位置 0xE6
2025-07-23 10:52:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xE6, 力矩=0xFF
2025-07-23 10:52:44 [INFO] Gripper_485.py:386 - 移动到位置 0xDD
2025-07-23 10:52:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xDD, 力矩=0xFF
2025-07-23 10:52:44 [INFO] Gripper_485.py:386 - 移动到位置 0xC6
2025-07-23 10:52:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xC6, 力矩=0xFF
2025-07-23 10:52:44 [INFO] Gripper_485.py:386 - 移动到位置 0xB2
2025-07-23 10:52:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xB2, 力矩=0xFF
2025-07-23 10:52:44 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-23 10:52:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-23 10:52:44 [INFO] Gripper_485.py:386 - 移动到位置 0x14
2025-07-23 10:52:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x14, 力矩=0xFF
2025-07-23 10:52:44 [INFO] Gripper_485.py:386 - 移动到位置 0x0B
2025-07-23 10:52:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0B, 力矩=0xFF
2025-07-23 10:52:44 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:44 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:44 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:44 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:44 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:44 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:44 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:44 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:44 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:44 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:44 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:44 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:44 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:44 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:44 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:44 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:44 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:44 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:44 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:44 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:44 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:44 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:44 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:44 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:44 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:44 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:44 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:44 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:44 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:44 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:44 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:44 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:44 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:44 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:44 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:44 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:44 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:44 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:44 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:44 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:44 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:44 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:44 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:44 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:44 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:44 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:44 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:44 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:44 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:44 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:44 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:45 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:45 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:45 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:45 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:45 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:45 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:45 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:45 [INFO] Gripper_485.py:386 - 移动到位置 0x08
2025-07-23 10:52:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x08, 力矩=0xFF
2025-07-23 10:52:45 [INFO] Gripper_485.py:386 - 移动到位置 0x1A
2025-07-23 10:52:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x1A, 力矩=0xFF
2025-07-23 10:52:45 [INFO] Gripper_485.py:386 - 移动到位置 0x86
2025-07-23 10:52:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x86, 力矩=0xFF
2025-07-23 10:52:45 [INFO] Gripper_485.py:386 - 移动到位置 0xB2
2025-07-23 10:52:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xB2, 力矩=0xFF
2025-07-23 10:52:45 [INFO] Gripper_485.py:386 - 移动到位置 0xEB
2025-07-23 10:52:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xEB, 力矩=0xFF
2025-07-23 10:52:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFA
2025-07-23 10:52:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFA, 力矩=0xFF
2025-07-23 10:52:51 [INFO] Gripper_485.py:386 - 移动到位置 0xFC
2025-07-23 10:52:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFC, 力矩=0xFF
2025-07-23 10:52:51 [INFO] Gripper_485.py:386 - 移动到位置 0xC3
2025-07-23 10:52:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xC3, 力矩=0xFF
2025-07-23 10:52:51 [INFO] Gripper_485.py:386 - 移动到位置 0x74
2025-07-23 10:52:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x74, 力矩=0xFF
2025-07-23 10:52:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:52 [INFO] Gripper_485.py:386 - 移动到位置 0x02
2025-07-23 10:52:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x02, 力矩=0xFF
2025-07-23 10:52:52 [INFO] Gripper_485.py:386 - 移动到位置 0x0A
2025-07-23 10:52:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0A, 力矩=0xFF
2025-07-23 10:52:52 [INFO] Gripper_485.py:386 - 移动到位置 0x0E
2025-07-23 10:52:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0E, 力矩=0xFF
2025-07-23 10:52:52 [INFO] Gripper_485.py:386 - 移动到位置 0x16
2025-07-23 10:52:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x16, 力矩=0xFF
2025-07-23 10:52:52 [INFO] Gripper_485.py:386 - 移动到位置 0x3E
2025-07-23 10:52:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x3E, 力矩=0xFF
2025-07-23 10:52:52 [INFO] Gripper_485.py:386 - 移动到位置 0x5A
2025-07-23 10:52:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x5A, 力矩=0xFF
2025-07-23 10:52:52 [INFO] Gripper_485.py:386 - 移动到位置 0x7B
2025-07-23 10:52:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x7B, 力矩=0xFF
2025-07-23 10:52:52 [INFO] Gripper_485.py:386 - 移动到位置 0xC2
2025-07-23 10:52:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xC2, 力矩=0xFF
2025-07-23 10:52:52 [INFO] Gripper_485.py:386 - 移动到位置 0xE1
2025-07-23 10:52:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xE1, 力矩=0xFF
2025-07-23 10:52:55 [INFO] Gripper_485.py:386 - 移动到位置 0xFC
2025-07-23 10:52:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFC, 力矩=0xFF
2025-07-23 10:52:55 [INFO] Gripper_485.py:386 - 移动到位置 0xEF
2025-07-23 10:52:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xEF, 力矩=0xFF
2025-07-23 10:52:55 [INFO] Gripper_485.py:386 - 移动到位置 0xBA
2025-07-23 10:52:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xBA, 力矩=0xFF
2025-07-23 10:52:55 [INFO] Gripper_485.py:386 - 移动到位置 0x79
2025-07-23 10:52:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x79, 力矩=0xFF
2025-07-23 10:52:55 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:55 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:55 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:55 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:55 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:55 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:55 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:55 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:55 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:55 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:52:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:52:56 [INFO] Gripper_485.py:386 - 移动到位置 0x13
2025-07-23 10:52:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x13, 力矩=0xFF
2025-07-23 10:52:56 [INFO] Gripper_485.py:386 - 移动到位置 0x5C
2025-07-23 10:52:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x5C, 力矩=0xFF
2025-07-23 10:52:56 [INFO] Gripper_485.py:386 - 移动到位置 0xD6
2025-07-23 10:52:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xD6, 力矩=0xFF
2025-07-23 10:52:56 [INFO] Gripper_485.py:386 - 移动到位置 0xF2
2025-07-23 10:52:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF2, 力矩=0xFF
2025-07-23 10:53:04 [INFO] Gripper_485.py:386 - 移动到位置 0xF5
2025-07-23 10:53:04 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF5, 力矩=0xFF
2025-07-23 10:53:04 [INFO] Gripper_485.py:386 - 移动到位置 0xD9
2025-07-23 10:53:04 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xD9, 力矩=0xFF
2025-07-23 10:53:04 [INFO] Gripper_485.py:386 - 移动到位置 0x40
2025-07-23 10:53:04 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x40, 力矩=0xFF
2025-07-23 10:53:04 [INFO] Gripper_485.py:386 - 移动到位置 0x06
2025-07-23 10:53:04 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x06, 力矩=0xFF
2025-07-23 10:53:04 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:53:04 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:53:04 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:53:04 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:53:04 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:53:04 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:53:04 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:53:04 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:53:04 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:53:04 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:53:04 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:53:04 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:53:04 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:53:04 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:53:04 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:53:04 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:53:04 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:53:04 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:53:04 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:53:04 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:53:04 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:53:04 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:53:04 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:53:04 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:53:04 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:53:04 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:53:04 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:53:04 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:53:04 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:53:04 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:53:04 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:53:04 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:53:04 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:53:04 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:53:04 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:53:04 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:53:04 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:53:04 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:53:04 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:53:04 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:53:04 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:53:04 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:53:04 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:53:04 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:53:04 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:53:04 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:53:04 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:53:04 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:53:04 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:53:04 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:53:04 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:53:04 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:53:04 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:53:04 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:53:05 [INFO] Gripper_485.py:386 - 移动到位置 0x03
2025-07-23 10:53:05 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x03, 力矩=0xFF
2025-07-23 10:53:05 [INFO] Gripper_485.py:386 - 移动到位置 0x0A
2025-07-23 10:53:05 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0A, 力矩=0xFF
2025-07-23 10:53:05 [INFO] Gripper_485.py:386 - 移动到位置 0x47
2025-07-23 10:53:05 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x47, 力矩=0xFF
2025-07-23 10:53:05 [INFO] Gripper_485.py:386 - 移动到位置 0x74
2025-07-23 10:53:05 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x74, 力矩=0xFF
2025-07-23 10:53:05 [INFO] Gripper_485.py:386 - 移动到位置 0x9F
2025-07-23 10:53:05 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x9F, 力矩=0xFF
2025-07-23 10:53:05 [INFO] Gripper_485.py:386 - 移动到位置 0xDC
2025-07-23 10:53:05 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xDC, 力矩=0xFF
2025-07-23 10:53:05 [INFO] Gripper_485.py:386 - 移动到位置 0xF3
2025-07-23 10:53:05 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF3, 力矩=0xFF
2025-07-23 10:53:07 [INFO] Gripper_485.py:386 - 移动到位置 0xDE
2025-07-23 10:53:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xDE, 力矩=0xFF
2025-07-23 10:53:07 [INFO] Gripper_485.py:386 - 移动到位置 0xC2
2025-07-23 10:53:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xC2, 力矩=0xFF
2025-07-23 10:53:07 [INFO] Gripper_485.py:386 - 移动到位置 0x71
2025-07-23 10:53:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x71, 力矩=0xFF
2025-07-23 10:53:07 [INFO] Gripper_485.py:386 - 移动到位置 0x3C
2025-07-23 10:53:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x3C, 力矩=0xFF
2025-07-23 10:53:07 [INFO] Gripper_485.py:386 - 移动到位置 0x0C
2025-07-23 10:53:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0C, 力矩=0xFF
2025-07-23 10:53:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:53:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:53:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:53:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:53:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:53:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:53:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:53:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:53:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:53:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:53:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:53:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:53:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:53:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:53:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:53:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:53:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:53:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:53:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:53:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:53:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:53:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:53:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:53:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:53:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:53:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:53:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:53:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:53:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:53:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:53:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:53:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:53:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:53:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:53:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:53:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:53:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:53:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:53:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:53:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:53:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:53:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:53:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:53:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:53:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:53:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:53:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:53:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:53:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:53:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:53:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:53:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:53:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:53:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:53:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:53:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:53:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:53:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:53:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:53:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:53:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:53:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:53:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:53:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:53:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:53:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:53:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:53:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:53:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:53:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:53:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:53:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:53:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:53:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:53:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:53:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:53:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:53:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:53:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:53:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:53:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:53:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:53:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:53:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:53:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:53:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:53:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:53:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:53:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:53:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:53:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:53:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:53:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:53:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:53:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:53:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:53:08 [INFO] Gripper_485.py:386 - 移动到位置 0x03
2025-07-23 10:53:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x03, 力矩=0xFF
2025-07-23 10:53:08 [INFO] Gripper_485.py:386 - 移动到位置 0x44
2025-07-23 10:53:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x44, 力矩=0xFF
2025-07-23 10:53:08 [INFO] Gripper_485.py:386 - 移动到位置 0x80
2025-07-23 10:53:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x80, 力矩=0xFF
2025-07-23 10:53:08 [INFO] Gripper_485.py:386 - 移动到位置 0xD7
2025-07-23 10:53:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xD7, 力矩=0xFF
2025-07-23 10:53:08 [INFO] Gripper_485.py:386 - 移动到位置 0xF8
2025-07-23 10:53:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF8, 力矩=0xFF
2025-07-23 10:54:07 [INFO] Gripper_485.py:386 - 移动到位置 0xF9
2025-07-23 10:54:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF9, 力矩=0xFF
2025-07-23 10:54:07 [INFO] Gripper_485.py:386 - 移动到位置 0x85
2025-07-23 10:54:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x85, 力矩=0xFF
2025-07-23 10:54:07 [INFO] Gripper_485.py:386 - 移动到位置 0x02
2025-07-23 10:54:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x02, 力矩=0xFF
2025-07-23 10:54:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:07 [INFO] Gripper_485.py:386 - 移动到位置 0x06
2025-07-23 10:54:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x06, 力矩=0xFF
2025-07-23 10:54:07 [INFO] Gripper_485.py:386 - 移动到位置 0x0C
2025-07-23 10:54:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0C, 力矩=0xFF
2025-07-23 10:54:07 [INFO] Gripper_485.py:386 - 移动到位置 0x6F
2025-07-23 10:54:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x6F, 力矩=0xFF
2025-07-23 10:54:07 [INFO] Gripper_485.py:386 - 移动到位置 0xA1
2025-07-23 10:54:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xA1, 力矩=0xFF
2025-07-23 10:54:07 [INFO] Gripper_485.py:386 - 移动到位置 0xE8
2025-07-23 10:54:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xE8, 力矩=0xFF
2025-07-23 10:54:07 [INFO] Gripper_485.py:386 - 移动到位置 0xFB
2025-07-23 10:54:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFB, 力矩=0xFF
2025-07-23 10:54:09 [INFO] Gripper_485.py:386 - 移动到位置 0xDF
2025-07-23 10:54:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xDF, 力矩=0xFF
2025-07-23 10:54:09 [INFO] Gripper_485.py:386 - 移动到位置 0xC2
2025-07-23 10:54:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xC2, 力矩=0xFF
2025-07-23 10:54:09 [INFO] Gripper_485.py:386 - 移动到位置 0x48
2025-07-23 10:54:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x48, 力矩=0xFF
2025-07-23 10:54:09 [INFO] Gripper_485.py:386 - 移动到位置 0x05
2025-07-23 10:54:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x05, 力矩=0xFF
2025-07-23 10:54:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:10 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:10 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:10 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:10 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:10 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:10 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:10 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:10 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:10 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:10 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:10 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:10 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:10 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:10 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:10 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:10 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:10 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:10 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:10 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:10 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:10 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:10 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:10 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:10 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:10 [INFO] Gripper_485.py:386 - 移动到位置 0x05
2025-07-23 10:54:10 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x05, 力矩=0xFF
2025-07-23 10:54:10 [INFO] Gripper_485.py:386 - 移动到位置 0x58
2025-07-23 10:54:10 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x58, 力矩=0xFF
2025-07-23 10:54:10 [INFO] Gripper_485.py:386 - 移动到位置 0x9D
2025-07-23 10:54:10 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x9D, 力矩=0xFF
2025-07-23 10:54:10 [INFO] Gripper_485.py:386 - 移动到位置 0xE3
2025-07-23 10:54:10 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xE3, 力矩=0xFF
2025-07-23 10:54:10 [INFO] Gripper_485.py:386 - 移动到位置 0xF7
2025-07-23 10:54:10 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF7, 力矩=0xFF
2025-07-23 10:54:11 [INFO] Gripper_485.py:386 - 移动到位置 0xF7
2025-07-23 10:54:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF7, 力矩=0xFF
2025-07-23 10:54:11 [INFO] Gripper_485.py:386 - 移动到位置 0xE5
2025-07-23 10:54:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xE5, 力矩=0xFF
2025-07-23 10:54:11 [INFO] Gripper_485.py:386 - 移动到位置 0xAC
2025-07-23 10:54:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xAC, 力矩=0xFF
2025-07-23 10:54:11 [INFO] Gripper_485.py:386 - 移动到位置 0x77
2025-07-23 10:54:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x77, 力矩=0xFF
2025-07-23 10:54:11 [INFO] Gripper_485.py:386 - 移动到位置 0x08
2025-07-23 10:54:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x08, 力矩=0xFF
2025-07-23 10:54:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:11 [INFO] Gripper_485.py:386 - 移动到位置 0x02
2025-07-23 10:54:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x02, 力矩=0xFF
2025-07-23 10:54:11 [INFO] Gripper_485.py:386 - 移动到位置 0x03
2025-07-23 10:54:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x03, 力矩=0xFF
2025-07-23 10:54:11 [INFO] Gripper_485.py:386 - 移动到位置 0x07
2025-07-23 10:54:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x07, 力矩=0xFF
2025-07-23 10:54:11 [INFO] Gripper_485.py:386 - 移动到位置 0x07
2025-07-23 10:54:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x07, 力矩=0xFF
2025-07-23 10:54:11 [INFO] Gripper_485.py:386 - 移动到位置 0x0A
2025-07-23 10:54:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0A, 力矩=0xFF
2025-07-23 10:54:11 [INFO] Gripper_485.py:386 - 移动到位置 0x08
2025-07-23 10:54:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x08, 力矩=0xFF
2025-07-23 10:54:11 [INFO] Gripper_485.py:386 - 移动到位置 0x07
2025-07-23 10:54:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x07, 力矩=0xFF
2025-07-23 10:54:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:11 [INFO] Gripper_485.py:386 - 移动到位置 0x02
2025-07-23 10:54:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x02, 力矩=0xFF
2025-07-23 10:54:11 [INFO] Gripper_485.py:386 - 移动到位置 0x04
2025-07-23 10:54:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x04, 力矩=0xFF
2025-07-23 10:54:11 [INFO] Gripper_485.py:386 - 移动到位置 0x05
2025-07-23 10:54:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x05, 力矩=0xFF
2025-07-23 10:54:11 [INFO] Gripper_485.py:386 - 移动到位置 0x07
2025-07-23 10:54:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x07, 力矩=0xFF
2025-07-23 10:54:11 [INFO] Gripper_485.py:386 - 移动到位置 0x07
2025-07-23 10:54:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x07, 力矩=0xFF
2025-07-23 10:54:12 [INFO] Gripper_485.py:386 - 移动到位置 0x08
2025-07-23 10:54:12 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x08, 力矩=0xFF
2025-07-23 10:54:12 [INFO] Gripper_485.py:386 - 移动到位置 0x0B
2025-07-23 10:54:12 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0B, 力矩=0xFF
2025-07-23 10:54:12 [INFO] Gripper_485.py:386 - 移动到位置 0x0E
2025-07-23 10:54:12 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0E, 力矩=0xFF
2025-07-23 10:54:12 [INFO] Gripper_485.py:386 - 移动到位置 0x1E
2025-07-23 10:54:12 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x1E, 力矩=0xFF
2025-07-23 10:54:12 [INFO] Gripper_485.py:386 - 移动到位置 0x33
2025-07-23 10:54:12 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x33, 力矩=0xFF
2025-07-23 10:54:12 [INFO] Gripper_485.py:386 - 移动到位置 0x48
2025-07-23 10:54:12 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x48, 力矩=0xFF
2025-07-23 10:54:12 [INFO] Gripper_485.py:386 - 移动到位置 0x81
2025-07-23 10:54:12 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x81, 力矩=0xFF
2025-07-23 10:54:12 [INFO] Gripper_485.py:386 - 移动到位置 0xA2
2025-07-23 10:54:12 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xA2, 力矩=0xFF
2025-07-23 10:54:12 [INFO] Gripper_485.py:386 - 移动到位置 0xC2
2025-07-23 10:54:12 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xC2, 力矩=0xFF
2025-07-23 10:54:12 [INFO] Gripper_485.py:386 - 移动到位置 0xF2
2025-07-23 10:54:12 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF2, 力矩=0xFF
2025-07-23 10:54:28 [INFO] Gripper_485.py:386 - 移动到位置 0xFD
2025-07-23 10:54:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFD, 力矩=0xFF
2025-07-23 10:54:28 [INFO] Gripper_485.py:386 - 移动到位置 0xFB
2025-07-23 10:54:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFB, 力矩=0xFF
2025-07-23 10:54:28 [INFO] Gripper_485.py:386 - 移动到位置 0xFB
2025-07-23 10:54:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFB, 力矩=0xFF
2025-07-23 10:54:28 [INFO] Gripper_485.py:386 - 移动到位置 0xEE
2025-07-23 10:54:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xEE, 力矩=0xFF
2025-07-23 10:54:28 [INFO] Gripper_485.py:386 - 移动到位置 0xE7
2025-07-23 10:54:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xE7, 力矩=0xFF
2025-07-23 10:54:28 [INFO] Gripper_485.py:386 - 移动到位置 0xD2
2025-07-23 10:54:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xD2, 力矩=0xFF
2025-07-23 10:54:28 [INFO] Gripper_485.py:386 - 移动到位置 0xD2
2025-07-23 10:54:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xD2, 力矩=0xFF
2025-07-23 10:54:28 [INFO] Gripper_485.py:386 - 移动到位置 0xCA
2025-07-23 10:54:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xCA, 力矩=0xFF
2025-07-23 10:54:28 [INFO] Gripper_485.py:386 - 移动到位置 0x91
2025-07-23 10:54:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x91, 力矩=0xFF
2025-07-23 10:54:28 [INFO] Gripper_485.py:386 - 移动到位置 0x60
2025-07-23 10:54:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x60, 力矩=0xFF
2025-07-23 10:54:28 [INFO] Gripper_485.py:386 - 移动到位置 0x19
2025-07-23 10:54:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x19, 力矩=0xFF
2025-07-23 10:54:28 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:28 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:28 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:28 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:28 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:28 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:28 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:28 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:28 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:28 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:28 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:28 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:28 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:28 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:28 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:28 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:28 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:28 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:28 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:28 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:28 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:28 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:28 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:28 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:28 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:28 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:28 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:28 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:28 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:28 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:28 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:28 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:28 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:28 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:28 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:28 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:28 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:28 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:28 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:28 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:28 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:28 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:32 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:32 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:32 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:32 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:32 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:32 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:32 [INFO] Gripper_485.py:386 - 移动到位置 0x4F
2025-07-23 10:54:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x4F, 力矩=0xFF
2025-07-23 10:54:32 [INFO] Gripper_485.py:386 - 移动到位置 0x99
2025-07-23 10:54:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x99, 力矩=0xFF
2025-07-23 10:54:32 [INFO] Gripper_485.py:386 - 移动到位置 0xEF
2025-07-23 10:54:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xEF, 力矩=0xFF
2025-07-23 10:54:37 [INFO] Gripper_485.py:386 - 移动到位置 0xF7
2025-07-23 10:54:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF7, 力矩=0xFF
2025-07-23 10:54:37 [INFO] Gripper_485.py:386 - 移动到位置 0xEE
2025-07-23 10:54:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xEE, 力矩=0xFF
2025-07-23 10:54:37 [INFO] Gripper_485.py:386 - 移动到位置 0xCF
2025-07-23 10:54:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xCF, 力矩=0xFF
2025-07-23 10:54:37 [INFO] Gripper_485.py:386 - 移动到位置 0xBA
2025-07-23 10:54:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xBA, 力矩=0xFF
2025-07-23 10:54:37 [INFO] Gripper_485.py:386 - 移动到位置 0x75
2025-07-23 10:54:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x75, 力矩=0xFF
2025-07-23 10:54:37 [INFO] Gripper_485.py:386 - 移动到位置 0x39
2025-07-23 10:54:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x39, 力矩=0xFF
2025-07-23 10:54:37 [INFO] Gripper_485.py:386 - 移动到位置 0x0A
2025-07-23 10:54:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0A, 力矩=0xFF
2025-07-23 10:54:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:41 [INFO] Gripper_485.py:386 - 移动到位置 0x0D
2025-07-23 10:54:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0D, 力矩=0xFF
2025-07-23 10:54:41 [INFO] Gripper_485.py:386 - 移动到位置 0x85
2025-07-23 10:54:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x85, 力矩=0xFF
2025-07-23 10:54:41 [INFO] Gripper_485.py:386 - 移动到位置 0xBA
2025-07-23 10:54:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xBA, 力矩=0xFF
2025-07-23 10:54:41 [INFO] Gripper_485.py:386 - 移动到位置 0xDE
2025-07-23 10:54:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xDE, 力矩=0xFF
2025-07-23 10:54:49 [INFO] Gripper_485.py:386 - 移动到位置 0xD6
2025-07-23 10:54:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xD6, 力矩=0xFF
2025-07-23 10:54:49 [INFO] Gripper_485.py:386 - 移动到位置 0xAD
2025-07-23 10:54:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xAD, 力矩=0xFF
2025-07-23 10:54:49 [INFO] Gripper_485.py:386 - 移动到位置 0x6D
2025-07-23 10:54:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x6D, 力矩=0xFF
2025-07-23 10:54:49 [INFO] Gripper_485.py:386 - 移动到位置 0x20
2025-07-23 10:54:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x20, 力矩=0xFF
2025-07-23 10:54:49 [INFO] Gripper_485.py:386 - 移动到位置 0x02
2025-07-23 10:54:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x02, 力矩=0xFF
2025-07-23 10:54:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:52 [INFO] Gripper_485.py:386 - 移动到位置 0x03
2025-07-23 10:54:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x03, 力矩=0xFF
2025-07-23 10:54:52 [INFO] Gripper_485.py:386 - 移动到位置 0x0B
2025-07-23 10:54:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0B, 力矩=0xFF
2025-07-23 10:54:52 [INFO] Gripper_485.py:386 - 移动到位置 0x6A
2025-07-23 10:54:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x6A, 力矩=0xFF
2025-07-23 10:54:52 [INFO] Gripper_485.py:386 - 移动到位置 0xA2
2025-07-23 10:54:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xA2, 力矩=0xFF
2025-07-23 10:54:52 [INFO] Gripper_485.py:386 - 移动到位置 0xCF
2025-07-23 10:54:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xCF, 力矩=0xFF
2025-07-23 10:54:56 [INFO] Gripper_485.py:386 - 移动到位置 0xE2
2025-07-23 10:54:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xE2, 力矩=0xFF
2025-07-23 10:54:56 [INFO] Gripper_485.py:386 - 移动到位置 0xC7
2025-07-23 10:54:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xC7, 力矩=0xFF
2025-07-23 10:54:56 [INFO] Gripper_485.py:386 - 移动到位置 0xAA
2025-07-23 10:54:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xAA, 力矩=0xFF
2025-07-23 10:54:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 10:54:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 10:54:57 [INFO] Gripper_485.py:386 - 移动到位置 0x1E
2025-07-23 10:54:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x1E, 力矩=0xFF
2025-07-23 10:54:57 [INFO] Gripper_485.py:386 - 移动到位置 0x58
2025-07-23 10:54:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x58, 力矩=0xFF
2025-07-23 10:54:57 [INFO] Gripper_485.py:386 - 移动到位置 0xBD
2025-07-23 10:54:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xBD, 力矩=0xFF
2025-07-23 10:54:57 [INFO] Gripper_485.py:386 - 移动到位置 0xDB
2025-07-23 10:54:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xDB, 力矩=0xFF
2025-07-23 10:54:57 [INFO] Gripper_485.py:386 - 移动到位置 0xEE
2025-07-23 10:54:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xEE, 力矩=0xFF
