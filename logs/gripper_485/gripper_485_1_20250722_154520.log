2025-07-22 15:45:20 [INFO] logger_config.py:102 - 日志系统初始化完成，日志文件: /home/<USER>/Spatial_AI/logs/gripper_485/gripper_485_1_20250722_154520.log
2025-07-22 15:45:20 [INFO] Gripper_485.py:53 - 初始化RS485夹爪 - 端口: /dev/ttyUSB1, 节点ID: 1, 波特率: 115200
2025-07-22 15:45:20 [DEBUG] Gripper_485.py:92 - 启动RS485消息接收线程
2025-07-22 15:45:20 [INFO] Gripper_485.py:96 - RS485消息接收线程已启动
2025-07-22 15:45:20 [INFO] Gripper_485.py:53 - 初始化RS485夹爪 - 端口: /dev/ttyUSB2, 节点ID: 1, 波特率: 115200
2025-07-22 15:45:20 [DEBUG] Gripper_485.py:92 - 启动RS485消息接收线程
2025-07-22 15:45:20 [INFO] Gripper_485.py:96 - RS485消息接收线程已启动
2025-07-22 15:45:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 15:45:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 15:45:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFD
2025-07-22 15:45:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFC
2025-07-22 15:45:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFA
2025-07-22 15:45:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xF7
2025-07-22 15:45:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xF4
2025-07-22 15:45:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xF0
2025-07-22 15:45:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xEC
2025-07-22 15:45:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xE7
2025-07-22 15:45:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xE2
2025-07-22 15:45:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xDC
2025-07-22 15:45:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xD5
2025-07-22 15:45:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xCE
2025-07-22 15:45:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xC7
2025-07-22 15:45:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xC0
2025-07-22 15:45:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xB9
2025-07-22 15:45:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xB3
2025-07-22 15:45:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xAC
2025-07-22 15:45:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xA5
2025-07-22 15:45:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x9F
2025-07-22 15:45:20 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x99
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x94
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x8F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x8B
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x88
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x85
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x83
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x81
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:21 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 15:45:22 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:23 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:24 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:25 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:26 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x00, 当前=0x7F, 误差=127
2025-07-22 15:45:27 [WARNING] Gripper_485.py:255 - 等待回复超时，节点ID: 1
2025-07-22 15:45:27 [WARNING] Gripper_485.py:257 - 最后状态: 目标=0x00, 实际=0x7F
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:28 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 15:45:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 15:45:28 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 15:45:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:28 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:29 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:30 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:31 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x7F, 误差=27
2025-07-22 15:45:33 [WARNING] Gripper_485.py:255 - 等待回复超时，节点ID: 1
2025-07-22 15:45:33 [WARNING] Gripper_485.py:257 - 最后状态: 目标=0x64, 实际=0x7F
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:34 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 15:45:34 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 15:45:34 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 15:45:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:35 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 15:45:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x7F, 误差=128
2025-07-22 15:45:39 [WARNING] Gripper_485.py:255 - 等待回复超时，节点ID: 1
2025-07-22 15:45:39 [WARNING] Gripper_485.py:257 - 最后状态: 目标=0xFF, 实际=0x7F
