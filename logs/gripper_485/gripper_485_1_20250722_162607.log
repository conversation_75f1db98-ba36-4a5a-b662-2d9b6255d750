2025-07-22 16:26:07 [INFO] logger_config.py:102 - 日志系统初始化完成，日志文件: /home/<USER>/Spatial_AI/logs/gripper_485/gripper_485_1_20250722_162607.log
2025-07-22 16:26:07 [INFO] Gripper_485.py:53 - 初始化RS485夹爪 - 端口: /dev/ttyUSB1, 节点ID: 1, 波特率: 115200
2025-07-22 16:26:07 [DEBUG] Gripper_485.py:92 - 启动RS485消息接收线程
2025-07-22 16:26:07 [INFO] Gripper_485.py:96 - RS485消息接收线程已启动
2025-07-22 16:26:07 [INFO] Gripper_485.py:53 - 初始化RS485夹爪 - 端口: /dev/ttyUSB2, 节点ID: 1, 波特率: 115200
2025-07-22 16:26:07 [DEBUG] Gripper_485.py:92 - 启动RS485消息接收线程
2025-07-22 16:26:07 [INFO] Gripper_485.py:96 - RS485消息接收线程已启动
2025-07-22 16:26:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:26:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:26:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:26:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x01
2025-07-22 16:26:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x03
2025-07-22 16:26:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x07
2025-07-22 16:26:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x0A
2025-07-22 16:26:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x0E
2025-07-22 16:26:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x13
2025-07-22 16:26:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x18
2025-07-22 16:26:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x1D
2025-07-22 16:26:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x24
2025-07-22 16:26:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x2A
2025-07-22 16:26:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x32
2025-07-22 16:26:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x39
2025-07-22 16:26:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x40
2025-07-22 16:26:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x47
2025-07-22 16:26:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x4E
2025-07-22 16:26:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x55
2025-07-22 16:26:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x5B
2025-07-22 16:26:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x62
2025-07-22 16:26:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x67
2025-07-22 16:26:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x6C
2025-07-22 16:26:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x71
2025-07-22 16:26:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x74
2025-07-22 16:26:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x78
2025-07-22 16:26:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7A
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7D
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7E
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:09 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:26:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7E
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7D
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7C
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7A
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x78
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x76
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x74
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x71
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x6E
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x6C
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:09 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x6C, 误差=8
2025-07-22 16:26:09 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:26:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x6A
2025-07-22 16:26:09 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x6A, 误差=6
2025-07-22 16:26:09 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:26:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x69
2025-07-22 16:26:09 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x69, 误差=5
2025-07-22 16:26:09 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:26:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x68
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:09 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x68, 误差=4
2025-07-22 16:26:09 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:26:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x68
2025-07-22 16:26:09 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x68, 误差=4
2025-07-22 16:26:09 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:26:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x67
2025-07-22 16:26:09 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x67, 误差=3
2025-07-22 16:26:09 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:26:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x66
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:09 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x66, 误差=2
2025-07-22 16:26:09 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:26:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x66
2025-07-22 16:26:09 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x66, 误差=2
2025-07-22 16:26:09 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:26:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x65
2025-07-22 16:26:09 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x65, 误差=1
2025-07-22 16:26:09 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:26:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x65
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:09 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x65, 误差=1
2025-07-22 16:26:09 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:26:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x64
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:09 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x64, 误差=0
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x64
2025-07-22 16:26:09 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:26:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x64
2025-07-22 16:26:09 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x64, 误差=0
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x64
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:09 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:26:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x64
2025-07-22 16:26:09 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x64, 误差=0
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x63
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:09 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:26:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:09 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:09 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:26:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:09 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:09 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:26:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:26:09 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:09 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:26:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:09 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:09 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:26:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:26:09 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:10 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:10 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:26:10 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:10 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:10 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:26:10 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:26:10 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:10 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:26:10 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:26:10 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:10 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:26:10 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:10 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:10 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:26:10 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:10 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:10 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:26:10 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:26:10 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:10 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:26:10 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:10 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:10 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:26:10 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:26:10 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:10 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:26:10 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:10 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:10 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:26:10 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:10 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:10 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:26:10 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:26:10 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:10 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:26:10 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:10 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:10 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:26:10 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:10 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:10 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:26:10 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:10 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:10 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:26:10 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:10 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:10 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:26:10 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:10 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:10 [INFO] Gripper_485.py:386 - 移动到位置 0x58
2025-07-22 16:26:10 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x58, 力矩=0xFF
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:26:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x58, 当前=0x63, 误差=11
2025-07-22 16:26:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x58, 当前=0x63, 误差=11
2025-07-22 16:26:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x58, 当前=0x63, 误差=11
2025-07-22 16:26:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x58, 当前=0x63, 误差=11
2025-07-22 16:26:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x58, 当前=0x63, 误差=11
2025-07-22 16:26:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x58, 当前=0x63, 误差=11
2025-07-22 16:26:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x58, 当前=0x63, 误差=11
2025-07-22 16:26:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x58, 当前=0x63, 误差=11
2025-07-22 16:26:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x58, 当前=0x63, 误差=11
2025-07-22 16:26:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x58, 当前=0x63, 误差=11
2025-07-22 16:26:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x58, 当前=0x63, 误差=11
2025-07-22 16:26:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x58, 当前=0x63, 误差=11
2025-07-22 16:26:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x58, 当前=0x63, 误差=11
2025-07-22 16:26:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x58, 当前=0x63, 误差=11
2025-07-22 16:26:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x58, 当前=0x63, 误差=11
2025-07-22 16:26:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x58, 当前=0x63, 误差=11
2025-07-22 16:26:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:26:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x58, 当前=0x63, 误差=11
2025-07-22 16:26:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x58, 当前=0x63, 误差=11
2025-07-22 16:26:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
