2025-07-22 16:18:53 [INFO] logger_config.py:102 - 日志系统初始化完成，日志文件: /home/<USER>/Spatial_AI/logs/gripper_485/gripper_485_1_20250722_161853.log
2025-07-22 16:18:53 [INFO] Gripper_485.py:53 - 初始化RS485夹爪 - 端口: /dev/ttyUSB1, 节点ID: 1, 波特率: 115200
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:92 - 启动RS485消息接收线程
2025-07-22 16:18:53 [INFO] Gripper_485.py:96 - RS485消息接收线程已启动
2025-07-22 16:18:53 [INFO] Gripper_485.py:53 - 初始化RS485夹爪 - 端口: /dev/ttyUSB2, 节点ID: 1, 波特率: 115200
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:92 - 启动RS485消息接收线程
2025-07-22 16:18:53 [INFO] Gripper_485.py:96 - RS485消息接收线程已启动
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:55 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:18:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7E
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7D
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7B
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7A
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x78
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x76
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x73
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x71
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x6E
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x6C
2025-07-22 16:18:55 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x6C, 误差=8
2025-07-22 16:18:55 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:18:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:55 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x6C, 误差=8
2025-07-22 16:18:55 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:18:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:18:55 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x6C, 误差=8
2025-07-22 16:18:55 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:18:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:55 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x6C, 误差=8
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:55 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:18:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:18:55 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x6C, 误差=8
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:55 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:18:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:55 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x6C, 误差=8
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:55 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:18:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:18:55 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x6C, 误差=8
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:55 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:18:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:55 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x6C, 误差=8
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:55 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:18:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:55 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x6C, 误差=8
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:55 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:18:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:55 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x6C, 误差=8
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:55 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:18:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:55 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x6C, 误差=8
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:55 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:18:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:55 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x6C, 误差=8
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:55 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:18:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:18:55 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x6C, 误差=8
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:55 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:18:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:18:55 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x6C, 误差=8
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:55 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:18:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:55 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x6C, 误差=8
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:55 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:18:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:55 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x6C, 误差=8
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:55 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:18:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:55 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x6C, 误差=8
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:55 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:18:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:18:55 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x6C, 误差=8
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:55 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:18:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:55 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x6C, 误差=8
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:18:55 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:18:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:55 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:18:55 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:18:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:18:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:18:56 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:56 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:18:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:18:56 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:18:56 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:18:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:56 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:56 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:18:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:56 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x63
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:56 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:18:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:56 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:56 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:18:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:56 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:56 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:18:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:18:56 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:56 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:18:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:56 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:56 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:18:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:18:56 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:56 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:18:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:18:56 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:56 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:18:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:18:56 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:56 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:18:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:56 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:56 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:18:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:56 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:56 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:18:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:56 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x64, 实际=0x63, 误差=1
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:56 [INFO] Gripper_485.py:386 - 移动到位置 0x32
2025-07-22 16:18:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x32, 力矩=0xFF
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:56 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:57 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:58 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:18:59 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:00 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x63, 误差=49
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:01 [WARNING] Gripper_485.py:255 - 等待回复超时，节点ID: 1
2025-07-22 16:19:01 [WARNING] Gripper_485.py:257 - 最后状态: 目标=0x32, 实际=0x63
2025-07-22 16:19:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 16:19:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x31
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x31
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x31
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x30
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x2F
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x2E
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x2D
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x2B
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x29
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x27
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x24
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x21
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x1E
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x1B
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x17
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x14
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x11
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x0E
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x0B
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x09
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x07
2025-07-22 16:19:01 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x00, 实际=0x07, 误差=7
2025-07-22 16:19:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 16:19:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x05
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:01 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x00, 实际=0x05, 误差=5
2025-07-22 16:19:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 16:19:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x03
2025-07-22 16:19:01 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x00, 实际=0x03, 误差=3
2025-07-22 16:19:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 16:19:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x02
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:19:01 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x00, 实际=0x02, 误差=2
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x01
2025-07-22 16:19:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 16:19:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-22 16:19:01 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x00, 实际=0x00, 误差=0
2025-07-22 16:19:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 16:19:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:01 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x00, 实际=0x00, 误差=0
2025-07-22 16:19:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 16:19:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:19:01 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x00, 实际=0x00, 误差=0
2025-07-22 16:19:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 16:19:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:01 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x00, 实际=0x00, 误差=0
2025-07-22 16:19:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 16:19:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:01 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x00, 实际=0x00, 误差=0
2025-07-22 16:19:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 16:19:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:19:01 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x00, 实际=0x00, 误差=0
2025-07-22 16:19:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 16:19:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:01 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x00, 实际=0x00, 误差=0
2025-07-22 16:19:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 16:19:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:01 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x00, 实际=0x00, 误差=0
2025-07-22 16:19:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 16:19:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:19:01 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x00, 实际=0x00, 误差=0
2025-07-22 16:19:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 16:19:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:01 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x00, 实际=0x00, 误差=0
2025-07-22 16:19:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 16:19:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:01 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x00, 实际=0x00, 误差=0
2025-07-22 16:19:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 16:19:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:01 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x00, 实际=0x00, 误差=0
2025-07-22 16:19:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 16:19:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:19:01 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x00, 实际=0x00, 误差=0
2025-07-22 16:19:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 16:19:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:01 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x00, 实际=0x00, 误差=0
2025-07-22 16:19:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 16:19:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:01 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x00, 实际=0x00, 误差=0
2025-07-22 16:19:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 16:19:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 16:19:01 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:19:02 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x00, 实际=0x00, 误差=0
2025-07-22 16:19:02 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 16:19:02 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:02 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x00, 实际=0x00, 误差=0
2025-07-22 16:19:02 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 16:19:02 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:02 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x00, 实际=0x00, 误差=0
2025-07-22 16:19:02 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 16:19:02 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:19:02 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x00, 实际=0x00, 误差=0
2025-07-22 16:19:02 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 16:19:02 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:02 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x00, 实际=0x00, 误差=0
2025-07-22 16:19:02 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 16:19:02 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:02 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x00, 实际=0x00, 误差=0
2025-07-22 16:19:02 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 16:19:02 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:02 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x00, 实际=0x00, 误差=0
2025-07-22 16:19:02 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 16:19:02 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:19:02 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x00, 实际=0x00, 误差=0
2025-07-22 16:19:02 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 16:19:02 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:02 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x00, 实际=0x00, 误差=0
2025-07-22 16:19:02 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 16:19:02 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:02 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x00, 实际=0x00, 误差=0
2025-07-22 16:19:02 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 16:19:02 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:19:02 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x00, 实际=0x00, 误差=0
2025-07-22 16:19:02 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 16:19:02 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:02 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x00, 实际=0x00, 误差=0
2025-07-22 16:19:02 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 16:19:02 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:02 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x00, 实际=0x00, 误差=0
2025-07-22 16:19:02 [INFO] Gripper_485.py:386 - 移动到位置 0x15
2025-07-22 16:19:02 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x15, 力矩=0xFF
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x01
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x02
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x04
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x06
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x08
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x0A
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x0C
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x0E
2025-07-22 16:19:02 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x15, 实际=0x0E, 误差=7
2025-07-22 16:19:02 [INFO] Gripper_485.py:386 - 移动到位置 0x32
2025-07-22 16:19:02 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x32, 力矩=0xFF
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x10
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x11
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x13
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x13
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x14
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x14
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x14
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x14
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:02 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:03 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:04 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:05 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:06 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x32, 当前=0x14, 误差=30
2025-07-22 16:19:07 [WARNING] Gripper_485.py:255 - 等待回复超时，节点ID: 1
2025-07-22 16:19:07 [WARNING] Gripper_485.py:257 - 最后状态: 目标=0x32, 实际=0x14
2025-07-22 16:19:07 [INFO] Gripper_485.py:386 - 移动到位置 0x59
2025-07-22 16:19:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x59, 力矩=0xFF
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:07 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:08 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:09 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:10 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x59, 当前=0x14, 误差=69
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:12 [WARNING] Gripper_485.py:255 - 等待回复超时，节点ID: 1
2025-07-22 16:19:12 [WARNING] Gripper_485.py:257 - 最后状态: 目标=0x59, 实际=0x14
2025-07-22 16:19:12 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:19:12 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:17 [WARNING] Gripper_485.py:255 - 等待回复超时，节点ID: 1
2025-07-22 16:19:17 [WARNING] Gripper_485.py:257 - 最后状态: 目标=0x64, 实际=0x14
2025-07-22 16:19:17 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:17 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 16:19:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 16:19:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:19 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0x64, 当前=0x14, 误差=80
2025-07-22 16:19:19 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
