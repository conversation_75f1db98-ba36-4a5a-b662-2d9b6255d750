2025-07-22 21:49:22 [INFO] logger_config.py:102 - 日志系统初始化完成，日志文件: /home/<USER>/Spatial_AI/logs/gripper_485/gripper_485_1_20250722_214922.log
2025-07-22 21:49:22 [INFO] Gripper_485.py:53 - 初始化RS485夹爪 - 端口: /dev/ttyUSB1, 节点ID: 1, 波特率: 115200
2025-07-22 21:49:22 [DEBUG] Gripper_485.py:92 - 启动RS485消息接收线程
2025-07-22 21:49:22 [INFO] Gripper_485.py:96 - RS485消息接收线程已启动
2025-07-22 21:49:22 [INFO] Gripper_485.py:53 - 初始化RS485夹爪 - 端口: /dev/ttyUSB2, 节点ID: 1, 波特率: 115200
2025-07-22 21:49:22 [DEBUG] Gripper_485.py:92 - 启动RS485消息接收线程
2025-07-22 21:49:22 [INFO] Gripper_485.py:96 - RS485消息接收线程已启动
2025-07-22 21:49:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:49:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:49:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:49:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:49:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFD
2025-07-22 21:49:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFD
2025-07-22 21:49:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFC
2025-07-22 21:49:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFC
2025-07-22 21:49:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xFA
2025-07-22 21:49:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFA
2025-07-22 21:49:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xF7
2025-07-22 21:49:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xF7
2025-07-22 21:49:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xF4
2025-07-22 21:49:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xF4
2025-07-22 21:49:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xF0
2025-07-22 21:49:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xF0
2025-07-22 21:49:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xEC
2025-07-22 21:49:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xEB
2025-07-22 21:49:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xE7
2025-07-22 21:49:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xE6
2025-07-22 21:49:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xE2
2025-07-22 21:49:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xE1
2025-07-22 21:49:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xDB
2025-07-22 21:49:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xDB
2025-07-22 21:49:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xD4
2025-07-22 21:49:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xD5
2025-07-22 21:49:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xCD
2025-07-22 21:49:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xCE
2025-07-22 21:49:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xC6
2025-07-22 21:49:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xC7
2025-07-22 21:49:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xBF
2025-07-22 21:49:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xC0
2025-07-22 21:49:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xB9
2025-07-22 21:49:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xB8
2025-07-22 21:49:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xB2
2025-07-22 21:49:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xB1
2025-07-22 21:49:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xAB
2025-07-22 21:49:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xAA
2025-07-22 21:49:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xA4
2025-07-22 21:49:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xA3
2025-07-22 21:49:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x9E
2025-07-22 21:49:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x9D
2025-07-22 21:49:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x98
2025-07-22 21:49:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x98
2025-07-22 21:49:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x93
2025-07-22 21:49:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x93
2025-07-22 21:49:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x8E
2025-07-22 21:49:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x8F
2025-07-22 21:49:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x8A
2025-07-22 21:49:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x8B
2025-07-22 21:49:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x87
2025-07-22 21:49:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x87
2025-07-22 21:49:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x85
2025-07-22 21:49:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x84
2025-07-22 21:49:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x83
2025-07-22 21:49:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x82
2025-07-22 21:49:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x81
2025-07-22 21:49:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x81
2025-07-22 21:49:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-22 21:49:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-22 21:49:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:49:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:49:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:49:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:49:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:49:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:49:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:49:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:49:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:49:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:49:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:49:22 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:23 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:24 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:24 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 5, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:24 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:24 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 15, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x81
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x81
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x83
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x84
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x86
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x88
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x8A
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x8C
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x8F
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x92
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x96
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x81
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x99
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x82
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x9D
2025-07-22 21:49:24 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:24 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 157, 'velocity': 214, 'force': 114, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 21:49:24 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:24 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 130, 'velocity': 60, 'force': 126, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xA1
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xA3
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xA6
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xAA
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xAF
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xB4
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xB9
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xBD
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xC2
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xC7
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xCC
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xA4
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xD0
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xD5
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xAB
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xDA
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xB2
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xDE
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xE2
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xB9
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xE6
2025-07-22 21:49:24 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:24 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 230, 'velocity': 198, 'force': 52, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 21:49:24 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:24 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 185, 'velocity': 254, 'force': 108, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xE9
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xED
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xF2
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xF5
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xF8
2025-07-22 21:49:24 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xFB
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xFC
2025-07-22 21:49:25 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:25 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 233, 'velocity': 185, 'force': 61, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 21:49:25 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:25 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 252, 'velocity': 60, 'force': 34, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xFD
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xFE
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xFE
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xFE
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xFF
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xFF
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xFF
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xFF
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xFF
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xFF
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xFF
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xFF
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xFF
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xFF
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xFF
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xFF
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xFF
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xFF
2025-07-22 21:49:25 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:25 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 233, 'velocity': 185, 'force': 61, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 21:49:25 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:25 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 255, 'velocity': 0, 'force': 21, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 21:49:25 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:25 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 233, 'velocity': 185, 'force': 61, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 21:49:25 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:25 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 255, 'velocity': 0, 'force': 21, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 21:49:25 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:25 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 233, 'velocity': 185, 'force': 61, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 21:49:25 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:25 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 255, 'velocity': 0, 'force': 21, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:49:25 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:25 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 233, 'velocity': 185, 'force': 61, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 21:49:25 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:25 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 32, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:49:25 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:25 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 233, 'velocity': 185, 'force': 61, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 21:49:25 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:25 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 35, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:49:25 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:25 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 233, 'velocity': 185, 'force': 61, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 21:49:25 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:25 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:49:25 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:25 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 233, 'velocity': 185, 'force': 61, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 21:49:25 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:25 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:25 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:26 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:26 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:26 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 233, 'velocity': 185, 'force': 61, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 21:49:26 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:26 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:26 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:26 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:26 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:26 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 233, 'velocity': 185, 'force': 61, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 21:49:26 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:26 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:26 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:26 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:26 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:26 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 233, 'velocity': 185, 'force': 61, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 21:49:26 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:26 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:26 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:26 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:26 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:26 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 233, 'velocity': 185, 'force': 61, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 21:49:26 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:26 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:26 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:26 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:26 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:26 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 233, 'velocity': 185, 'force': 61, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 21:49:26 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:26 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:26 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:26 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:26 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:26 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 233, 'velocity': 185, 'force': 61, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 21:49:26 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:26 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:26 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:26 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:26 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:26 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 233, 'velocity': 185, 'force': 61, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 21:49:26 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:26 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:26 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:26 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:26 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:26 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 233, 'velocity': 185, 'force': 61, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 21:49:26 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:26 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:26 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:26 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:26 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:26 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 233, 'velocity': 185, 'force': 61, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 21:49:26 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:26 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:26 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:27 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:27 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:27 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 233, 'velocity': 185, 'force': 61, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 21:49:27 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:27 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:27 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:27 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:27 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:27 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 233, 'velocity': 185, 'force': 61, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 21:49:27 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:27 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:27 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:27 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:27 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:27 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 233, 'velocity': 185, 'force': 61, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 21:49:27 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:27 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:27 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:49:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:49:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:49:27 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:27 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:27 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:27 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:27 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:27 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:49:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:49:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:49:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:49:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:49:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:49:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:49:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:49:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:49:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:49:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:49:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:49:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:49:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:49:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:49:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:49:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:49:27 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:27 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:27 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:27 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:27 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:27 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:49:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:49:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:49:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:49:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:49:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:49:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:49:27 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:49:27 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:27 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:27 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:27 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:27 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:27 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:27 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:27 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:27 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:27 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:27 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:27 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:28 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:28 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:28 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:28 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:28 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:28 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:28 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:28 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:28 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:28 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:28 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:28 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:28 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:28 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:28 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:28 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:28 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:28 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:28 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:28 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:28 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:28 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:28 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:28 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:28 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:28 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:28 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:28 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:28 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:28 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:28 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:28 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:28 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:28 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:28 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:28 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:28 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:28 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:28 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:28 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:28 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:28 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:28 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:28 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:28 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:28 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:28 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:28 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:28 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:28 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:28 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:28 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:28 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:28 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:28 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:28 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:28 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:28 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:28 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:28 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:28 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:28 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:28 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:28 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:28 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:28 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:29 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:29 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:29 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:29 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:29 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:29 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:29 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:29 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:29 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:29 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:29 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:29 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:29 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:29 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:29 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:40 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 42, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:40 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:49:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 26, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 26, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 26, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 26, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 26, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 26, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 26, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 26, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 26, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 26, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 26, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 26, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 26, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 26, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 26, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 26, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 26, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 26, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 26, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 26, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 26, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 26, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 26, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 26, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 26, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 26, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 26, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 26, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 26, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 26, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 26, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 26, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 26, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 26, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 26, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 26, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 26, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 26, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 26, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 26, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 26, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 26, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 26, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:49:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:49:41 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:49:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
