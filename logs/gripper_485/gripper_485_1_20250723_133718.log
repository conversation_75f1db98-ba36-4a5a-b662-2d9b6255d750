2025-07-23 13:37:18 [INFO] logger_config.py:102 - 日志系统初始化完成，日志文件: /home/<USER>/Spatial_AI/logs/gripper_485/gripper_485_1_20250723_133718.log
2025-07-23 13:37:18 [INFO] Gripper_485.py:53 - 初始化RS485夹爪 - 端口: /dev/ttyUSB2, 节点ID: 1, 波特率: 115200
2025-07-23 13:37:18 [DEBUG] Gripper_485.py:92 - 启动RS485消息接收线程
2025-07-23 13:37:18 [INFO] Gripper_485.py:96 - RS485消息接收线程已启动
2025-07-23 13:37:18 [INFO] Gripper_485.py:53 - 初始化RS485夹爪 - 端口: /dev/ttyUSB1, 节点ID: 1, 波特率: 115200
2025-07-23 13:37:18 [DEBUG] Gripper_485.py:92 - 启动RS485消息接收线程
2025-07-23 13:37:18 [INFO] Gripper_485.py:96 - RS485消息接收线程已启动
2025-07-23 13:37:36 [INFO] Gripper_485.py:386 - 移动到位置 0xEF
2025-07-23 13:37:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xEF, 力矩=0xFF
2025-07-23 13:37:36 [INFO] Gripper_485.py:386 - 移动到位置 0xDF
2025-07-23 13:37:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xDF, 力矩=0xFF
2025-07-23 13:37:36 [INFO] Gripper_485.py:386 - 移动到位置 0xC3
2025-07-23 13:37:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xC3, 力矩=0xFF
2025-07-23 13:37:36 [INFO] Gripper_485.py:386 - 移动到位置 0xB7
2025-07-23 13:37:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xB7, 力矩=0xFF
2025-07-23 13:37:36 [INFO] Gripper_485.py:386 - 移动到位置 0xAB
2025-07-23 13:37:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xAB, 力矩=0xFF
2025-07-23 13:37:36 [INFO] Gripper_485.py:386 - 移动到位置 0x7D
2025-07-23 13:37:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x7D, 力矩=0xFF
2025-07-23 13:37:36 [INFO] Gripper_485.py:386 - 移动到位置 0x62
2025-07-23 13:37:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x62, 力矩=0xFF
2025-07-23 13:37:36 [INFO] Gripper_485.py:386 - 移动到位置 0x08
2025-07-23 13:37:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x08, 力矩=0xFF
2025-07-23 13:37:36 [INFO] Gripper_485.py:386 - 移动到位置 0x0B
2025-07-23 13:37:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0B, 力矩=0xFF
2025-07-23 13:37:36 [INFO] Gripper_485.py:386 - 移动到位置 0x08
2025-07-23 13:37:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x08, 力矩=0xFF
2025-07-23 13:37:36 [INFO] Gripper_485.py:386 - 移动到位置 0x09
2025-07-23 13:37:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x09, 力矩=0xFF
2025-07-23 13:37:37 [INFO] Gripper_485.py:386 - 移动到位置 0x0B
2025-07-23 13:37:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0B, 力矩=0xFF
2025-07-23 13:37:37 [INFO] Gripper_485.py:386 - 移动到位置 0x0E
2025-07-23 13:37:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0E, 力矩=0xFF
2025-07-23 13:37:37 [INFO] Gripper_485.py:386 - 移动到位置 0x10
2025-07-23 13:37:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x10, 力矩=0xFF
2025-07-23 13:37:37 [INFO] Gripper_485.py:386 - 移动到位置 0x1B
2025-07-23 13:37:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x1B, 力矩=0xFF
2025-07-23 13:37:37 [INFO] Gripper_485.py:386 - 移动到位置 0x12
2025-07-23 13:37:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x12, 力矩=0xFF
2025-07-23 13:37:37 [INFO] Gripper_485.py:386 - 移动到位置 0x0E
2025-07-23 13:37:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0E, 力矩=0xFF
2025-07-23 13:37:37 [INFO] Gripper_485.py:386 - 移动到位置 0x0D
2025-07-23 13:37:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0D, 力矩=0xFF
2025-07-23 13:37:37 [INFO] Gripper_485.py:386 - 移动到位置 0x0D
2025-07-23 13:37:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0D, 力矩=0xFF
2025-07-23 13:37:37 [INFO] Gripper_485.py:386 - 移动到位置 0x0C
2025-07-23 13:37:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0C, 力矩=0xFF
2025-07-23 13:37:37 [INFO] Gripper_485.py:386 - 移动到位置 0x0E
2025-07-23 13:37:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0E, 力矩=0xFF
2025-07-23 13:37:37 [INFO] Gripper_485.py:386 - 移动到位置 0x0E
2025-07-23 13:37:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0E, 力矩=0xFF
2025-07-23 13:37:37 [INFO] Gripper_485.py:386 - 移动到位置 0x0E
2025-07-23 13:37:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0E, 力矩=0xFF
2025-07-23 13:37:37 [INFO] Gripper_485.py:386 - 移动到位置 0x0F
2025-07-23 13:37:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0F, 力矩=0xFF
2025-07-23 13:37:37 [INFO] Gripper_485.py:386 - 移动到位置 0x0F
2025-07-23 13:37:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0F, 力矩=0xFF
2025-07-23 13:37:37 [INFO] Gripper_485.py:386 - 移动到位置 0x10
2025-07-23 13:37:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x10, 力矩=0xFF
2025-07-23 13:37:37 [INFO] Gripper_485.py:386 - 移动到位置 0x12
2025-07-23 13:37:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x12, 力矩=0xFF
2025-07-23 13:37:37 [INFO] Gripper_485.py:386 - 移动到位置 0x13
2025-07-23 13:37:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x13, 力矩=0xFF
2025-07-23 13:37:37 [INFO] Gripper_485.py:386 - 移动到位置 0x0F
2025-07-23 13:37:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0F, 力矩=0xFF
2025-07-23 13:37:37 [INFO] Gripper_485.py:386 - 移动到位置 0x0F
2025-07-23 13:37:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0F, 力矩=0xFF
2025-07-23 13:37:37 [INFO] Gripper_485.py:386 - 移动到位置 0x0E
2025-07-23 13:37:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0E, 力矩=0xFF
2025-07-23 13:37:37 [INFO] Gripper_485.py:386 - 移动到位置 0x0C
2025-07-23 13:37:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0C, 力矩=0xFF
2025-07-23 13:37:37 [INFO] Gripper_485.py:386 - 移动到位置 0x0C
2025-07-23 13:37:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0C, 力矩=0xFF
2025-07-23 13:37:37 [INFO] Gripper_485.py:386 - 移动到位置 0x09
2025-07-23 13:37:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x09, 力矩=0xFF
2025-07-23 13:37:37 [INFO] Gripper_485.py:386 - 移动到位置 0x08
2025-07-23 13:37:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x08, 力矩=0xFF
2025-07-23 13:37:37 [INFO] Gripper_485.py:386 - 移动到位置 0x07
2025-07-23 13:37:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x07, 力矩=0xFF
2025-07-23 13:37:37 [INFO] Gripper_485.py:386 - 移动到位置 0x07
2025-07-23 13:37:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x07, 力矩=0xFF
2025-07-23 13:37:37 [INFO] Gripper_485.py:386 - 移动到位置 0x07
2025-07-23 13:37:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x07, 力矩=0xFF
2025-07-23 13:37:37 [INFO] Gripper_485.py:386 - 移动到位置 0x09
2025-07-23 13:37:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x09, 力矩=0xFF
2025-07-23 13:37:37 [INFO] Gripper_485.py:386 - 移动到位置 0x09
2025-07-23 13:37:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x09, 力矩=0xFF
2025-07-23 13:37:37 [INFO] Gripper_485.py:386 - 移动到位置 0x0D
2025-07-23 13:37:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0D, 力矩=0xFF
2025-07-23 13:37:37 [INFO] Gripper_485.py:386 - 移动到位置 0x0D
2025-07-23 13:37:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0D, 力矩=0xFF
2025-07-23 13:37:37 [INFO] Gripper_485.py:386 - 移动到位置 0x0C
2025-07-23 13:37:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0C, 力矩=0xFF
2025-07-23 13:37:37 [INFO] Gripper_485.py:386 - 移动到位置 0x0B
2025-07-23 13:37:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0B, 力矩=0xFF
2025-07-23 13:37:37 [INFO] Gripper_485.py:386 - 移动到位置 0x0B
2025-07-23 13:37:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0B, 力矩=0xFF
2025-07-23 13:37:37 [INFO] Gripper_485.py:386 - 移动到位置 0x0B
2025-07-23 13:37:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0B, 力矩=0xFF
2025-07-23 13:37:37 [INFO] Gripper_485.py:386 - 移动到位置 0x0B
2025-07-23 13:37:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0B, 力矩=0xFF
2025-07-23 13:37:37 [INFO] Gripper_485.py:386 - 移动到位置 0x0C
2025-07-23 13:37:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0C, 力矩=0xFF
2025-07-23 13:37:37 [INFO] Gripper_485.py:386 - 移动到位置 0x0C
2025-07-23 13:37:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0C, 力矩=0xFF
2025-07-23 13:37:37 [INFO] Gripper_485.py:386 - 移动到位置 0x0F
2025-07-23 13:37:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0F, 力矩=0xFF
2025-07-23 13:37:37 [INFO] Gripper_485.py:386 - 移动到位置 0x0C
2025-07-23 13:37:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0C, 力矩=0xFF
2025-07-23 13:37:37 [INFO] Gripper_485.py:386 - 移动到位置 0x04
2025-07-23 13:37:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x04, 力矩=0xFF
2025-07-23 13:37:37 [INFO] Gripper_485.py:386 - 移动到位置 0x01
2025-07-23 13:37:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x01, 力矩=0xFF
2025-07-23 13:37:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:37 [INFO] Gripper_485.py:386 - 移动到位置 0x01
2025-07-23 13:37:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x01, 力矩=0xFF
2025-07-23 13:37:37 [INFO] Gripper_485.py:386 - 移动到位置 0x07
2025-07-23 13:37:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x07, 力矩=0xFF
2025-07-23 13:37:37 [INFO] Gripper_485.py:386 - 移动到位置 0x0B
2025-07-23 13:37:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0B, 力矩=0xFF
2025-07-23 13:37:37 [INFO] Gripper_485.py:386 - 移动到位置 0x20
2025-07-23 13:37:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x20, 力矩=0xFF
2025-07-23 13:37:37 [INFO] Gripper_485.py:386 - 移动到位置 0x40
2025-07-23 13:37:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x40, 力矩=0xFF
2025-07-23 13:37:37 [INFO] Gripper_485.py:386 - 移动到位置 0x5D
2025-07-23 13:37:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x5D, 力矩=0xFF
2025-07-23 13:37:37 [INFO] Gripper_485.py:386 - 移动到位置 0x9D
2025-07-23 13:37:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x9D, 力矩=0xFF
2025-07-23 13:37:37 [INFO] Gripper_485.py:386 - 移动到位置 0xBC
2025-07-23 13:37:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xBC, 力矩=0xFF
2025-07-23 13:37:37 [INFO] Gripper_485.py:386 - 移动到位置 0xEF
2025-07-23 13:37:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xEF, 力矩=0xFF
2025-07-23 13:37:38 [INFO] Gripper_485.py:386 - 移动到位置 0xE9
2025-07-23 13:37:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xE9, 力矩=0xFF
2025-07-23 13:37:38 [INFO] Gripper_485.py:386 - 移动到位置 0xD4
2025-07-23 13:37:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xD4, 力矩=0xFF
2025-07-23 13:37:38 [INFO] Gripper_485.py:386 - 移动到位置 0xAB
2025-07-23 13:37:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xAB, 力矩=0xFF
2025-07-23 13:37:38 [INFO] Gripper_485.py:386 - 移动到位置 0x07
2025-07-23 13:37:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x07, 力矩=0xFF
2025-07-23 13:37:38 [INFO] Gripper_485.py:386 - 移动到位置 0x03
2025-07-23 13:37:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x03, 力矩=0xFF
2025-07-23 13:37:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:38 [INFO] Gripper_485.py:386 - 移动到位置 0x02
2025-07-23 13:37:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x02, 力矩=0xFF
2025-07-23 13:37:38 [INFO] Gripper_485.py:386 - 移动到位置 0x03
2025-07-23 13:37:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x03, 力矩=0xFF
2025-07-23 13:37:38 [INFO] Gripper_485.py:386 - 移动到位置 0x06
2025-07-23 13:37:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x06, 力矩=0xFF
2025-07-23 13:37:38 [INFO] Gripper_485.py:386 - 移动到位置 0x08
2025-07-23 13:37:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x08, 力矩=0xFF
2025-07-23 13:37:38 [INFO] Gripper_485.py:386 - 移动到位置 0x0A
2025-07-23 13:37:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0A, 力矩=0xFF
2025-07-23 13:37:38 [INFO] Gripper_485.py:386 - 移动到位置 0x0D
2025-07-23 13:37:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0D, 力矩=0xFF
2025-07-23 13:37:38 [INFO] Gripper_485.py:386 - 移动到位置 0x0C
2025-07-23 13:37:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0C, 力矩=0xFF
2025-07-23 13:37:38 [INFO] Gripper_485.py:386 - 移动到位置 0x0B
2025-07-23 13:37:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0B, 力矩=0xFF
2025-07-23 13:37:38 [INFO] Gripper_485.py:386 - 移动到位置 0x0B
2025-07-23 13:37:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0B, 力矩=0xFF
2025-07-23 13:37:38 [INFO] Gripper_485.py:386 - 移动到位置 0x0C
2025-07-23 13:37:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0C, 力矩=0xFF
2025-07-23 13:37:38 [INFO] Gripper_485.py:386 - 移动到位置 0x0C
2025-07-23 13:37:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0C, 力矩=0xFF
2025-07-23 13:37:38 [INFO] Gripper_485.py:386 - 移动到位置 0x0D
2025-07-23 13:37:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0D, 力矩=0xFF
2025-07-23 13:37:38 [INFO] Gripper_485.py:386 - 移动到位置 0x10
2025-07-23 13:37:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x10, 力矩=0xFF
2025-07-23 13:37:38 [INFO] Gripper_485.py:386 - 移动到位置 0x1C
2025-07-23 13:37:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x1C, 力矩=0xFF
2025-07-23 13:37:38 [INFO] Gripper_485.py:386 - 移动到位置 0x1F
2025-07-23 13:37:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x1F, 力矩=0xFF
2025-07-23 13:37:38 [INFO] Gripper_485.py:386 - 移动到位置 0x28
2025-07-23 13:37:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x28, 力矩=0xFF
2025-07-23 13:37:38 [INFO] Gripper_485.py:386 - 移动到位置 0x2F
2025-07-23 13:37:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x2F, 力矩=0xFF
2025-07-23 13:37:38 [INFO] Gripper_485.py:386 - 移动到位置 0x35
2025-07-23 13:37:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x35, 力矩=0xFF
2025-07-23 13:37:38 [INFO] Gripper_485.py:386 - 移动到位置 0x3D
2025-07-23 13:37:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x3D, 力矩=0xFF
2025-07-23 13:37:38 [INFO] Gripper_485.py:386 - 移动到位置 0x40
2025-07-23 13:37:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x40, 力矩=0xFF
2025-07-23 13:37:38 [INFO] Gripper_485.py:386 - 移动到位置 0x40
2025-07-23 13:37:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x40, 力矩=0xFF
2025-07-23 13:37:38 [INFO] Gripper_485.py:386 - 移动到位置 0x3F
2025-07-23 13:37:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x3F, 力矩=0xFF
2025-07-23 13:37:38 [INFO] Gripper_485.py:386 - 移动到位置 0x3A
2025-07-23 13:37:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x3A, 力矩=0xFF
2025-07-23 13:37:38 [INFO] Gripper_485.py:386 - 移动到位置 0x20
2025-07-23 13:37:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x20, 力矩=0xFF
2025-07-23 13:37:38 [INFO] Gripper_485.py:386 - 移动到位置 0x12
2025-07-23 13:37:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x12, 力矩=0xFF
2025-07-23 13:37:38 [INFO] Gripper_485.py:386 - 移动到位置 0x0E
2025-07-23 13:37:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0E, 力矩=0xFF
2025-07-23 13:37:38 [INFO] Gripper_485.py:386 - 移动到位置 0x0F
2025-07-23 13:37:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0F, 力矩=0xFF
2025-07-23 13:37:38 [INFO] Gripper_485.py:386 - 移动到位置 0x0F
2025-07-23 13:37:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0F, 力矩=0xFF
2025-07-23 13:37:38 [INFO] Gripper_485.py:386 - 移动到位置 0x0E
2025-07-23 13:37:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0E, 力矩=0xFF
2025-07-23 13:37:38 [INFO] Gripper_485.py:386 - 移动到位置 0x0F
2025-07-23 13:37:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0F, 力矩=0xFF
2025-07-23 13:37:38 [INFO] Gripper_485.py:386 - 移动到位置 0x0F
2025-07-23 13:37:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0F, 力矩=0xFF
2025-07-23 13:37:38 [INFO] Gripper_485.py:386 - 移动到位置 0x1B
2025-07-23 13:37:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x1B, 力矩=0xFF
2025-07-23 13:37:38 [INFO] Gripper_485.py:386 - 移动到位置 0x23
2025-07-23 13:37:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x23, 力矩=0xFF
2025-07-23 13:37:38 [INFO] Gripper_485.py:386 - 移动到位置 0x2B
2025-07-23 13:37:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x2B, 力矩=0xFF
2025-07-23 13:37:38 [INFO] Gripper_485.py:386 - 移动到位置 0x30
2025-07-23 13:37:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x30, 力矩=0xFF
2025-07-23 13:37:38 [INFO] Gripper_485.py:386 - 移动到位置 0x30
2025-07-23 13:37:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x30, 力矩=0xFF
2025-07-23 13:37:38 [INFO] Gripper_485.py:386 - 移动到位置 0x2B
2025-07-23 13:37:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x2B, 力矩=0xFF
2025-07-23 13:37:38 [INFO] Gripper_485.py:386 - 移动到位置 0x1F
2025-07-23 13:37:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x1F, 力矩=0xFF
2025-07-23 13:37:38 [INFO] Gripper_485.py:386 - 移动到位置 0x0E
2025-07-23 13:37:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0E, 力矩=0xFF
2025-07-23 13:37:38 [INFO] Gripper_485.py:386 - 移动到位置 0x0E
2025-07-23 13:37:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0E, 力矩=0xFF
2025-07-23 13:37:38 [INFO] Gripper_485.py:386 - 移动到位置 0x0D
2025-07-23 13:37:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0D, 力矩=0xFF
2025-07-23 13:37:38 [INFO] Gripper_485.py:386 - 移动到位置 0x0E
2025-07-23 13:37:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0E, 力矩=0xFF
2025-07-23 13:37:38 [INFO] Gripper_485.py:386 - 移动到位置 0x0F
2025-07-23 13:37:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0F, 力矩=0xFF
2025-07-23 13:37:38 [INFO] Gripper_485.py:386 - 移动到位置 0x12
2025-07-23 13:37:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x12, 力矩=0xFF
2025-07-23 13:37:39 [INFO] Gripper_485.py:386 - 移动到位置 0x2E
2025-07-23 13:37:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x2E, 力矩=0xFF
2025-07-23 13:37:39 [INFO] Gripper_485.py:386 - 移动到位置 0x43
2025-07-23 13:37:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x43, 力矩=0xFF
2025-07-23 13:37:39 [INFO] Gripper_485.py:386 - 移动到位置 0x6B
2025-07-23 13:37:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x6B, 力矩=0xFF
2025-07-23 13:37:39 [INFO] Gripper_485.py:386 - 移动到位置 0x82
2025-07-23 13:37:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x82, 力矩=0xFF
2025-07-23 13:37:39 [INFO] Gripper_485.py:386 - 移动到位置 0x9C
2025-07-23 13:37:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x9C, 力矩=0xFF
2025-07-23 13:37:39 [INFO] Gripper_485.py:386 - 移动到位置 0xD0
2025-07-23 13:37:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xD0, 力矩=0xFF
2025-07-23 13:37:39 [INFO] Gripper_485.py:386 - 移动到位置 0xE3
2025-07-23 13:37:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xE3, 力矩=0xFF
2025-07-23 13:37:39 [INFO] Gripper_485.py:386 - 移动到位置 0xFD
2025-07-23 13:37:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFD, 力矩=0xFF
2025-07-23 13:37:39 [INFO] Gripper_485.py:386 - 移动到位置 0xF6
2025-07-23 13:37:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF6, 力矩=0xFF
2025-07-23 13:37:39 [INFO] Gripper_485.py:386 - 移动到位置 0xEB
2025-07-23 13:37:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xEB, 力矩=0xFF
2025-07-23 13:37:39 [INFO] Gripper_485.py:386 - 移动到位置 0xCD
2025-07-23 13:37:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xCD, 力矩=0xFF
2025-07-23 13:37:39 [INFO] Gripper_485.py:386 - 移动到位置 0xBD
2025-07-23 13:37:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xBD, 力矩=0xFF
2025-07-23 13:37:39 [INFO] Gripper_485.py:386 - 移动到位置 0x88
2025-07-23 13:37:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x88, 力矩=0xFF
2025-07-23 13:37:39 [INFO] Gripper_485.py:386 - 移动到位置 0x5D
2025-07-23 13:37:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x5D, 力矩=0xFF
2025-07-23 13:37:39 [INFO] Gripper_485.py:386 - 移动到位置 0x2D
2025-07-23 13:37:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x2D, 力矩=0xFF
2025-07-23 13:37:39 [INFO] Gripper_485.py:386 - 移动到位置 0x0A
2025-07-23 13:37:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0A, 力矩=0xFF
2025-07-23 13:37:39 [INFO] Gripper_485.py:386 - 移动到位置 0x08
2025-07-23 13:37:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x08, 力矩=0xFF
2025-07-23 13:37:39 [INFO] Gripper_485.py:386 - 移动到位置 0x07
2025-07-23 13:37:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x07, 力矩=0xFF
2025-07-23 13:37:39 [INFO] Gripper_485.py:386 - 移动到位置 0x05
2025-07-23 13:37:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x05, 力矩=0xFF
2025-07-23 13:37:39 [INFO] Gripper_485.py:386 - 移动到位置 0x04
2025-07-23 13:37:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x04, 力矩=0xFF
2025-07-23 13:37:40 [INFO] Gripper_485.py:386 - 移动到位置 0x03
2025-07-23 13:37:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x03, 力矩=0xFF
2025-07-23 13:37:40 [INFO] Gripper_485.py:386 - 移动到位置 0x04
2025-07-23 13:37:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x04, 力矩=0xFF
2025-07-23 13:37:40 [INFO] Gripper_485.py:386 - 移动到位置 0x05
2025-07-23 13:37:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x05, 力矩=0xFF
2025-07-23 13:37:40 [INFO] Gripper_485.py:386 - 移动到位置 0x07
2025-07-23 13:37:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x07, 力矩=0xFF
2025-07-23 13:37:40 [INFO] Gripper_485.py:386 - 移动到位置 0x08
2025-07-23 13:37:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x08, 力矩=0xFF
2025-07-23 13:37:40 [INFO] Gripper_485.py:386 - 移动到位置 0x0B
2025-07-23 13:37:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0B, 力矩=0xFF
2025-07-23 13:37:40 [INFO] Gripper_485.py:386 - 移动到位置 0x0B
2025-07-23 13:37:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0B, 力矩=0xFF
2025-07-23 13:37:40 [INFO] Gripper_485.py:386 - 移动到位置 0x0B
2025-07-23 13:37:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0B, 力矩=0xFF
2025-07-23 13:37:40 [INFO] Gripper_485.py:386 - 移动到位置 0x0B
2025-07-23 13:37:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0B, 力矩=0xFF
2025-07-23 13:37:40 [INFO] Gripper_485.py:386 - 移动到位置 0x0A
2025-07-23 13:37:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0A, 力矩=0xFF
2025-07-23 13:37:40 [INFO] Gripper_485.py:386 - 移动到位置 0x0B
2025-07-23 13:37:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0B, 力矩=0xFF
2025-07-23 13:37:40 [INFO] Gripper_485.py:386 - 移动到位置 0x0A
2025-07-23 13:37:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0A, 力矩=0xFF
2025-07-23 13:37:40 [INFO] Gripper_485.py:386 - 移动到位置 0x0A
2025-07-23 13:37:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0A, 力矩=0xFF
2025-07-23 13:37:40 [INFO] Gripper_485.py:386 - 移动到位置 0x0A
2025-07-23 13:37:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0A, 力矩=0xFF
2025-07-23 13:37:40 [INFO] Gripper_485.py:386 - 移动到位置 0x09
2025-07-23 13:37:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x09, 力矩=0xFF
2025-07-23 13:37:40 [INFO] Gripper_485.py:386 - 移动到位置 0x0A
2025-07-23 13:37:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0A, 力矩=0xFF
2025-07-23 13:37:40 [INFO] Gripper_485.py:386 - 移动到位置 0x0A
2025-07-23 13:37:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0A, 力矩=0xFF
2025-07-23 13:37:40 [INFO] Gripper_485.py:386 - 移动到位置 0x0B
2025-07-23 13:37:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0B, 力矩=0xFF
2025-07-23 13:37:40 [INFO] Gripper_485.py:386 - 移动到位置 0x0B
2025-07-23 13:37:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0B, 力矩=0xFF
2025-07-23 13:37:40 [INFO] Gripper_485.py:386 - 移动到位置 0x0C
2025-07-23 13:37:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0C, 力矩=0xFF
2025-07-23 13:37:40 [INFO] Gripper_485.py:386 - 移动到位置 0x0C
2025-07-23 13:37:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0C, 力矩=0xFF
2025-07-23 13:37:40 [INFO] Gripper_485.py:386 - 移动到位置 0x0C
2025-07-23 13:37:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0C, 力矩=0xFF
2025-07-23 13:37:40 [INFO] Gripper_485.py:386 - 移动到位置 0x0C
2025-07-23 13:37:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0C, 力矩=0xFF
2025-07-23 13:37:40 [INFO] Gripper_485.py:386 - 移动到位置 0x0C
2025-07-23 13:37:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0C, 力矩=0xFF
2025-07-23 13:37:40 [INFO] Gripper_485.py:386 - 移动到位置 0x0D
2025-07-23 13:37:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0D, 力矩=0xFF
2025-07-23 13:37:40 [INFO] Gripper_485.py:386 - 移动到位置 0x0D
2025-07-23 13:37:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0D, 力矩=0xFF
2025-07-23 13:37:40 [INFO] Gripper_485.py:386 - 移动到位置 0x0D
2025-07-23 13:37:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0D, 力矩=0xFF
2025-07-23 13:37:40 [INFO] Gripper_485.py:386 - 移动到位置 0x0C
2025-07-23 13:37:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0C, 力矩=0xFF
2025-07-23 13:37:40 [INFO] Gripper_485.py:386 - 移动到位置 0x0D
2025-07-23 13:37:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0D, 力矩=0xFF
2025-07-23 13:37:40 [INFO] Gripper_485.py:386 - 移动到位置 0x0D
2025-07-23 13:37:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0D, 力矩=0xFF
2025-07-23 13:37:40 [INFO] Gripper_485.py:386 - 移动到位置 0x0D
2025-07-23 13:37:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0D, 力矩=0xFF
2025-07-23 13:37:40 [INFO] Gripper_485.py:386 - 移动到位置 0x0D
2025-07-23 13:37:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0D, 力矩=0xFF
2025-07-23 13:37:40 [INFO] Gripper_485.py:386 - 移动到位置 0x0E
2025-07-23 13:37:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0E, 力矩=0xFF
2025-07-23 13:37:40 [INFO] Gripper_485.py:386 - 移动到位置 0x0F
2025-07-23 13:37:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0F, 力矩=0xFF
2025-07-23 13:37:40 [INFO] Gripper_485.py:386 - 移动到位置 0x0E
2025-07-23 13:37:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0E, 力矩=0xFF
2025-07-23 13:37:40 [INFO] Gripper_485.py:386 - 移动到位置 0x0D
2025-07-23 13:37:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0D, 力矩=0xFF
2025-07-23 13:37:40 [INFO] Gripper_485.py:386 - 移动到位置 0x0D
2025-07-23 13:37:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0D, 力矩=0xFF
2025-07-23 13:37:40 [INFO] Gripper_485.py:386 - 移动到位置 0x0D
2025-07-23 13:37:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0D, 力矩=0xFF
2025-07-23 13:37:40 [INFO] Gripper_485.py:386 - 移动到位置 0x0D
2025-07-23 13:37:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0D, 力矩=0xFF
2025-07-23 13:37:40 [INFO] Gripper_485.py:386 - 移动到位置 0x0D
2025-07-23 13:37:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0D, 力矩=0xFF
2025-07-23 13:37:40 [INFO] Gripper_485.py:386 - 移动到位置 0x0E
2025-07-23 13:37:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0E, 力矩=0xFF
2025-07-23 13:37:40 [INFO] Gripper_485.py:386 - 移动到位置 0x0E
2025-07-23 13:37:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0E, 力矩=0xFF
2025-07-23 13:37:40 [INFO] Gripper_485.py:386 - 移动到位置 0x0D
2025-07-23 13:37:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0D, 力矩=0xFF
2025-07-23 13:37:40 [INFO] Gripper_485.py:386 - 移动到位置 0x0D
2025-07-23 13:37:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0D, 力矩=0xFF
2025-07-23 13:37:40 [INFO] Gripper_485.py:386 - 移动到位置 0x0E
2025-07-23 13:37:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0E, 力矩=0xFF
2025-07-23 13:37:40 [INFO] Gripper_485.py:386 - 移动到位置 0x0E
2025-07-23 13:37:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0E, 力矩=0xFF
2025-07-23 13:37:40 [INFO] Gripper_485.py:386 - 移动到位置 0x0F
2025-07-23 13:37:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0F, 力矩=0xFF
2025-07-23 13:37:40 [INFO] Gripper_485.py:386 - 移动到位置 0x0F
2025-07-23 13:37:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0F, 力矩=0xFF
2025-07-23 13:37:40 [INFO] Gripper_485.py:386 - 移动到位置 0x10
2025-07-23 13:37:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x10, 力矩=0xFF
2025-07-23 13:37:40 [INFO] Gripper_485.py:386 - 移动到位置 0x13
2025-07-23 13:37:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x13, 力矩=0xFF
2025-07-23 13:37:40 [INFO] Gripper_485.py:386 - 移动到位置 0x18
2025-07-23 13:37:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x18, 力矩=0xFF
2025-07-23 13:37:40 [INFO] Gripper_485.py:386 - 移动到位置 0x1C
2025-07-23 13:37:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x1C, 力矩=0xFF
2025-07-23 13:37:40 [INFO] Gripper_485.py:386 - 移动到位置 0x2A
2025-07-23 13:37:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x2A, 力矩=0xFF
2025-07-23 13:37:40 [INFO] Gripper_485.py:386 - 移动到位置 0x34
2025-07-23 13:37:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x34, 力矩=0xFF
2025-07-23 13:37:40 [INFO] Gripper_485.py:386 - 移动到位置 0x4C
2025-07-23 13:37:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x4C, 力矩=0xFF
2025-07-23 13:37:40 [INFO] Gripper_485.py:386 - 移动到位置 0x5C
2025-07-23 13:37:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x5C, 力矩=0xFF
2025-07-23 13:37:40 [INFO] Gripper_485.py:386 - 移动到位置 0x6F
2025-07-23 13:37:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x6F, 力矩=0xFF
2025-07-23 13:37:40 [INFO] Gripper_485.py:386 - 移动到位置 0xA8
2025-07-23 13:37:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xA8, 力矩=0xFF
2025-07-23 13:37:40 [INFO] Gripper_485.py:386 - 移动到位置 0xC6
2025-07-23 13:37:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xC6, 力矩=0xFF
2025-07-23 13:37:40 [INFO] Gripper_485.py:386 - 移动到位置 0xF7
2025-07-23 13:37:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF7, 力矩=0xFF
2025-07-23 13:37:47 [INFO] Gripper_485.py:386 - 移动到位置 0xF0
2025-07-23 13:37:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF0, 力矩=0xFF
2025-07-23 13:37:47 [INFO] Gripper_485.py:386 - 移动到位置 0xE9
2025-07-23 13:37:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xE9, 力矩=0xFF
2025-07-23 13:37:47 [INFO] Gripper_485.py:386 - 移动到位置 0xE2
2025-07-23 13:37:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xE2, 力矩=0xFF
2025-07-23 13:37:47 [INFO] Gripper_485.py:386 - 移动到位置 0xD4
2025-07-23 13:37:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xD4, 力矩=0xFF
2025-07-23 13:37:47 [INFO] Gripper_485.py:386 - 移动到位置 0xD0
2025-07-23 13:37:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xD0, 力矩=0xFF
2025-07-23 13:37:47 [INFO] Gripper_485.py:386 - 移动到位置 0xCC
2025-07-23 13:37:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xCC, 力矩=0xFF
2025-07-23 13:37:47 [INFO] Gripper_485.py:386 - 移动到位置 0xC8
2025-07-23 13:37:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xC8, 力矩=0xFF
2025-07-23 13:37:47 [INFO] Gripper_485.py:386 - 移动到位置 0xC5
2025-07-23 13:37:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xC5, 力矩=0xFF
2025-07-23 13:37:47 [INFO] Gripper_485.py:386 - 移动到位置 0xBF
2025-07-23 13:37:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xBF, 力矩=0xFF
2025-07-23 13:37:47 [INFO] Gripper_485.py:386 - 移动到位置 0xBE
2025-07-23 13:37:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xBE, 力矩=0xFF
2025-07-23 13:37:47 [INFO] Gripper_485.py:386 - 移动到位置 0xBF
2025-07-23 13:37:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xBF, 力矩=0xFF
2025-07-23 13:37:47 [INFO] Gripper_485.py:386 - 移动到位置 0xBF
2025-07-23 13:37:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xBF, 力矩=0xFF
2025-07-23 13:37:47 [INFO] Gripper_485.py:386 - 移动到位置 0xBD
2025-07-23 13:37:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xBD, 力矩=0xFF
2025-07-23 13:37:47 [INFO] Gripper_485.py:386 - 移动到位置 0xAE
2025-07-23 13:37:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xAE, 力矩=0xFF
2025-07-23 13:37:47 [INFO] Gripper_485.py:386 - 移动到位置 0xA0
2025-07-23 13:37:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xA0, 力矩=0xFF
2025-07-23 13:37:47 [INFO] Gripper_485.py:386 - 移动到位置 0x98
2025-07-23 13:37:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x98, 力矩=0xFF
2025-07-23 13:37:47 [INFO] Gripper_485.py:386 - 移动到位置 0x8D
2025-07-23 13:37:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x8D, 力矩=0xFF
2025-07-23 13:37:47 [INFO] Gripper_485.py:386 - 移动到位置 0x8A
2025-07-23 13:37:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x8A, 力矩=0xFF
2025-07-23 13:37:47 [INFO] Gripper_485.py:386 - 移动到位置 0x89
2025-07-23 13:37:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x89, 力矩=0xFF
2025-07-23 13:37:47 [INFO] Gripper_485.py:386 - 移动到位置 0x8A
2025-07-23 13:37:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x8A, 力矩=0xFF
2025-07-23 13:37:47 [INFO] Gripper_485.py:386 - 移动到位置 0x93
2025-07-23 13:37:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x93, 力矩=0xFF
2025-07-23 13:37:47 [INFO] Gripper_485.py:386 - 移动到位置 0x9B
2025-07-23 13:37:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x9B, 力矩=0xFF
2025-07-23 13:37:47 [INFO] Gripper_485.py:386 - 移动到位置 0xA2
2025-07-23 13:37:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xA2, 力矩=0xFF
2025-07-23 13:37:47 [INFO] Gripper_485.py:386 - 移动到位置 0xA7
2025-07-23 13:37:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xA7, 力矩=0xFF
2025-07-23 13:37:47 [INFO] Gripper_485.py:386 - 移动到位置 0xBB
2025-07-23 13:37:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xBB, 力矩=0xFF
2025-07-23 13:37:47 [INFO] Gripper_485.py:386 - 移动到位置 0xC5
2025-07-23 13:37:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xC5, 力矩=0xFF
2025-07-23 13:37:47 [INFO] Gripper_485.py:386 - 移动到位置 0xD3
2025-07-23 13:37:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xD3, 力矩=0xFF
2025-07-23 13:37:47 [INFO] Gripper_485.py:386 - 移动到位置 0xD7
2025-07-23 13:37:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xD7, 力矩=0xFF
2025-07-23 13:37:47 [INFO] Gripper_485.py:386 - 移动到位置 0xDB
2025-07-23 13:37:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xDB, 力矩=0xFF
2025-07-23 13:37:47 [INFO] Gripper_485.py:386 - 移动到位置 0xE5
2025-07-23 13:37:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xE5, 力矩=0xFF
2025-07-23 13:37:47 [INFO] Gripper_485.py:386 - 移动到位置 0xE8
2025-07-23 13:37:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xE8, 力矩=0xFF
2025-07-23 13:37:47 [INFO] Gripper_485.py:386 - 移动到位置 0xEB
2025-07-23 13:37:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xEB, 力矩=0xFF
2025-07-23 13:37:47 [INFO] Gripper_485.py:386 - 移动到位置 0xEE
2025-07-23 13:37:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xEE, 力矩=0xFF
2025-07-23 13:37:47 [INFO] Gripper_485.py:386 - 移动到位置 0xF0
2025-07-23 13:37:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF0, 力矩=0xFF
2025-07-23 13:37:47 [INFO] Gripper_485.py:386 - 移动到位置 0xF3
2025-07-23 13:37:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF3, 力矩=0xFF
2025-07-23 13:37:47 [INFO] Gripper_485.py:386 - 移动到位置 0xF3
2025-07-23 13:37:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF3, 力矩=0xFF
2025-07-23 13:37:47 [INFO] Gripper_485.py:386 - 移动到位置 0xF4
2025-07-23 13:37:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF4, 力矩=0xFF
2025-07-23 13:37:47 [INFO] Gripper_485.py:386 - 移动到位置 0xF3
2025-07-23 13:37:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF3, 力矩=0xFF
2025-07-23 13:37:47 [INFO] Gripper_485.py:386 - 移动到位置 0xF3
2025-07-23 13:37:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF3, 力矩=0xFF
2025-07-23 13:37:47 [INFO] Gripper_485.py:386 - 移动到位置 0xF3
2025-07-23 13:37:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF3, 力矩=0xFF
2025-07-23 13:37:47 [INFO] Gripper_485.py:386 - 移动到位置 0xF3
2025-07-23 13:37:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF3, 力矩=0xFF
2025-07-23 13:37:47 [INFO] Gripper_485.py:386 - 移动到位置 0xF3
2025-07-23 13:37:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF3, 力矩=0xFF
2025-07-23 13:37:47 [INFO] Gripper_485.py:386 - 移动到位置 0xF6
2025-07-23 13:37:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF6, 力矩=0xFF
2025-07-23 13:37:48 [INFO] Gripper_485.py:386 - 移动到位置 0xF7
2025-07-23 13:37:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF7, 力矩=0xFF
2025-07-23 13:37:48 [INFO] Gripper_485.py:386 - 移动到位置 0xF7
2025-07-23 13:37:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF7, 力矩=0xFF
2025-07-23 13:37:48 [INFO] Gripper_485.py:386 - 移动到位置 0xFB
2025-07-23 13:37:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFB, 力矩=0xFF
2025-07-23 13:37:54 [INFO] Gripper_485.py:386 - 移动到位置 0xF6
2025-07-23 13:37:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF6, 力矩=0xFF
2025-07-23 13:37:54 [INFO] Gripper_485.py:386 - 移动到位置 0xDE
2025-07-23 13:37:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xDE, 力矩=0xFF
2025-07-23 13:37:54 [INFO] Gripper_485.py:386 - 移动到位置 0xAA
2025-07-23 13:37:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xAA, 力矩=0xFF
2025-07-23 13:37:54 [INFO] Gripper_485.py:386 - 移动到位置 0x90
2025-07-23 13:37:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x90, 力矩=0xFF
2025-07-23 13:37:54 [INFO] Gripper_485.py:386 - 移动到位置 0x79
2025-07-23 13:37:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x79, 力矩=0xFF
2025-07-23 13:37:54 [INFO] Gripper_485.py:386 - 移动到位置 0x71
2025-07-23 13:37:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x71, 力矩=0xFF
2025-07-23 13:37:54 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:54 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:54 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:54 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:54 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:54 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:54 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:54 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:54 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:54 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:54 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:54 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:54 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:54 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:54 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:54 [INFO] Gripper_485.py:386 - 移动到位置 0x03
2025-07-23 13:37:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x03, 力矩=0xFF
2025-07-23 13:37:54 [INFO] Gripper_485.py:386 - 移动到位置 0x04
2025-07-23 13:37:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x04, 力矩=0xFF
2025-07-23 13:37:54 [INFO] Gripper_485.py:386 - 移动到位置 0x06
2025-07-23 13:37:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x06, 力矩=0xFF
2025-07-23 13:37:54 [INFO] Gripper_485.py:386 - 移动到位置 0x06
2025-07-23 13:37:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x06, 力矩=0xFF
2025-07-23 13:37:54 [INFO] Gripper_485.py:386 - 移动到位置 0x05
2025-07-23 13:37:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x05, 力矩=0xFF
2025-07-23 13:37:54 [INFO] Gripper_485.py:386 - 移动到位置 0x01
2025-07-23 13:37:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x01, 力矩=0xFF
2025-07-23 13:37:54 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:54 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:54 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:54 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:54 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:55 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:55 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:55 [INFO] Gripper_485.py:386 - 移动到位置 0x01
2025-07-23 13:37:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x01, 力矩=0xFF
2025-07-23 13:37:55 [INFO] Gripper_485.py:386 - 移动到位置 0x04
2025-07-23 13:37:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x04, 力矩=0xFF
2025-07-23 13:37:55 [INFO] Gripper_485.py:386 - 移动到位置 0x06
2025-07-23 13:37:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x06, 力矩=0xFF
2025-07-23 13:37:55 [INFO] Gripper_485.py:386 - 移动到位置 0x0A
2025-07-23 13:37:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0A, 力矩=0xFF
2025-07-23 13:37:55 [INFO] Gripper_485.py:386 - 移动到位置 0x0E
2025-07-23 13:37:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0E, 力矩=0xFF
2025-07-23 13:37:55 [INFO] Gripper_485.py:386 - 移动到位置 0x38
2025-07-23 13:37:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x38, 力矩=0xFF
2025-07-23 13:37:55 [INFO] Gripper_485.py:386 - 移动到位置 0x55
2025-07-23 13:37:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x55, 力矩=0xFF
2025-07-23 13:37:55 [INFO] Gripper_485.py:386 - 移动到位置 0x78
2025-07-23 13:37:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x78, 力矩=0xFF
2025-07-23 13:37:55 [INFO] Gripper_485.py:386 - 移动到位置 0xCA
2025-07-23 13:37:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xCA, 力矩=0xFF
2025-07-23 13:37:55 [INFO] Gripper_485.py:386 - 移动到位置 0xEC
2025-07-23 13:37:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xEC, 力矩=0xFF
2025-07-23 13:37:56 [INFO] Gripper_485.py:386 - 移动到位置 0xC8
2025-07-23 13:37:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xC8, 力矩=0xFF
2025-07-23 13:37:56 [INFO] Gripper_485.py:386 - 移动到位置 0x7C
2025-07-23 13:37:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x7C, 力矩=0xFF
2025-07-23 13:37:56 [INFO] Gripper_485.py:386 - 移动到位置 0x04
2025-07-23 13:37:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x04, 力矩=0xFF
2025-07-23 13:37:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:57 [INFO] Gripper_485.py:386 - 移动到位置 0x0F
2025-07-23 13:37:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0F, 力矩=0xFF
2025-07-23 13:37:57 [INFO] Gripper_485.py:386 - 移动到位置 0x29
2025-07-23 13:37:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x29, 力矩=0xFF
2025-07-23 13:37:57 [INFO] Gripper_485.py:386 - 移动到位置 0x95
2025-07-23 13:37:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x95, 力矩=0xFF
2025-07-23 13:37:57 [INFO] Gripper_485.py:386 - 移动到位置 0xC2
2025-07-23 13:37:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xC2, 力矩=0xFF
2025-07-23 13:37:57 [INFO] Gripper_485.py:386 - 移动到位置 0xDD
2025-07-23 13:37:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xDD, 力矩=0xFF
2025-07-23 13:37:57 [INFO] Gripper_485.py:386 - 移动到位置 0xFA
2025-07-23 13:37:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFA, 力矩=0xFF
2025-07-23 13:37:57 [INFO] Gripper_485.py:386 - 移动到位置 0xE7
2025-07-23 13:37:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xE7, 力矩=0xFF
2025-07-23 13:37:57 [INFO] Gripper_485.py:386 - 移动到位置 0xBB
2025-07-23 13:37:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xBB, 力矩=0xFF
2025-07-23 13:37:58 [INFO] Gripper_485.py:386 - 移动到位置 0x10
2025-07-23 13:37:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x10, 力矩=0xFF
2025-07-23 13:37:58 [INFO] Gripper_485.py:386 - 移动到位置 0x04
2025-07-23 13:37:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x04, 力矩=0xFF
2025-07-23 13:37:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:37:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:37:58 [INFO] Gripper_485.py:386 - 移动到位置 0x04
2025-07-23 13:37:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x04, 力矩=0xFF
2025-07-23 13:37:58 [INFO] Gripper_485.py:386 - 移动到位置 0x0A
2025-07-23 13:37:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0A, 力矩=0xFF
2025-07-23 13:37:58 [INFO] Gripper_485.py:386 - 移动到位置 0x10
2025-07-23 13:37:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x10, 力矩=0xFF
2025-07-23 13:37:58 [INFO] Gripper_485.py:386 - 移动到位置 0x26
2025-07-23 13:37:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x26, 力矩=0xFF
2025-07-23 13:37:58 [INFO] Gripper_485.py:386 - 移动到位置 0x40
2025-07-23 13:37:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x40, 力矩=0xFF
2025-07-23 13:37:58 [INFO] Gripper_485.py:386 - 移动到位置 0x95
2025-07-23 13:37:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x95, 力矩=0xFF
2025-07-23 13:37:58 [INFO] Gripper_485.py:386 - 移动到位置 0xB8
2025-07-23 13:37:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xB8, 力矩=0xFF
2025-07-23 13:37:58 [INFO] Gripper_485.py:386 - 移动到位置 0xD3
2025-07-23 13:37:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xD3, 力矩=0xFF
2025-07-23 13:37:58 [INFO] Gripper_485.py:386 - 移动到位置 0xF6
2025-07-23 13:37:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF6, 力矩=0xFF
2025-07-23 13:38:00 [INFO] Gripper_485.py:386 - 移动到位置 0xE4
2025-07-23 13:38:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xE4, 力矩=0xFF
2025-07-23 13:38:00 [INFO] Gripper_485.py:386 - 移动到位置 0xD2
2025-07-23 13:38:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xD2, 力矩=0xFF
2025-07-23 13:38:00 [INFO] Gripper_485.py:386 - 移动到位置 0xC0
2025-07-23 13:38:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xC0, 力矩=0xFF
2025-07-23 13:38:00 [INFO] Gripper_485.py:386 - 移动到位置 0x89
2025-07-23 13:38:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x89, 力矩=0xFF
2025-07-23 13:38:00 [INFO] Gripper_485.py:386 - 移动到位置 0x60
2025-07-23 13:38:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x60, 力矩=0xFF
2025-07-23 13:38:00 [INFO] Gripper_485.py:386 - 移动到位置 0x25
2025-07-23 13:38:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x25, 力矩=0xFF
2025-07-23 13:38:00 [INFO] Gripper_485.py:386 - 移动到位置 0x07
2025-07-23 13:38:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x07, 力矩=0xFF
2025-07-23 13:38:00 [INFO] Gripper_485.py:386 - 移动到位置 0x04
2025-07-23 13:38:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x04, 力矩=0xFF
2025-07-23 13:38:00 [INFO] Gripper_485.py:386 - 移动到位置 0x04
2025-07-23 13:38:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x04, 力矩=0xFF
2025-07-23 13:38:00 [INFO] Gripper_485.py:386 - 移动到位置 0x06
2025-07-23 13:38:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x06, 力矩=0xFF
2025-07-23 13:38:00 [INFO] Gripper_485.py:386 - 移动到位置 0x07
2025-07-23 13:38:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x07, 力矩=0xFF
2025-07-23 13:38:00 [INFO] Gripper_485.py:386 - 移动到位置 0x0A
2025-07-23 13:38:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0A, 力矩=0xFF
2025-07-23 13:38:00 [INFO] Gripper_485.py:386 - 移动到位置 0x0D
2025-07-23 13:38:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0D, 力矩=0xFF
2025-07-23 13:38:00 [INFO] Gripper_485.py:386 - 移动到位置 0x10
2025-07-23 13:38:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x10, 力矩=0xFF
2025-07-23 13:38:00 [INFO] Gripper_485.py:386 - 移动到位置 0x14
2025-07-23 13:38:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x14, 力矩=0xFF
2025-07-23 13:38:00 [INFO] Gripper_485.py:386 - 移动到位置 0x18
2025-07-23 13:38:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x18, 力矩=0xFF
2025-07-23 13:38:00 [INFO] Gripper_485.py:386 - 移动到位置 0x18
2025-07-23 13:38:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x18, 力矩=0xFF
2025-07-23 13:38:00 [INFO] Gripper_485.py:386 - 移动到位置 0x17
2025-07-23 13:38:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x17, 力矩=0xFF
2025-07-23 13:38:00 [INFO] Gripper_485.py:386 - 移动到位置 0x17
2025-07-23 13:38:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x17, 力矩=0xFF
2025-07-23 13:38:00 [INFO] Gripper_485.py:386 - 移动到位置 0x18
2025-07-23 13:38:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x18, 力矩=0xFF
2025-07-23 13:38:00 [INFO] Gripper_485.py:386 - 移动到位置 0x18
2025-07-23 13:38:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x18, 力矩=0xFF
2025-07-23 13:38:00 [INFO] Gripper_485.py:386 - 移动到位置 0x19
2025-07-23 13:38:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x19, 力矩=0xFF
2025-07-23 13:38:00 [INFO] Gripper_485.py:386 - 移动到位置 0x1A
2025-07-23 13:38:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x1A, 力矩=0xFF
2025-07-23 13:38:00 [INFO] Gripper_485.py:386 - 移动到位置 0x1B
2025-07-23 13:38:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x1B, 力矩=0xFF
2025-07-23 13:38:00 [INFO] Gripper_485.py:386 - 移动到位置 0x1C
2025-07-23 13:38:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x1C, 力矩=0xFF
2025-07-23 13:38:00 [INFO] Gripper_485.py:386 - 移动到位置 0x1B
2025-07-23 13:38:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x1B, 力矩=0xFF
2025-07-23 13:38:00 [INFO] Gripper_485.py:386 - 移动到位置 0x1D
2025-07-23 13:38:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x1D, 力矩=0xFF
2025-07-23 13:38:00 [INFO] Gripper_485.py:386 - 移动到位置 0x1F
2025-07-23 13:38:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x1F, 力矩=0xFF
2025-07-23 13:38:00 [INFO] Gripper_485.py:386 - 移动到位置 0x20
2025-07-23 13:38:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x20, 力矩=0xFF
2025-07-23 13:38:00 [INFO] Gripper_485.py:386 - 移动到位置 0x23
2025-07-23 13:38:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x23, 力矩=0xFF
2025-07-23 13:38:00 [INFO] Gripper_485.py:386 - 移动到位置 0x26
2025-07-23 13:38:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x26, 力矩=0xFF
2025-07-23 13:38:00 [INFO] Gripper_485.py:386 - 移动到位置 0x2E
2025-07-23 13:38:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x2E, 力矩=0xFF
2025-07-23 13:38:00 [INFO] Gripper_485.py:386 - 移动到位置 0x2F
2025-07-23 13:38:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x2F, 力矩=0xFF
2025-07-23 13:38:00 [INFO] Gripper_485.py:386 - 移动到位置 0x33
2025-07-23 13:38:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x33, 力矩=0xFF
2025-07-23 13:38:00 [INFO] Gripper_485.py:386 - 移动到位置 0x3E
2025-07-23 13:38:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x3E, 力矩=0xFF
2025-07-23 13:38:00 [INFO] Gripper_485.py:386 - 移动到位置 0x46
2025-07-23 13:38:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x46, 力矩=0xFF
2025-07-23 13:38:00 [INFO] Gripper_485.py:386 - 移动到位置 0x4F
2025-07-23 13:38:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x4F, 力矩=0xFF
2025-07-23 13:38:00 [INFO] Gripper_485.py:386 - 移动到位置 0x5C
2025-07-23 13:38:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x5C, 力矩=0xFF
2025-07-23 13:38:00 [INFO] Gripper_485.py:386 - 移动到位置 0x5F
2025-07-23 13:38:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x5F, 力矩=0xFF
2025-07-23 13:38:00 [INFO] Gripper_485.py:386 - 移动到位置 0x69
2025-07-23 13:38:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x69, 力矩=0xFF
2025-07-23 13:38:00 [INFO] Gripper_485.py:386 - 移动到位置 0x70
2025-07-23 13:38:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x70, 力矩=0xFF
2025-07-23 13:38:00 [INFO] Gripper_485.py:386 - 移动到位置 0x75
2025-07-23 13:38:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x75, 力矩=0xFF
2025-07-23 13:38:00 [INFO] Gripper_485.py:386 - 移动到位置 0x85
2025-07-23 13:38:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x85, 力矩=0xFF
2025-07-23 13:38:00 [INFO] Gripper_485.py:386 - 移动到位置 0x8D
2025-07-23 13:38:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x8D, 力矩=0xFF
2025-07-23 13:38:00 [INFO] Gripper_485.py:386 - 移动到位置 0xA1
2025-07-23 13:38:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xA1, 力矩=0xFF
2025-07-23 13:38:00 [INFO] Gripper_485.py:386 - 移动到位置 0xAD
2025-07-23 13:38:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xAD, 力矩=0xFF
2025-07-23 13:38:00 [INFO] Gripper_485.py:386 - 移动到位置 0xBA
2025-07-23 13:38:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xBA, 力矩=0xFF
2025-07-23 13:38:00 [INFO] Gripper_485.py:386 - 移动到位置 0xD6
2025-07-23 13:38:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xD6, 力矩=0xFF
2025-07-23 13:38:00 [INFO] Gripper_485.py:386 - 移动到位置 0xE2
2025-07-23 13:38:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xE2, 力矩=0xFF
2025-07-23 13:38:00 [INFO] Gripper_485.py:386 - 移动到位置 0xF0
2025-07-23 13:38:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF0, 力矩=0xFF
2025-07-23 13:38:00 [INFO] Gripper_485.py:386 - 移动到位置 0xF7
2025-07-23 13:38:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF7, 力矩=0xFF
2025-07-23 13:38:00 [INFO] Gripper_485.py:386 - 移动到位置 0xFC
2025-07-23 13:38:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFC, 力矩=0xFF
2025-07-23 13:38:01 [INFO] Gripper_485.py:386 - 移动到位置 0xFC
2025-07-23 13:38:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFC, 力矩=0xFF
2025-07-23 13:38:01 [INFO] Gripper_485.py:386 - 移动到位置 0xEC
2025-07-23 13:38:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xEC, 力矩=0xFF
2025-07-23 13:38:01 [INFO] Gripper_485.py:386 - 移动到位置 0xBB
2025-07-23 13:38:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xBB, 力矩=0xFF
2025-07-23 13:38:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:02 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:02 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:02 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:02 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:02 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:02 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:02 [INFO] Gripper_485.py:386 - 移动到位置 0x05
2025-07-23 13:38:02 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x05, 力矩=0xFF
2025-07-23 13:38:02 [INFO] Gripper_485.py:386 - 移动到位置 0x0E
2025-07-23 13:38:02 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0E, 力矩=0xFF
2025-07-23 13:38:02 [INFO] Gripper_485.py:386 - 移动到位置 0x2A
2025-07-23 13:38:02 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x2A, 力矩=0xFF
2025-07-23 13:38:02 [INFO] Gripper_485.py:386 - 移动到位置 0x4A
2025-07-23 13:38:02 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x4A, 力矩=0xFF
2025-07-23 13:38:02 [INFO] Gripper_485.py:386 - 移动到位置 0xA0
2025-07-23 13:38:02 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xA0, 力矩=0xFF
2025-07-23 13:38:02 [INFO] Gripper_485.py:386 - 移动到位置 0xBF
2025-07-23 13:38:02 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xBF, 力矩=0xFF
2025-07-23 13:38:02 [INFO] Gripper_485.py:386 - 移动到位置 0xDA
2025-07-23 13:38:02 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xDA, 力矩=0xFF
2025-07-23 13:38:02 [INFO] Gripper_485.py:386 - 移动到位置 0xF7
2025-07-23 13:38:02 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF7, 力矩=0xFF
2025-07-23 13:38:03 [INFO] Gripper_485.py:386 - 移动到位置 0xFB
2025-07-23 13:38:03 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFB, 力矩=0xFF
2025-07-23 13:38:03 [INFO] Gripper_485.py:386 - 移动到位置 0xF7
2025-07-23 13:38:03 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF7, 力矩=0xFF
2025-07-23 13:38:03 [INFO] Gripper_485.py:386 - 移动到位置 0xDC
2025-07-23 13:38:03 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xDC, 力矩=0xFF
2025-07-23 13:38:03 [INFO] Gripper_485.py:386 - 移动到位置 0xC9
2025-07-23 13:38:03 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xC9, 力矩=0xFF
2025-07-23 13:38:03 [INFO] Gripper_485.py:386 - 移动到位置 0x9B
2025-07-23 13:38:03 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x9B, 力矩=0xFF
2025-07-23 13:38:03 [INFO] Gripper_485.py:386 - 移动到位置 0x7E
2025-07-23 13:38:03 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x7E, 力矩=0xFF
2025-07-23 13:38:03 [INFO] Gripper_485.py:386 - 移动到位置 0x0F
2025-07-23 13:38:03 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0F, 力矩=0xFF
2025-07-23 13:38:03 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:03 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:03 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:03 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:03 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:03 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:03 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:03 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:03 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:03 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:03 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:03 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:03 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:03 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:03 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:03 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:03 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:03 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:03 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:03 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:03 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:03 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:03 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:03 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:03 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:03 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:03 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:03 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:03 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:03 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:03 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:03 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:03 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:03 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:03 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:03 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:03 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:03 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:03 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:03 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:03 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:03 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:03 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:03 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:03 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:03 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:03 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:03 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:03 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:03 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:03 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:03 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:03 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:03 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:03 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:03 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:03 [INFO] Gripper_485.py:386 - 移动到位置 0x02
2025-07-23 13:38:03 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x02, 力矩=0xFF
2025-07-23 13:38:03 [INFO] Gripper_485.py:386 - 移动到位置 0x06
2025-07-23 13:38:03 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x06, 力矩=0xFF
2025-07-23 13:38:03 [INFO] Gripper_485.py:386 - 移动到位置 0x0B
2025-07-23 13:38:03 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0B, 力矩=0xFF
2025-07-23 13:38:03 [INFO] Gripper_485.py:386 - 移动到位置 0x17
2025-07-23 13:38:03 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x17, 力矩=0xFF
2025-07-23 13:38:03 [INFO] Gripper_485.py:386 - 移动到位置 0x22
2025-07-23 13:38:03 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x22, 力矩=0xFF
2025-07-23 13:38:03 [INFO] Gripper_485.py:386 - 移动到位置 0x39
2025-07-23 13:38:03 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x39, 力矩=0xFF
2025-07-23 13:38:03 [INFO] Gripper_485.py:386 - 移动到位置 0x9F
2025-07-23 13:38:03 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x9F, 力矩=0xFF
2025-07-23 13:38:03 [INFO] Gripper_485.py:386 - 移动到位置 0xC9
2025-07-23 13:38:03 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xC9, 力矩=0xFF
2025-07-23 13:38:08 [INFO] Gripper_485.py:386 - 移动到位置 0xEF
2025-07-23 13:38:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xEF, 力矩=0xFF
2025-07-23 13:38:08 [INFO] Gripper_485.py:386 - 移动到位置 0xE4
2025-07-23 13:38:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xE4, 力矩=0xFF
2025-07-23 13:38:08 [INFO] Gripper_485.py:386 - 移动到位置 0xC1
2025-07-23 13:38:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xC1, 力矩=0xFF
2025-07-23 13:38:08 [INFO] Gripper_485.py:386 - 移动到位置 0xA0
2025-07-23 13:38:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xA0, 力矩=0xFF
2025-07-23 13:38:08 [INFO] Gripper_485.py:386 - 移动到位置 0x20
2025-07-23 13:38:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x20, 力矩=0xFF
2025-07-23 13:38:08 [INFO] Gripper_485.py:386 - 移动到位置 0x20
2025-07-23 13:38:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x20, 力矩=0xFF
2025-07-23 13:38:08 [INFO] Gripper_485.py:386 - 移动到位置 0x0F
2025-07-23 13:38:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0F, 力矩=0xFF
2025-07-23 13:38:08 [INFO] Gripper_485.py:386 - 移动到位置 0x03
2025-07-23 13:38:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x03, 力矩=0xFF
2025-07-23 13:38:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:09 [INFO] Gripper_485.py:386 - 移动到位置 0x02
2025-07-23 13:38:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x02, 力矩=0xFF
2025-07-23 13:38:09 [INFO] Gripper_485.py:386 - 移动到位置 0x2B
2025-07-23 13:38:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x2B, 力矩=0xFF
2025-07-23 13:38:09 [INFO] Gripper_485.py:386 - 移动到位置 0x55
2025-07-23 13:38:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x55, 力矩=0xFF
2025-07-23 13:38:09 [INFO] Gripper_485.py:386 - 移动到位置 0x80
2025-07-23 13:38:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x80, 力矩=0xFF
2025-07-23 13:38:09 [INFO] Gripper_485.py:386 - 移动到位置 0xBF
2025-07-23 13:38:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xBF, 力矩=0xFF
2025-07-23 13:38:09 [INFO] Gripper_485.py:386 - 移动到位置 0xD8
2025-07-23 13:38:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xD8, 力矩=0xFF
2025-07-23 13:38:09 [INFO] Gripper_485.py:386 - 移动到位置 0xFD
2025-07-23 13:38:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFD, 力矩=0xFF
2025-07-23 13:38:14 [INFO] Gripper_485.py:386 - 移动到位置 0xFD
2025-07-23 13:38:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFD, 力矩=0xFF
2025-07-23 13:38:14 [INFO] Gripper_485.py:386 - 移动到位置 0xFA
2025-07-23 13:38:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFA, 力矩=0xFF
2025-07-23 13:38:14 [INFO] Gripper_485.py:386 - 移动到位置 0xF7
2025-07-23 13:38:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF7, 力矩=0xFF
2025-07-23 13:38:14 [INFO] Gripper_485.py:386 - 移动到位置 0xF4
2025-07-23 13:38:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF4, 力矩=0xFF
2025-07-23 13:38:14 [INFO] Gripper_485.py:386 - 移动到位置 0xF1
2025-07-23 13:38:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF1, 力矩=0xFF
2025-07-23 13:38:14 [INFO] Gripper_485.py:386 - 移动到位置 0xE9
2025-07-23 13:38:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xE9, 力矩=0xFF
2025-07-23 13:38:14 [INFO] Gripper_485.py:386 - 移动到位置 0xE2
2025-07-23 13:38:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xE2, 力矩=0xFF
2025-07-23 13:38:14 [INFO] Gripper_485.py:386 - 移动到位置 0xD4
2025-07-23 13:38:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xD4, 力矩=0xFF
2025-07-23 13:38:14 [INFO] Gripper_485.py:386 - 移动到位置 0xC9
2025-07-23 13:38:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xC9, 力矩=0xFF
2025-07-23 13:38:14 [INFO] Gripper_485.py:386 - 移动到位置 0xBE
2025-07-23 13:38:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xBE, 力矩=0xFF
2025-07-23 13:38:14 [INFO] Gripper_485.py:386 - 移动到位置 0x9F
2025-07-23 13:38:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x9F, 力矩=0xFF
2025-07-23 13:38:14 [INFO] Gripper_485.py:386 - 移动到位置 0x84
2025-07-23 13:38:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x84, 力矩=0xFF
2025-07-23 13:38:14 [INFO] Gripper_485.py:386 - 移动到位置 0x61
2025-07-23 13:38:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x61, 力矩=0xFF
2025-07-23 13:38:14 [INFO] Gripper_485.py:386 - 移动到位置 0x20
2025-07-23 13:38:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x20, 力矩=0xFF
2025-07-23 13:38:14 [INFO] Gripper_485.py:386 - 移动到位置 0x13
2025-07-23 13:38:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x13, 力矩=0xFF
2025-07-23 13:38:14 [INFO] Gripper_485.py:386 - 移动到位置 0x05
2025-07-23 13:38:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x05, 力矩=0xFF
2025-07-23 13:38:14 [INFO] Gripper_485.py:386 - 移动到位置 0x03
2025-07-23 13:38:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x03, 力矩=0xFF
2025-07-23 13:38:14 [INFO] Gripper_485.py:386 - 移动到位置 0x01
2025-07-23 13:38:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x01, 力矩=0xFF
2025-07-23 13:38:14 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:14 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:15 [INFO] Gripper_485.py:386 - 移动到位置 0x09
2025-07-23 13:38:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x09, 力矩=0xFF
2025-07-23 13:38:15 [INFO] Gripper_485.py:386 - 移动到位置 0x11
2025-07-23 13:38:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x11, 力矩=0xFF
2025-07-23 13:38:15 [INFO] Gripper_485.py:386 - 移动到位置 0x1C
2025-07-23 13:38:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x1C, 力矩=0xFF
2025-07-23 13:38:15 [INFO] Gripper_485.py:386 - 移动到位置 0x46
2025-07-23 13:38:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x46, 力矩=0xFF
2025-07-23 13:38:15 [INFO] Gripper_485.py:386 - 移动到位置 0x6E
2025-07-23 13:38:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x6E, 力矩=0xFF
2025-07-23 13:38:15 [INFO] Gripper_485.py:386 - 移动到位置 0xAD
2025-07-23 13:38:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xAD, 力矩=0xFF
2025-07-23 13:38:15 [INFO] Gripper_485.py:386 - 移动到位置 0xC8
2025-07-23 13:38:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xC8, 力矩=0xFF
2025-07-23 13:38:15 [INFO] Gripper_485.py:386 - 移动到位置 0xDB
2025-07-23 13:38:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xDB, 力矩=0xFF
2025-07-23 13:38:15 [INFO] Gripper_485.py:386 - 移动到位置 0xF4
2025-07-23 13:38:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF4, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0xED
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xED, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0xDB
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xDB, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0xA5
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xA5, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x7D
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x7D, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0xF2
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF2, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x47
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x47, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0xE3
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xE3, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x19
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x19, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0xBE
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xBE, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x0D
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0D, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0xA9
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xA9, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x7A
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x7A, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x60
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x60, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x35
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x35, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x06
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x06, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x02
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x02, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x05
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x05, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x0D
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0D, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x07
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x07, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x1C
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x1C, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x11
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x11, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x5B
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x5B, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x2D
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x2D, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x74
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x74, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x52
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x52, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x8A
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x8A, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0x78
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x78, 力矩=0xFF
2025-07-23 13:38:37 [INFO] Gripper_485.py:386 - 移动到位置 0xB2
2025-07-23 13:38:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xB2, 力矩=0xFF
2025-07-23 13:38:38 [INFO] Gripper_485.py:386 - 移动到位置 0xB4
2025-07-23 13:38:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xB4, 力矩=0xFF
2025-07-23 13:38:38 [INFO] Gripper_485.py:386 - 移动到位置 0xC2
2025-07-23 13:38:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xC2, 力矩=0xFF
2025-07-23 13:38:38 [INFO] Gripper_485.py:386 - 移动到位置 0xCC
2025-07-23 13:38:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xCC, 力矩=0xFF
2025-07-23 13:38:38 [INFO] Gripper_485.py:386 - 移动到位置 0xE2
2025-07-23 13:38:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xE2, 力矩=0xFF
2025-07-23 13:38:38 [INFO] Gripper_485.py:386 - 移动到位置 0xF0
2025-07-23 13:38:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF0, 力矩=0xFF
2025-07-23 13:38:38 [INFO] Gripper_485.py:386 - 移动到位置 0xEF
2025-07-23 13:38:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xEF, 力矩=0xFF
2025-07-23 13:38:38 [INFO] Gripper_485.py:386 - 移动到位置 0xFA
2025-07-23 13:38:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFA, 力矩=0xFF
2025-07-23 13:38:38 [INFO] Gripper_485.py:386 - 移动到位置 0xFB
2025-07-23 13:38:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFB, 力矩=0xFF
2025-07-23 13:38:47 [INFO] Gripper_485.py:386 - 移动到位置 0xFA
2025-07-23 13:38:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFA, 力矩=0xFF
2025-07-23 13:38:47 [INFO] Gripper_485.py:386 - 移动到位置 0xF7
2025-07-23 13:38:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF7, 力矩=0xFF
2025-07-23 13:38:47 [INFO] Gripper_485.py:386 - 移动到位置 0xF0
2025-07-23 13:38:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF0, 力矩=0xFF
2025-07-23 13:38:47 [INFO] Gripper_485.py:386 - 移动到位置 0xEF
2025-07-23 13:38:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xEF, 力矩=0xFF
2025-07-23 13:38:47 [INFO] Gripper_485.py:386 - 移动到位置 0xEE
2025-07-23 13:38:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xEE, 力矩=0xFF
2025-07-23 13:38:47 [INFO] Gripper_485.py:386 - 移动到位置 0xEF
2025-07-23 13:38:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xEF, 力矩=0xFF
2025-07-23 13:38:47 [INFO] Gripper_485.py:386 - 移动到位置 0xF0
2025-07-23 13:38:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF0, 力矩=0xFF
2025-07-23 13:38:47 [INFO] Gripper_485.py:386 - 移动到位置 0xF3
2025-07-23 13:38:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF3, 力矩=0xFF
2025-07-23 13:38:47 [INFO] Gripper_485.py:386 - 移动到位置 0xF3
2025-07-23 13:38:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF3, 力矩=0xFF
2025-07-23 13:38:47 [INFO] Gripper_485.py:386 - 移动到位置 0xF3
2025-07-23 13:38:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF3, 力矩=0xFF
2025-07-23 13:38:47 [INFO] Gripper_485.py:386 - 移动到位置 0xFA
2025-07-23 13:38:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFA, 力矩=0xFF
2025-07-23 13:38:47 [INFO] Gripper_485.py:386 - 移动到位置 0xFB
2025-07-23 13:38:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFB, 力矩=0xFF
2025-07-23 13:38:47 [INFO] Gripper_485.py:386 - 移动到位置 0xFB
2025-07-23 13:38:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFB, 力矩=0xFF
2025-07-23 13:38:47 [INFO] Gripper_485.py:386 - 移动到位置 0xFA
2025-07-23 13:38:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFA, 力矩=0xFF
2025-07-23 13:38:47 [INFO] Gripper_485.py:386 - 移动到位置 0xFA
2025-07-23 13:38:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFA, 力矩=0xFF
2025-07-23 13:38:47 [INFO] Gripper_485.py:386 - 移动到位置 0xFC
2025-07-23 13:38:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFC, 力矩=0xFF
