2025-07-22 15:31:56 [INFO] logger_config.py:102 - 日志系统初始化完成，日志文件: /home/<USER>/Spatial_AI/logs/gripper_485/gripper_485_1_20250722_153156.log
2025-07-22 15:31:56 [INFO] Gripper_485.py:53 - 初始化RS485夹爪 - 端口: /dev/ttyUSB1, 节点ID: 1, 波特率: 115200
2025-07-22 15:31:56 [DEBUG] Gripper_485.py:92 - 启动RS485消息接收线程
2025-07-22 15:31:56 [INFO] Gripper_485.py:96 - RS485消息接收线程已启动
2025-07-22 15:31:56 [INFO] Gripper_485.py:53 - 初始化RS485夹爪 - 端口: /dev/ttyUSB2, 节点ID: 1, 波特率: 115200
2025-07-22 15:31:56 [DEBUG] Gripper_485.py:92 - 启动RS485消息接收线程
2025-07-22 15:31:56 [INFO] Gripper_485.py:96 - RS485消息接收线程已启动
2025-07-22 15:31:58 [INFO] Gripper_485.py:372 - 执行关闭夹爪指令
2025-07-22 15:31:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 15:31:58 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 15:32:03 [WARNING] Gripper_485.py:255 - 等待回复超时，节点ID: 1
2025-07-22 15:32:03 [INFO] Gripper_485.py:372 - 执行关闭夹爪指令
2025-07-22 15:32:03 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 15:32:03 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 15:32:08 [WARNING] Gripper_485.py:255 - 等待回复超时，节点ID: 1
2025-07-22 15:32:09 [INFO] Gripper_485.py:358 - 执行打开夹爪指令
2025-07-22 15:32:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 15:32:09 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 15:32:14 [WARNING] Gripper_485.py:255 - 等待回复超时，节点ID: 1
2025-07-22 15:32:14 [INFO] Gripper_485.py:358 - 执行打开夹爪指令
2025-07-22 15:32:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 15:32:14 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
