2025-07-22 21:38:40 [INFO] logger_config.py:102 - 日志系统初始化完成，日志文件: /home/<USER>/Spatial_AI/logs/gripper_485/gripper_485_1_20250722_213840.log
2025-07-22 21:38:40 [INFO] Gripper_485.py:53 - 初始化RS485夹爪 - 端口: /dev/ttyUSB1, 节点ID: 1, 波特率: 115200
2025-07-22 21:38:40 [DEBUG] Gripper_485.py:92 - 启动RS485消息接收线程
2025-07-22 21:38:40 [INFO] Gripper_485.py:96 - RS485消息接收线程已启动
2025-07-22 21:38:40 [INFO] Gripper_485.py:53 - 初始化RS485夹爪 - 端口: /dev/ttyUSB2, 节点ID: 1, 波特率: 115200
2025-07-22 21:38:40 [DEBUG] Gripper_485.py:92 - 启动RS485消息接收线程
2025-07-22 21:38:40 [INFO] Gripper_485.py:96 - RS485消息接收线程已启动
2025-07-22 21:38:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:38:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:38:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:38:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:38:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xFD
2025-07-22 21:38:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFD
2025-07-22 21:38:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xFC
2025-07-22 21:38:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFC
2025-07-22 21:38:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xFA
2025-07-22 21:38:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFA
2025-07-22 21:38:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xF7
2025-07-22 21:38:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xF7
2025-07-22 21:38:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xF4
2025-07-22 21:38:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xF4
2025-07-22 21:38:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xF0
2025-07-22 21:38:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xF0
2025-07-22 21:38:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xEC
2025-07-22 21:38:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xEC
2025-07-22 21:38:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xE7
2025-07-22 21:38:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xE7
2025-07-22 21:38:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xE2
2025-07-22 21:38:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xE1
2025-07-22 21:38:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xDC
2025-07-22 21:38:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xDB
2025-07-22 21:38:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xD5
2025-07-22 21:38:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xD5
2025-07-22 21:38:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xCE
2025-07-22 21:38:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xCE
2025-07-22 21:38:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xC7
2025-07-22 21:38:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xC7
2025-07-22 21:38:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xC0
2025-07-22 21:38:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xC0
2025-07-22 21:38:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xB9
2025-07-22 21:38:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xB9
2025-07-22 21:38:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xB3
2025-07-22 21:38:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xB2
2025-07-22 21:38:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xAC
2025-07-22 21:38:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xAB
2025-07-22 21:38:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xA5
2025-07-22 21:38:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xA5
2025-07-22 21:38:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x9F
2025-07-22 21:38:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x9E
2025-07-22 21:38:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x99
2025-07-22 21:38:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x99
2025-07-22 21:38:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x94
2025-07-22 21:38:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x94
2025-07-22 21:38:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x8F
2025-07-22 21:38:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x8F
2025-07-22 21:38:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x8B
2025-07-22 21:38:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x8B
2025-07-22 21:38:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x88
2025-07-22 21:38:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x88
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x85
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x85
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x83
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x83
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x81
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x81
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:42 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:42 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:42 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:42 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 17, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 21:38:42 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:42 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 128, 'velocity': 28, 'force': 91, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:42 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:42 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 17, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x81
2025-07-22 21:38:42 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:42 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 129, 'velocity': 43, 'force': 92, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:42 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:42 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 17, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:42 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:42 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 129, 'velocity': 43, 'force': 92, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:42 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:42 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 17, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:42 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:42 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 129, 'velocity': 43, 'force': 92, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:42 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:42 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 17, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:42 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:42 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 129, 'velocity': 43, 'force': 92, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:42 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:42 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 17, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:42 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:42 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 129, 'velocity': 43, 'force': 92, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:42 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:42 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 17, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:42 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:42 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 129, 'velocity': 43, 'force': 92, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:42 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:42 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 17, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:42 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:42 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 129, 'velocity': 43, 'force': 92, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:42 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:42 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 17, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:42 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:42 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 129, 'velocity': 43, 'force': 92, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:42 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:42 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 17, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:42 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:42 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 129, 'velocity': 43, 'force': 92, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:42 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:42 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 17, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xE8
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xEA
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xEC
2025-07-22 21:38:42 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:42 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 236, 'velocity': 175, 'force': 46, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 21:38:42 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:42 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 17, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xEF
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xF1
2025-07-22 21:38:42 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:42 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 241, 'velocity': 145, 'force': 33, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 21:38:42 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:42 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 17, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:42 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:42 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 241, 'velocity': 145, 'force': 33, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 21:38:42 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:42 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 17, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 241, 'velocity': 145, 'force': 33, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 21:38:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 17, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 241, 'velocity': 145, 'force': 33, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 21:38:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 17, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 241, 'velocity': 145, 'force': 33, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 21:38:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 17, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 241, 'velocity': 145, 'force': 33, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 21:38:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 17, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 241, 'velocity': 145, 'force': 33, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 21:38:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 17, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 241, 'velocity': 145, 'force': 33, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 21:38:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 17, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 241, 'velocity': 145, 'force': 33, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 21:38:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 17, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 241, 'velocity': 145, 'force': 33, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 21:38:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 17, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 241, 'velocity': 145, 'force': 33, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 21:38:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 17, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 241, 'velocity': 145, 'force': 33, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 21:38:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 17, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 241, 'velocity': 145, 'force': 33, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 21:38:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 17, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:38:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 17, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 17, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 17, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 17, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 17, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:38:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:38:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 17, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 17, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 17, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 17, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 17, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 17, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 17, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:38:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 17, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 17, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 17, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 17, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 17, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 17, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:43 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 17, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:43 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 17, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 17, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:38:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 5, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 17, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:38:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 17, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:38:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:38:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 17, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 17, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 17, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 17, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 17, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 17, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 17, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 17, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 17, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 17, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 17, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 17, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 17, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 17, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 17, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 17, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 17, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 17, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 17, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 17, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 17, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 17, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 17, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 17, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 17, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 17, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 17, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:38:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 33, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:38:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 35, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:38:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 34, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:38:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 32, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:38:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:38:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:38:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 5, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:38:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:38:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:38:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:38:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:38:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:38:47 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:47 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:38:47 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:38:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:38:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:38:47 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
