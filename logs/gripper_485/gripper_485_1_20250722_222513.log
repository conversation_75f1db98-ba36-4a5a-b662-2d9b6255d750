2025-07-22 22:25:13 [INFO] logger_config.py:102 - 日志系统初始化完成，日志文件: /home/<USER>/Spatial_AI/logs/gripper_485/gripper_485_1_20250722_222513.log
2025-07-22 22:25:13 [INFO] Gripper_485.py:53 - 初始化RS485夹爪 - 端口: /dev/ttyUSB1, 节点ID: 1, 波特率: 115200
2025-07-22 22:25:13 [DEBUG] Gripper_485.py:92 - 启动RS485消息接收线程
2025-07-22 22:25:13 [INFO] Gripper_485.py:96 - RS485消息接收线程已启动
2025-07-22 22:25:13 [INFO] Gripper_485.py:53 - 初始化RS485夹爪 - 端口: /dev/ttyUSB2, 节点ID: 1, 波特率: 115200
2025-07-22 22:25:13 [DEBUG] Gripper_485.py:92 - 启动RS485消息接收线程
2025-07-22 22:25:13 [INFO] Gripper_485.py:96 - RS485消息接收线程已启动
2025-07-22 22:25:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:29 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:29 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:29 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:29 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:29 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:29 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:29 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:29 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:29 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:29 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:29 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:29 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:29 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:29 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:29 [INFO] Gripper_485.py:386 - 移动到位置 0x38
2025-07-22 22:25:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x38, 力矩=0xFF
2025-07-22 22:25:29 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:29 [INFO] Gripper_485.py:386 - 移动到位置 0xE5
2025-07-22 22:25:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xE5, 力矩=0xFF
2025-07-22 22:25:29 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:30 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:30 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:30 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:30 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:30 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:30 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:30 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:30 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:30 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:30 [INFO] Gripper_485.py:386 - 移动到位置 0x41
2025-07-22 22:25:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x41, 力矩=0xFF
2025-07-22 22:25:30 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:30 [INFO] Gripper_485.py:386 - 移动到位置 0xE2
2025-07-22 22:25:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xE2, 力矩=0xFF
2025-07-22 22:25:30 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:36 [INFO] Gripper_485.py:386 - 移动到位置 0xF0
2025-07-22 22:25:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF0, 力矩=0xFF
2025-07-22 22:25:36 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:36 [INFO] Gripper_485.py:386 - 移动到位置 0x2D
2025-07-22 22:25:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x2D, 力矩=0xFF
2025-07-22 22:25:36 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:36 [INFO] Gripper_485.py:386 - 移动到位置 0x8B
2025-07-22 22:25:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x8B, 力矩=0xFF
2025-07-22 22:25:36 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:36 [INFO] Gripper_485.py:386 - 移动到位置 0xB5
2025-07-22 22:25:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xB5, 力矩=0xFF
2025-07-22 22:25:36 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:36 [INFO] Gripper_485.py:386 - 移动到位置 0xB7
2025-07-22 22:25:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xB7, 力矩=0xFF
2025-07-22 22:25:36 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:36 [INFO] Gripper_485.py:386 - 移动到位置 0x35
2025-07-22 22:25:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x35, 力矩=0xFF
2025-07-22 22:25:36 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:36 [INFO] Gripper_485.py:386 - 移动到位置 0x19
2025-07-22 22:25:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x19, 力矩=0xFF
2025-07-22 22:25:36 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:36 [INFO] Gripper_485.py:386 - 移动到位置 0xA7
2025-07-22 22:25:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xA7, 力矩=0xFF
2025-07-22 22:25:36 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:36 [INFO] Gripper_485.py:386 - 移动到位置 0xE1
2025-07-22 22:25:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xE1, 力矩=0xFF
2025-07-22 22:25:36 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:37 [INFO] Gripper_485.py:386 - 移动到位置 0xF9
2025-07-22 22:25:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF9, 力矩=0xFF
2025-07-22 22:25:37 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:37 [INFO] Gripper_485.py:386 - 移动到位置 0xF9
2025-07-22 22:25:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF9, 力矩=0xFF
2025-07-22 22:25:37 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:37 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:37 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:37 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:37 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:37 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:37 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:37 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:37 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:37 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:37 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:37 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:37 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:37 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:37 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:37 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:37 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:37 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:37 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:37 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:38 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:38 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:38 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:38 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:38 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:38 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:38 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:38 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:38 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:38 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:38 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:38 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:38 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:38 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:38 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:38 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:38 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:38 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:38 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:38 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:38 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:38 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:38 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:38 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:38 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:38 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:38 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:38 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:38 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:38 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:38 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:38 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:39 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:39 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:39 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:39 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:39 [INFO] Gripper_485.py:386 - 移动到位置 0xFA
2025-07-22 22:25:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFA, 力矩=0xFF
2025-07-22 22:25:39 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:39 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:39 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:39 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:39 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:39 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:39 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:39 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:39 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:41 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:41 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:41 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:42 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:42 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:42 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:42 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:42 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:42 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:42 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:42 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:42 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:42 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:42 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:42 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:42 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:42 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:42 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:42 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:42 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:42 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:42 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:42 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:42 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:42 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:42 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:42 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:42 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:42 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:42 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:42 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:42 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:42 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:43 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:43 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:43 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:43 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:43 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:43 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:43 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:43 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:43 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:43 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:43 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:43 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:43 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:43 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:43 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:43 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:43 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:43 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:43 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:43 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:43 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:44 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:44 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:44 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:44 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:44 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:44 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:44 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:44 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:44 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:44 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:44 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:44 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:44 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:44 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:44 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:44 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:44 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:44 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:44 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:44 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:44 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:44 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:44 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:45 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:45 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:45 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:45 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:45 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:45 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:45 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:45 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:45 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:45 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:45 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:45 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:45 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:45 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:45 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:45 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:45 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:45 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:45 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:45 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:45 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:45 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:45 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:45 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:45 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:46 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:46 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:46 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:46 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:46 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:46 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:46 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:46 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:46 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:46 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:46 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:46 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:46 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:46 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:46 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:46 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:46 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:46 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:46 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:46 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:46 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:46 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:46 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:46 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:46 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:46 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:46 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:46 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:46 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:47 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:47 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:47 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:47 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:47 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:47 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:47 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:47 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:47 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:47 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:47 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:47 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:47 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:47 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:47 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:47 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:47 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:47 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:47 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:47 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:47 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:47 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:47 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:47 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:47 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:47 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:47 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:47 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:48 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:48 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:48 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:48 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:48 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:48 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:48 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:48 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:48 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:48 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:48 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:48 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:48 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:48 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:48 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:48 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:48 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:48 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:48 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:48 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:48 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:48 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:48 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:48 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:48 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:48 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:48 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:48 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:48 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:49 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:49 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:49 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:49 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:49 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:49 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:49 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:49 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:49 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:49 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:49 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:49 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:49 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:49 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:49 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:49 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:49 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:49 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:49 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:49 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:49 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:49 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:49 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:49 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:49 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:49 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:49 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:50 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:52 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:53 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:53 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:53 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:53 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:53 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:53 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:53 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:53 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:53 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:53 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:53 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:53 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:53 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:53 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:53 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:53 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:53 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:53 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:53 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:53 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:53 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:53 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:53 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:53 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:53 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:53 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:53 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:53 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:53 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:54 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:54 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:54 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:54 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:54 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:54 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:54 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:54 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:54 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:54 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:54 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:54 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:54 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:54 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:54 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:54 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:54 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:54 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:54 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:54 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:54 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:54 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:54 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:54 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:54 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:54 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:54 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:54 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:54 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:55 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:55 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:55 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:55 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:55 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:55 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:55 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:55 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:55 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:55 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:55 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:55 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:55 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:55 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:55 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:55 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:55 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:55 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:55 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:55 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:55 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:55 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:55 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:55 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:55 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:55 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:55 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:55 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:55 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:55 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:56 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:56 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:56 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:56 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:56 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:56 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:56 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:56 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:56 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:56 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:56 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:56 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:56 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:56 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:56 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:56 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:56 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:56 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:56 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:56 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:56 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:56 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:56 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:56 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:56 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:56 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:56 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:56 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:57 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:57 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:57 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:57 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:57 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:57 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:57 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:57 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:57 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:57 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:57 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:57 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:57 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:57 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:57 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:57 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:57 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:57 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:57 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:57 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:57 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:57 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:57 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:57 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:57 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:58 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:58 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:58 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:58 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:58 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:58 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:58 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:58 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:58 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:58 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:58 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:58 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:58 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:58 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:58 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:58 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:58 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:58 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:58 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:58 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:58 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:58 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:58 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:58 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:58 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:58 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:58 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:58 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:58 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:58 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:58 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:59 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:59 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:59 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:59 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:59 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:59 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:59 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:59 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:59 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:59 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:59 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:59 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:59 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:59 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:59 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:59 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:59 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:59 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:59 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:59 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:59 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:59 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:59 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:59 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:59 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:59 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:59 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:59 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:59 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:59 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:59 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:59 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:59 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:59 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:59 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:59 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:59 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:59 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:59 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:59 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:59 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:59 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:59 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:59 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:59 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:59 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:59 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:59 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:59 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:59 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:59 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:59 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:59 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:59 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:59 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:59 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:59 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:59 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:59 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:59 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:59 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:59 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:59 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:59 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:59 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:59 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:59 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:59 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:59 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:59 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:59 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:59 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:59 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:59 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:59 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:59 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:59 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:59 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:25:59 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:25:59 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:25:59 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:00 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:00 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:00 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:00 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:00 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:00 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:00 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:00 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:00 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:00 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:00 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:00 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:00 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:00 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:00 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:00 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:00 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:00 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:00 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:00 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:00 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:00 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:00 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:00 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:00 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:00 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:00 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:00 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:00 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:00 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:00 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:00 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:00 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:00 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:00 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:00 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:00 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:00 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:00 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:00 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:00 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:00 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:00 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:00 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:01 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:01 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:01 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:01 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:01 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:01 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:01 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:01 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:01 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:01 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:01 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:01 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:01 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:01 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:01 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:01 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:01 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:01 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:01 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:01 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:01 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:02 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:02 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:02 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:02 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:02 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:02 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:02 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:02 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:02 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:02 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:02 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:02 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:02 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:02 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:02 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:02 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:02 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:02 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:02 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:02 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:02 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:02 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:02 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:02 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:02 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:02 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:02 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:02 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:02 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:02 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:02 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:02 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:02 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:02 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:02 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:02 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:02 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:02 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:02 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:02 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:02 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:02 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:02 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:02 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:02 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:02 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:02 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:02 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:02 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:02 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:02 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:02 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:02 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:02 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:02 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:02 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:02 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:02 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:02 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:02 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:02 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:02 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:02 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:02 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:02 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:02 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:02 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:02 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:02 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:02 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:02 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:02 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:02 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:02 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:02 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:02 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:02 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:02 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:03 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:03 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:03 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:03 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:03 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:03 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:03 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:03 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:03 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:03 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:03 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:03 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:03 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:03 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:03 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:03 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:03 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:03 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:03 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:03 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:03 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:03 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:03 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:03 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:03 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:03 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:03 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:03 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:03 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:03 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:03 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:03 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:03 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:03 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:03 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:03 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:03 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:03 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:03 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:03 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:03 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:03 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:03 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:03 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:03 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:03 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:03 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:03 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:03 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:03 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:03 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:03 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:03 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:03 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:03 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:03 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:03 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:03 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:03 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:03 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:03 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:03 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:03 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:03 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:03 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:03 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:03 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:03 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:03 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:03 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:03 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:03 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:03 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:03 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:03 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:03 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:03 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:03 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:03 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:03 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:03 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:03 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:03 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:03 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:03 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:03 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:03 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:04 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:04 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:04 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:04 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:04 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:04 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:04 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:04 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:04 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:04 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:04 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:04 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:04 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:04 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:04 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:04 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:04 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:04 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:04 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:04 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:04 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:04 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:04 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:04 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:04 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:04 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:04 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:04 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:04 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:04 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:04 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:04 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:04 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:04 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:04 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:04 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:04 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:04 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:04 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:04 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:04 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:04 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:04 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:04 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:04 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:04 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:04 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:04 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:04 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:04 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:04 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:04 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:04 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:04 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:04 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:04 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:04 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:04 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:04 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:04 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:04 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:04 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:04 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:04 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:04 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:04 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:04 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:04 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:04 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:04 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:04 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:04 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:04 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:04 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:04 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:04 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:04 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:04 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:04 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:04 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:04 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:04 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:04 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:04 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:05 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:05 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:05 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:05 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:05 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:05 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:05 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:05 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:05 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:05 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:05 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:05 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:05 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:05 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:05 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:05 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:05 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:05 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:05 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:05 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:05 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:05 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:05 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:05 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:05 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:05 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:05 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:05 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:05 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:05 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:05 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:05 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:05 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:05 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:05 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:05 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:05 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:05 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:05 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:05 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:05 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:05 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:05 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:05 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:05 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:05 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:05 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:05 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:05 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:05 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:05 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:05 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:05 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:05 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:05 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:05 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:05 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:05 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:05 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:05 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:05 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:05 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:05 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:05 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:05 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:05 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:05 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:05 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:05 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:05 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:05 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:05 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:05 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:05 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:05 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:05 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:05 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:05 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:06 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:06 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:06 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:06 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:06 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:06 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:06 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:06 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:06 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:06 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:06 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:06 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:06 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:06 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:06 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:06 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:06 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:06 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:06 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:06 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:06 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:06 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:06 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:06 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:06 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:06 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:06 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:06 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:06 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:06 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:06 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:06 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:06 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:06 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:06 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:06 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:06 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:06 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:06 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:06 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:06 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:06 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:06 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:06 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:06 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:06 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:06 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:06 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:06 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:06 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:06 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:06 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:06 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:06 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:06 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:06 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:06 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:06 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:06 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:06 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:06 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:06 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:06 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:06 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:06 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:06 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:06 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:06 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:06 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:06 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:06 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:06 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:06 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:06 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:06 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:06 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:06 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:06 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:06 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:06 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:06 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:06 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:06 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:06 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:07 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:07 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:07 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:07 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:07 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:07 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:07 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:07 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:07 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:07 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:07 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:07 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:07 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:07 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:07 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:07 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:07 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:07 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:07 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:07 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:07 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:07 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:07 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:07 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:07 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:07 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:07 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:07 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:08 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:08 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:08 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:08 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:08 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:08 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:08 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:08 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:08 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:08 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:08 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:08 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:08 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:08 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:08 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:08 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:08 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:08 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:08 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:08 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:08 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:08 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:08 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:08 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:08 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:08 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:09 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:09 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:09 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:09 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:09 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:09 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:09 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:09 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:09 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:09 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:09 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:09 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:09 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:09 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:09 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:09 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:09 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:09 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:09 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:09 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:09 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:09 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:10 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:10 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:10 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:10 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:10 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:10 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:10 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:10 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:10 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:10 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:10 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:10 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:10 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:10 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:10 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:10 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:10 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:10 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:10 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:10 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:10 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:10 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:10 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:10 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:10 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:10 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:10 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:10 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:10 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:10 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:10 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:10 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:10 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:10 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:10 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:10 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:10 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:10 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:10 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:10 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:10 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:10 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:10 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:10 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:10 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:10 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:10 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:10 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:10 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:10 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:10 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:10 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:10 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:10 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:10 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:10 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:10 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:10 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:10 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:10 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:10 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:10 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:10 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:10 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:10 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:10 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:10 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:10 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:10 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:10 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:10 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:10 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:10 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:10 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:10 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:11 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:11 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:11 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:11 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:11 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:11 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:11 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:11 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:11 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:11 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:11 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:11 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:11 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:11 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:11 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:11 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:11 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:11 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:11 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:11 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:11 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:11 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:11 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:11 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:11 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:11 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:11 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:11 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:11 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:11 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:12 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:12 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:12 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:12 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:12 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:12 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:12 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:12 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:12 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:12 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:12 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:12 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:12 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:12 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:12 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:12 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:12 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:12 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:12 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:12 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:12 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:12 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:12 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:12 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:12 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:12 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:12 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:12 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:12 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:12 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:12 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:12 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:12 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:12 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:12 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:12 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:12 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:12 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:12 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:12 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:12 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:12 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:12 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:12 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:12 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:12 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:12 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:12 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:12 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:12 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:12 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:12 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:12 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:12 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:12 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:12 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:12 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:12 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:12 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:12 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:12 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:12 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:12 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:12 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:12 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:12 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:12 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:12 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:12 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:12 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:12 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:12 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:12 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:12 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:12 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:12 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:12 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:12 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:12 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:12 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:12 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:12 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:12 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:12 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:12 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:12 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:12 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:12 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:12 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:12 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:13 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:13 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:13 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:13 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:13 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:13 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:13 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:13 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:13 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:13 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:13 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:13 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:13 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:13 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:13 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:13 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:13 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:13 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:13 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:13 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:13 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:13 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:13 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:13 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:13 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:13 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:13 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:13 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:13 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:13 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:13 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:13 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:13 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:13 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:13 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:13 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:13 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:13 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:13 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:13 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:13 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:13 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:13 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:13 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:13 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:13 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:13 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:13 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:13 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:13 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:13 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:13 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:13 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:13 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:13 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:13 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:13 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:13 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:13 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:13 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:13 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:13 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:13 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:13 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:13 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:13 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:13 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:13 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:13 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:13 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:13 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:13 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:13 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:13 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:13 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:13 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:13 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:13 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:13 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:13 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:13 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:13 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:13 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:13 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:13 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:13 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:13 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:14 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:14 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:14 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:14 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:14 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:14 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:14 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:14 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:14 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:14 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:14 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:14 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:14 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:14 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:14 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:14 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:14 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:14 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:14 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:14 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:14 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:14 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:14 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:14 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:14 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:14 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:14 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:14 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:14 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:14 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:14 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:14 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:14 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:14 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:14 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:14 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:14 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:14 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:14 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:14 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:14 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:14 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:14 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:14 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:14 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:14 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:14 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:14 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:14 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:14 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:14 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:14 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:14 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:14 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:14 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:14 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:14 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:15 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:15 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:15 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:15 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:15 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:15 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:15 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:15 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:15 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:15 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:15 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:15 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:15 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:15 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:15 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:15 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:15 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:15 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:15 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:15 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:15 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:15 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:15 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:15 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:15 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:15 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:15 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:15 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:15 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:15 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:15 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:16 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:16 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:16 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:16 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:16 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:16 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:16 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:16 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:16 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:16 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:16 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:16 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:16 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:16 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:16 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:16 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:16 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:16 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:16 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:16 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:16 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:16 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:16 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:16 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:16 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:16 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:16 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:16 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:16 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:16 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:16 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:16 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:16 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:16 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:16 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:16 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:16 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:16 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:16 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:16 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:16 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:16 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:16 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:16 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:16 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:16 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:16 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:16 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:16 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:16 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:16 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:16 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:16 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:16 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:16 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:16 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:16 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:16 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:16 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:16 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:16 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:16 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:16 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:16 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:16 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:16 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:16 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:16 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:16 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:16 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:16 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:16 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:16 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:16 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:16 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:16 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:16 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:16 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:16 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:16 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:16 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:16 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:16 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:16 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:17 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:17 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:17 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:17 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:17 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:17 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:17 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:17 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:17 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:17 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:17 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:17 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:17 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:17 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:17 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:17 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:17 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:17 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:17 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:17 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:17 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:17 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:17 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:17 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:17 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:17 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:17 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:17 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:17 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:17 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:17 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:17 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:17 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:17 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:17 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:17 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:17 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:17 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:17 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:17 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:17 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:17 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:17 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:17 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:17 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:17 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:17 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:17 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:17 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:17 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:17 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:17 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:17 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:17 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:17 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:17 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:17 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:17 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:17 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:17 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:17 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:17 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:17 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:17 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:17 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:17 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:17 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:17 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:17 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:17 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:17 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:17 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:17 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:17 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:17 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:17 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:17 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:17 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:17 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:17 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:17 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:17 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:17 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:17 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:17 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:17 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:17 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:18 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:18 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:18 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:18 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:18 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:18 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:18 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:18 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:18 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:18 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:18 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:18 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:18 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:18 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:18 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:18 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:18 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:18 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:18 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:18 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:18 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:18 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:18 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:18 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:18 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:18 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:18 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:18 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:18 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:18 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:18 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:18 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:18 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:18 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:18 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:18 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:18 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:18 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:18 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:18 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:18 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:18 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:18 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:18 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:18 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:18 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:18 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:18 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:18 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:18 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:18 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:18 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:18 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:18 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:18 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:18 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:18 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:18 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:18 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:18 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:18 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:18 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:18 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:18 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:18 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:18 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:18 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:18 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:18 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:18 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:18 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:18 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:18 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:18 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:18 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:18 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:18 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:18 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:18 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:18 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:18 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:18 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:18 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:18 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:18 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:18 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:18 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:19 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:19 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:19 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:19 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:19 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:19 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:19 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:19 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:19 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:19 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:19 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:19 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:19 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:19 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:19 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:19 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:19 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:19 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:19 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:19 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:19 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:19 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:19 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:19 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:19 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:19 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:19 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:19 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:19 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:19 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:19 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:19 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:19 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:19 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:19 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:19 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:19 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:19 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:19 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:19 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:19 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:19 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:19 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:19 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:19 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:19 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:19 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:19 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:19 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:19 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:19 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:19 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:19 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:19 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:19 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:19 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:19 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:19 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:19 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:19 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:19 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:19 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:19 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:19 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:19 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:19 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:19 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:19 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:19 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:19 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:19 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:19 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:19 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:19 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:19 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:19 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:19 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:19 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:20 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:20 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:20 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:20 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:20 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:20 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:20 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:20 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:20 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:20 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:20 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:20 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:20 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:20 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:20 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:20 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:20 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:20 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:20 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:20 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:20 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:20 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:20 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:20 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:20 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:20 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:20 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:20 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:20 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:20 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:20 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:20 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:20 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:20 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:20 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:20 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:20 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:20 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:20 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:20 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:20 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:20 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:20 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:20 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:20 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:20 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:20 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:20 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:20 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:20 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:20 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:20 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:20 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:20 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:20 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:20 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:20 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:20 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:20 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:20 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:20 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:20 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:20 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:20 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:20 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:20 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:20 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:20 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:20 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:20 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:20 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:20 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:20 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:20 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:20 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:20 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:20 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:20 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:20 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:20 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:20 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:20 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:20 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:20 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:20 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:20 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:20 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:21 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:21 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:21 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:21 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:21 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:21 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:21 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:21 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:21 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:21 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:21 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:21 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:21 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:21 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:21 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:21 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:21 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:21 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:21 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:21 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:21 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:21 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:21 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:21 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:21 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:21 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:21 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:21 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:21 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:21 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:21 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:21 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:21 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:21 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:21 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:21 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:21 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:21 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:21 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:21 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:21 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:21 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:21 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:21 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:21 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:21 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:21 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:21 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:21 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:21 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:21 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:21 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:21 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:21 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:21 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:21 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:21 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:21 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:21 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:21 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:21 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:21 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:21 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:21 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:21 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:21 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:21 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:21 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:21 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:21 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:21 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:21 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:21 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:21 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:21 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:21 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:21 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:21 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:21 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:21 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:21 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:21 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:21 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:21 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:21 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:21 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:21 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:22 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:22 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:22 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:22 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:22 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:22 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:22 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:22 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:22 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:22 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:22 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:22 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:22 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:22 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:22 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:22 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:22 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:22 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:22 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:22 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:22 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:22 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:22 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:22 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:22 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:22 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:22 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:22 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:22 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:22 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:22 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:22 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:22 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:22 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:22 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:22 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:22 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:22 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:22 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:22 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:22 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:22 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:22 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:22 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:22 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:22 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:22 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:22 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:22 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:22 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:22 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:22 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:22 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:22 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:22 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:22 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:22 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:22 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:22 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:22 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:22 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:22 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:22 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:22 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:22 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:22 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:22 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:22 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:22 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:22 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:22 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:22 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:22 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:22 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:22 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:22 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:22 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:22 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:22 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:22 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:22 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:22 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:22 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:22 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:23 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:23 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:23 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:23 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:23 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:23 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:23 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:23 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:23 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:23 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:23 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:23 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:23 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:23 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:23 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:23 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:23 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:23 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:23 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:23 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:23 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:23 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:23 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:23 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:23 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:23 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:23 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:23 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:23 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:23 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:23 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:23 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:23 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:23 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:23 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:23 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:23 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:23 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:23 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:23 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:23 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:23 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:23 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:23 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:23 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:23 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:23 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:23 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:23 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:23 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:23 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:23 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:23 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:23 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:23 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:23 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:23 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:23 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:23 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:23 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:23 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:23 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:23 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:23 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:23 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:23 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:23 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:23 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:23 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:23 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:23 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:23 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:23 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:23 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:23 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:23 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:23 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:23 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:23 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:23 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:23 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:23 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:23 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:23 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:24 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:24 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:24 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:24 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:24 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:24 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:24 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:24 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:24 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:24 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:24 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:24 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:24 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:24 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:24 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:24 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:24 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:24 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:24 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:24 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:24 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:24 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:24 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:24 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:24 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:24 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:24 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:24 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:24 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:25 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:25 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:25 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:25 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:25 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:25 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:25 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:25 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:25 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:25 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:25 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:25 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:25 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:25 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:25 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:25 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:25 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:25 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:25 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:25 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:25 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:25 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:25 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:25 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:25 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:25 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:25 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:25 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:25 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:25 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:25 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:25 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:25 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:25 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:25 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:25 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:25 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:25 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:25 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:25 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:25 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:25 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:25 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:25 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:25 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:25 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:25 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:25 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:25 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:25 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:25 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:25 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:25 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:25 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:25 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:25 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:25 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:25 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:25 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:25 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:26 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:26 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:26 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:26 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:26 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:26 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:26 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:26 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:26 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:26 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:26 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:26 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:26 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:26 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:26 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:26 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:26 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:26 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:26 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:26 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:26 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:26 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:26 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:26 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:26 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:26:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:26:26 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:26 [INFO] Gripper_485.py:386 - 移动到位置 0x6D
2025-07-22 22:26:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x6D, 力矩=0xFF
2025-07-22 22:26:26 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:26 [INFO] Gripper_485.py:386 - 移动到位置 0xA1
2025-07-22 22:26:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xA1, 力矩=0xFF
2025-07-22 22:26:26 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:35 [INFO] Gripper_485.py:386 - 移动到位置 0xF2
2025-07-22 22:26:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF2, 力矩=0xFF
2025-07-22 22:26:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:35 [INFO] Gripper_485.py:386 - 移动到位置 0xC0
2025-07-22 22:26:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xC0, 力矩=0xFF
2025-07-22 22:26:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:35 [INFO] Gripper_485.py:386 - 移动到位置 0x12
2025-07-22 22:26:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x12, 力矩=0xFF
2025-07-22 22:26:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:35 [INFO] Gripper_485.py:386 - 移动到位置 0x16
2025-07-22 22:26:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x16, 力矩=0xFF
2025-07-22 22:26:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:35 [INFO] Gripper_485.py:386 - 移动到位置 0x4C
2025-07-22 22:26:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x4C, 力矩=0xFF
2025-07-22 22:26:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:35 [INFO] Gripper_485.py:386 - 移动到位置 0xDB
2025-07-22 22:26:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xDB, 力矩=0xFF
2025-07-22 22:26:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:36 [INFO] Gripper_485.py:386 - 移动到位置 0xEF
2025-07-22 22:26:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xEF, 力矩=0xFF
2025-07-22 22:26:36 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:36 [INFO] Gripper_485.py:386 - 移动到位置 0xDF
2025-07-22 22:26:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xDF, 力矩=0xFF
2025-07-22 22:26:36 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:36 [INFO] Gripper_485.py:386 - 移动到位置 0xBE
2025-07-22 22:26:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xBE, 力矩=0xFF
2025-07-22 22:26:36 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:36 [INFO] Gripper_485.py:386 - 移动到位置 0xC3
2025-07-22 22:26:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xC3, 力矩=0xFF
2025-07-22 22:26:36 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:26:36 [INFO] Gripper_485.py:386 - 移动到位置 0xEC
2025-07-22 22:26:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xEC, 力矩=0xFF
2025-07-22 22:26:36 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
