2025-07-22 21:34:25 [INFO] logger_config.py:102 - 日志系统初始化完成，日志文件: /home/<USER>/Spatial_AI/logs/gripper_485/gripper_485_1_20250722_213425.log
2025-07-22 21:34:25 [INFO] Gripper_485.py:53 - 初始化RS485夹爪 - 端口: /dev/ttyUSB1, 节点ID: 1, 波特率: 115200
2025-07-22 21:34:25 [DEBUG] Gripper_485.py:92 - 启动RS485消息接收线程
2025-07-22 21:34:25 [INFO] Gripper_485.py:96 - RS485消息接收线程已启动
2025-07-22 21:34:25 [INFO] Gripper_485.py:53 - 初始化RS485夹爪 - 端口: /dev/ttyUSB2, 节点ID: 1, 波特率: 115200
2025-07-22 21:34:25 [DEBUG] Gripper_485.py:92 - 启动RS485消息接收线程
2025-07-22 21:34:25 [INFO] Gripper_485.py:96 - RS485消息接收线程已启动
2025-07-22 21:34:27 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:34:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:34:27 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:34:32 [WARNING] Gripper_485.py:255 - 等待回复超时，节点ID: 1
2025-07-22 21:34:32 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:34:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:34:32 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:34:37 [WARNING] Gripper_485.py:255 - 等待回复超时，节点ID: 1
2025-07-22 21:34:37 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:34:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:34:37 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:34:42 [WARNING] Gripper_485.py:255 - 等待回复超时，节点ID: 1
2025-07-22 21:34:42 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:34:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:34:42 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:34:47 [WARNING] Gripper_485.py:255 - 等待回复超时，节点ID: 1
2025-07-22 21:34:47 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:34:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:34:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
