2025-07-23 21:01:20 [INFO] logger_config.py:102 - 日志系统初始化完成，日志文件: /home/<USER>/Spatial_AI/logs/gripper_485/gripper_485_1_20250723_210120.log
2025-07-23 21:01:20 [INFO] Gripper_485.py:53 - 初始化RS485夹爪 - 端口: /dev/ttyUSB2, 节点ID: 1, 波特率: 115200
2025-07-23 21:01:20 [DEBUG] Gripper_485.py:92 - 启动RS485消息接收线程
2025-07-23 21:01:20 [INFO] Gripper_485.py:96 - RS485消息接收线程已启动
2025-07-23 21:01:20 [INFO] Gripper_485.py:53 - 初始化RS485夹爪 - 端口: /dev/ttyUSB1, 节点ID: 1, 波特率: 115200
2025-07-23 21:01:20 [DEBUG] Gripper_485.py:92 - 启动RS485消息接收线程
2025-07-23 21:01:20 [INFO] Gripper_485.py:96 - RS485消息接收线程已启动
2025-07-23 21:05:00 [INFO] Gripper_485.py:386 - 移动到位置 0xFB
2025-07-23 21:05:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFB, 力矩=0xFF
2025-07-23 21:05:00 [INFO] Gripper_485.py:386 - 移动到位置 0xDF
2025-07-23 21:05:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xDF, 力矩=0xFF
2025-07-23 21:05:00 [INFO] Gripper_485.py:386 - 移动到位置 0xCC
2025-07-23 21:05:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xCC, 力矩=0xFF
2025-07-23 21:05:00 [INFO] Gripper_485.py:386 - 移动到位置 0xB1
2025-07-23 21:05:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xB1, 力矩=0xFF
2025-07-23 21:05:00 [INFO] Gripper_485.py:386 - 移动到位置 0xA7
2025-07-23 21:05:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xA7, 力矩=0xFF
2025-07-23 21:05:00 [INFO] Gripper_485.py:386 - 移动到位置 0xA5
2025-07-23 21:05:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xA5, 力矩=0xFF
2025-07-23 21:05:00 [INFO] Gripper_485.py:386 - 移动到位置 0xC9
2025-07-23 21:05:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xC9, 力矩=0xFF
2025-07-23 21:05:00 [INFO] Gripper_485.py:386 - 移动到位置 0xDB
2025-07-23 21:05:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xDB, 力矩=0xFF
2025-07-23 21:05:00 [INFO] Gripper_485.py:386 - 移动到位置 0xF7
2025-07-23 21:05:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF7, 力矩=0xFF
2025-07-23 21:05:00 [INFO] Gripper_485.py:386 - 移动到位置 0xFD
2025-07-23 21:05:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFD, 力矩=0xFF
2025-07-23 21:05:00 [INFO] Gripper_485.py:386 - 移动到位置 0xF6
2025-07-23 21:05:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF6, 力矩=0xFF
2025-07-23 21:05:00 [INFO] Gripper_485.py:386 - 移动到位置 0xF0
2025-07-23 21:05:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF0, 力矩=0xFF
2025-07-23 21:05:00 [INFO] Gripper_485.py:386 - 移动到位置 0xF2
2025-07-23 21:05:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF2, 力矩=0xFF
2025-07-23 21:05:00 [INFO] Gripper_485.py:386 - 移动到位置 0xF6
2025-07-23 21:05:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF6, 力矩=0xFF
2025-07-23 21:05:00 [INFO] Gripper_485.py:386 - 移动到位置 0xFB
2025-07-23 21:05:00 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFB, 力矩=0xFF
2025-07-23 21:05:07 [INFO] Gripper_485.py:386 - 移动到位置 0xE1
2025-07-23 21:05:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xE1, 力矩=0xFF
2025-07-23 21:05:07 [INFO] Gripper_485.py:386 - 移动到位置 0x95
2025-07-23 21:05:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x95, 力矩=0xFF
2025-07-23 21:05:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:07 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:07 [INFO] Gripper_485.py:386 - 移动到位置 0x03
2025-07-23 21:05:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x03, 力矩=0xFF
2025-07-23 21:05:07 [INFO] Gripper_485.py:386 - 移动到位置 0x0C
2025-07-23 21:05:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0C, 力矩=0xFF
2025-07-23 21:05:07 [INFO] Gripper_485.py:386 - 移动到位置 0x30
2025-07-23 21:05:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x30, 力矩=0xFF
2025-07-23 21:05:07 [INFO] Gripper_485.py:386 - 移动到位置 0xAE
2025-07-23 21:05:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xAE, 力矩=0xFF
2025-07-23 21:05:07 [INFO] Gripper_485.py:386 - 移动到位置 0xE0
2025-07-23 21:05:07 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xE0, 力矩=0xFF
2025-07-23 21:05:08 [INFO] Gripper_485.py:386 - 移动到位置 0xE7
2025-07-23 21:05:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xE7, 力矩=0xFF
2025-07-23 21:05:08 [INFO] Gripper_485.py:386 - 移动到位置 0xCD
2025-07-23 21:05:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xCD, 力矩=0xFF
2025-07-23 21:05:08 [INFO] Gripper_485.py:386 - 移动到位置 0x65
2025-07-23 21:05:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x65, 力矩=0xFF
2025-07-23 21:05:08 [INFO] Gripper_485.py:386 - 移动到位置 0x17
2025-07-23 21:05:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x17, 力矩=0xFF
2025-07-23 21:05:08 [INFO] Gripper_485.py:386 - 移动到位置 0x0C
2025-07-23 21:05:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0C, 力矩=0xFF
2025-07-23 21:05:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:08 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:08 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:09 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:09 [INFO] Gripper_485.py:386 - 移动到位置 0x24
2025-07-23 21:05:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x24, 力矩=0xFF
2025-07-23 21:05:09 [INFO] Gripper_485.py:386 - 移动到位置 0x4E
2025-07-23 21:05:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x4E, 力矩=0xFF
2025-07-23 21:05:09 [INFO] Gripper_485.py:386 - 移动到位置 0x8A
2025-07-23 21:05:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x8A, 力矩=0xFF
2025-07-23 21:05:09 [INFO] Gripper_485.py:386 - 移动到位置 0xDE
2025-07-23 21:05:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xDE, 力矩=0xFF
2025-07-23 21:05:09 [INFO] Gripper_485.py:386 - 移动到位置 0xF6
2025-07-23 21:05:09 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF6, 力矩=0xFF
2025-07-23 21:05:23 [INFO] Gripper_485.py:386 - 移动到位置 0xF9
2025-07-23 21:05:23 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF9, 力矩=0xFF
2025-07-23 21:05:23 [INFO] Gripper_485.py:386 - 移动到位置 0xB8
2025-07-23 21:05:23 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xB8, 力矩=0xFF
2025-07-23 21:05:23 [INFO] Gripper_485.py:386 - 移动到位置 0x8A
2025-07-23 21:05:23 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x8A, 力矩=0xFF
2025-07-23 21:05:23 [INFO] Gripper_485.py:386 - 移动到位置 0x58
2025-07-23 21:05:23 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x58, 力矩=0xFF
2025-07-23 21:05:23 [INFO] Gripper_485.py:386 - 移动到位置 0x24
2025-07-23 21:05:23 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x24, 力矩=0xFF
2025-07-23 21:05:23 [INFO] Gripper_485.py:386 - 移动到位置 0x1E
2025-07-23 21:05:23 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x1E, 力矩=0xFF
2025-07-23 21:05:23 [INFO] Gripper_485.py:386 - 移动到位置 0x13
2025-07-23 21:05:23 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x13, 力矩=0xFF
2025-07-23 21:05:23 [INFO] Gripper_485.py:386 - 移动到位置 0x02
2025-07-23 21:05:23 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x02, 力矩=0xFF
2025-07-23 21:05:23 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:23 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:23 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:23 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:23 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:23 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:23 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:23 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:23 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:23 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:23 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:23 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:23 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:23 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:23 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:23 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:23 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:23 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:23 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:23 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:23 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:23 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:23 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:23 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:23 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:23 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:23 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:23 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:23 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:23 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:23 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:23 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:23 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:23 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:23 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:23 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:23 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:23 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:23 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:23 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:23 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:23 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:23 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:23 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:23 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:23 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:23 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:23 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:23 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:23 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:23 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:23 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:23 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:23 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:23 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:23 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:23 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:23 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:23 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:23 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:23 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:23 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:23 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:23 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:23 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:23 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:23 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:23 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:23 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:23 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:23 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:23 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:23 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:23 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:23 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:23 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:24 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:24 [INFO] Gripper_485.py:386 - 移动到位置 0x01
2025-07-23 21:05:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x01, 力矩=0xFF
2025-07-23 21:05:24 [INFO] Gripper_485.py:386 - 移动到位置 0x03
2025-07-23 21:05:24 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x03, 力矩=0xFF
2025-07-23 21:05:25 [INFO] Gripper_485.py:386 - 移动到位置 0x04
2025-07-23 21:05:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x04, 力矩=0xFF
2025-07-23 21:05:25 [INFO] Gripper_485.py:386 - 移动到位置 0x04
2025-07-23 21:05:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x04, 力矩=0xFF
2025-07-23 21:05:25 [INFO] Gripper_485.py:386 - 移动到位置 0x07
2025-07-23 21:05:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x07, 力矩=0xFF
2025-07-23 21:05:25 [INFO] Gripper_485.py:386 - 移动到位置 0x08
2025-07-23 21:05:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x08, 力矩=0xFF
2025-07-23 21:05:25 [INFO] Gripper_485.py:386 - 移动到位置 0x0A
2025-07-23 21:05:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0A, 力矩=0xFF
2025-07-23 21:05:25 [INFO] Gripper_485.py:386 - 移动到位置 0x0B
2025-07-23 21:05:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0B, 力矩=0xFF
2025-07-23 21:05:25 [INFO] Gripper_485.py:386 - 移动到位置 0x0B
2025-07-23 21:05:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0B, 力矩=0xFF
2025-07-23 21:05:25 [INFO] Gripper_485.py:386 - 移动到位置 0x0B
2025-07-23 21:05:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0B, 力矩=0xFF
2025-07-23 21:05:25 [INFO] Gripper_485.py:386 - 移动到位置 0x0A
2025-07-23 21:05:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0A, 力矩=0xFF
2025-07-23 21:05:25 [INFO] Gripper_485.py:386 - 移动到位置 0x08
2025-07-23 21:05:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x08, 力矩=0xFF
2025-07-23 21:05:25 [INFO] Gripper_485.py:386 - 移动到位置 0x08
2025-07-23 21:05:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x08, 力矩=0xFF
2025-07-23 21:05:25 [INFO] Gripper_485.py:386 - 移动到位置 0x08
2025-07-23 21:05:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x08, 力矩=0xFF
2025-07-23 21:05:25 [INFO] Gripper_485.py:386 - 移动到位置 0x08
2025-07-23 21:05:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x08, 力矩=0xFF
2025-07-23 21:05:25 [INFO] Gripper_485.py:386 - 移动到位置 0x08
2025-07-23 21:05:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x08, 力矩=0xFF
2025-07-23 21:05:25 [INFO] Gripper_485.py:386 - 移动到位置 0x08
2025-07-23 21:05:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x08, 力矩=0xFF
2025-07-23 21:05:25 [INFO] Gripper_485.py:386 - 移动到位置 0x07
2025-07-23 21:05:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x07, 力矩=0xFF
2025-07-23 21:05:25 [INFO] Gripper_485.py:386 - 移动到位置 0x07
2025-07-23 21:05:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x07, 力矩=0xFF
2025-07-23 21:05:25 [INFO] Gripper_485.py:386 - 移动到位置 0x08
2025-07-23 21:05:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x08, 力矩=0xFF
2025-07-23 21:05:25 [INFO] Gripper_485.py:386 - 移动到位置 0x09
2025-07-23 21:05:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x09, 力矩=0xFF
2025-07-23 21:05:25 [INFO] Gripper_485.py:386 - 移动到位置 0x09
2025-07-23 21:05:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x09, 力矩=0xFF
2025-07-23 21:05:25 [INFO] Gripper_485.py:386 - 移动到位置 0x0B
2025-07-23 21:05:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0B, 力矩=0xFF
2025-07-23 21:05:25 [INFO] Gripper_485.py:386 - 移动到位置 0x0C
2025-07-23 21:05:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0C, 力矩=0xFF
2025-07-23 21:05:25 [INFO] Gripper_485.py:386 - 移动到位置 0x0C
2025-07-23 21:05:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0C, 力矩=0xFF
2025-07-23 21:05:25 [INFO] Gripper_485.py:386 - 移动到位置 0x0E
2025-07-23 21:05:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0E, 力矩=0xFF
2025-07-23 21:05:25 [INFO] Gripper_485.py:386 - 移动到位置 0x0F
2025-07-23 21:05:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0F, 力矩=0xFF
2025-07-23 21:05:25 [INFO] Gripper_485.py:386 - 移动到位置 0x10
2025-07-23 21:05:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x10, 力矩=0xFF
2025-07-23 21:05:25 [INFO] Gripper_485.py:386 - 移动到位置 0x11
2025-07-23 21:05:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x11, 力矩=0xFF
2025-07-23 21:05:25 [INFO] Gripper_485.py:386 - 移动到位置 0x11
2025-07-23 21:05:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x11, 力矩=0xFF
2025-07-23 21:05:25 [INFO] Gripper_485.py:386 - 移动到位置 0x12
2025-07-23 21:05:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x12, 力矩=0xFF
2025-07-23 21:05:25 [INFO] Gripper_485.py:386 - 移动到位置 0x12
2025-07-23 21:05:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x12, 力矩=0xFF
2025-07-23 21:05:25 [INFO] Gripper_485.py:386 - 移动到位置 0x12
2025-07-23 21:05:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x12, 力矩=0xFF
2025-07-23 21:05:25 [INFO] Gripper_485.py:386 - 移动到位置 0x12
2025-07-23 21:05:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x12, 力矩=0xFF
2025-07-23 21:05:25 [INFO] Gripper_485.py:386 - 移动到位置 0x12
2025-07-23 21:05:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x12, 力矩=0xFF
2025-07-23 21:05:25 [INFO] Gripper_485.py:386 - 移动到位置 0x14
2025-07-23 21:05:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x14, 力矩=0xFF
2025-07-23 21:05:25 [INFO] Gripper_485.py:386 - 移动到位置 0x14
2025-07-23 21:05:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x14, 力矩=0xFF
2025-07-23 21:05:25 [INFO] Gripper_485.py:386 - 移动到位置 0x16
2025-07-23 21:05:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x16, 力矩=0xFF
2025-07-23 21:05:25 [INFO] Gripper_485.py:386 - 移动到位置 0x18
2025-07-23 21:05:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x18, 力矩=0xFF
2025-07-23 21:05:25 [INFO] Gripper_485.py:386 - 移动到位置 0x1A
2025-07-23 21:05:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x1A, 力矩=0xFF
2025-07-23 21:05:25 [INFO] Gripper_485.py:386 - 移动到位置 0x1D
2025-07-23 21:05:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x1D, 力矩=0xFF
2025-07-23 21:05:25 [INFO] Gripper_485.py:386 - 移动到位置 0x1E
2025-07-23 21:05:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x1E, 力矩=0xFF
2025-07-23 21:05:25 [INFO] Gripper_485.py:386 - 移动到位置 0x20
2025-07-23 21:05:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x20, 力矩=0xFF
2025-07-23 21:05:25 [INFO] Gripper_485.py:386 - 移动到位置 0x21
2025-07-23 21:05:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x21, 力矩=0xFF
2025-07-23 21:05:25 [INFO] Gripper_485.py:386 - 移动到位置 0x21
2025-07-23 21:05:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x21, 力矩=0xFF
2025-07-23 21:05:25 [INFO] Gripper_485.py:386 - 移动到位置 0x22
2025-07-23 21:05:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x22, 力矩=0xFF
2025-07-23 21:05:25 [INFO] Gripper_485.py:386 - 移动到位置 0x23
2025-07-23 21:05:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x23, 力矩=0xFF
2025-07-23 21:05:25 [INFO] Gripper_485.py:386 - 移动到位置 0x23
2025-07-23 21:05:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x23, 力矩=0xFF
2025-07-23 21:05:25 [INFO] Gripper_485.py:386 - 移动到位置 0x23
2025-07-23 21:05:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x23, 力矩=0xFF
2025-07-23 21:05:25 [INFO] Gripper_485.py:386 - 移动到位置 0x22
2025-07-23 21:05:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x22, 力矩=0xFF
2025-07-23 21:05:25 [INFO] Gripper_485.py:386 - 移动到位置 0x23
2025-07-23 21:05:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x23, 力矩=0xFF
2025-07-23 21:05:25 [INFO] Gripper_485.py:386 - 移动到位置 0x22
2025-07-23 21:05:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x22, 力矩=0xFF
2025-07-23 21:05:25 [INFO] Gripper_485.py:386 - 移动到位置 0x23
2025-07-23 21:05:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x23, 力矩=0xFF
2025-07-23 21:05:25 [INFO] Gripper_485.py:386 - 移动到位置 0x22
2025-07-23 21:05:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x22, 力矩=0xFF
2025-07-23 21:05:25 [INFO] Gripper_485.py:386 - 移动到位置 0x21
2025-07-23 21:05:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x21, 力矩=0xFF
2025-07-23 21:05:25 [INFO] Gripper_485.py:386 - 移动到位置 0x22
2025-07-23 21:05:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x22, 力矩=0xFF
2025-07-23 21:05:25 [INFO] Gripper_485.py:386 - 移动到位置 0x22
2025-07-23 21:05:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x22, 力矩=0xFF
2025-07-23 21:05:25 [INFO] Gripper_485.py:386 - 移动到位置 0x21
2025-07-23 21:05:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x21, 力矩=0xFF
2025-07-23 21:05:25 [INFO] Gripper_485.py:386 - 移动到位置 0x21
2025-07-23 21:05:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x21, 力矩=0xFF
2025-07-23 21:05:25 [INFO] Gripper_485.py:386 - 移动到位置 0x21
2025-07-23 21:05:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x21, 力矩=0xFF
2025-07-23 21:05:25 [INFO] Gripper_485.py:386 - 移动到位置 0x21
2025-07-23 21:05:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x21, 力矩=0xFF
2025-07-23 21:05:25 [INFO] Gripper_485.py:386 - 移动到位置 0x20
2025-07-23 21:05:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x20, 力矩=0xFF
2025-07-23 21:05:25 [INFO] Gripper_485.py:386 - 移动到位置 0x21
2025-07-23 21:05:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x21, 力矩=0xFF
2025-07-23 21:05:25 [INFO] Gripper_485.py:386 - 移动到位置 0x21
2025-07-23 21:05:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x21, 力矩=0xFF
2025-07-23 21:05:25 [INFO] Gripper_485.py:386 - 移动到位置 0x21
2025-07-23 21:05:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x21, 力矩=0xFF
2025-07-23 21:05:25 [INFO] Gripper_485.py:386 - 移动到位置 0x21
2025-07-23 21:05:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x21, 力矩=0xFF
2025-07-23 21:05:25 [INFO] Gripper_485.py:386 - 移动到位置 0x21
2025-07-23 21:05:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x21, 力矩=0xFF
2025-07-23 21:05:25 [INFO] Gripper_485.py:386 - 移动到位置 0x20
2025-07-23 21:05:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x20, 力矩=0xFF
2025-07-23 21:05:25 [INFO] Gripper_485.py:386 - 移动到位置 0x20
2025-07-23 21:05:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x20, 力矩=0xFF
2025-07-23 21:05:25 [INFO] Gripper_485.py:386 - 移动到位置 0x20
2025-07-23 21:05:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x20, 力矩=0xFF
2025-07-23 21:05:25 [INFO] Gripper_485.py:386 - 移动到位置 0x20
2025-07-23 21:05:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x20, 力矩=0xFF
2025-07-23 21:05:25 [INFO] Gripper_485.py:386 - 移动到位置 0x20
2025-07-23 21:05:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x20, 力矩=0xFF
2025-07-23 21:05:25 [INFO] Gripper_485.py:386 - 移动到位置 0x1F
2025-07-23 21:05:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x1F, 力矩=0xFF
2025-07-23 21:05:25 [INFO] Gripper_485.py:386 - 移动到位置 0x20
2025-07-23 21:05:25 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x20, 力矩=0xFF
2025-07-23 21:05:26 [INFO] Gripper_485.py:386 - 移动到位置 0x1F
2025-07-23 21:05:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x1F, 力矩=0xFF
2025-07-23 21:05:26 [INFO] Gripper_485.py:386 - 移动到位置 0x1F
2025-07-23 21:05:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x1F, 力矩=0xFF
2025-07-23 21:05:26 [INFO] Gripper_485.py:386 - 移动到位置 0x1E
2025-07-23 21:05:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x1E, 力矩=0xFF
2025-07-23 21:05:26 [INFO] Gripper_485.py:386 - 移动到位置 0x1F
2025-07-23 21:05:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x1F, 力矩=0xFF
2025-07-23 21:05:26 [INFO] Gripper_485.py:386 - 移动到位置 0x1E
2025-07-23 21:05:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x1E, 力矩=0xFF
2025-07-23 21:05:26 [INFO] Gripper_485.py:386 - 移动到位置 0x1F
2025-07-23 21:05:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x1F, 力矩=0xFF
2025-07-23 21:05:26 [INFO] Gripper_485.py:386 - 移动到位置 0x1E
2025-07-23 21:05:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x1E, 力矩=0xFF
2025-07-23 21:05:26 [INFO] Gripper_485.py:386 - 移动到位置 0x1E
2025-07-23 21:05:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x1E, 力矩=0xFF
2025-07-23 21:05:26 [INFO] Gripper_485.py:386 - 移动到位置 0x1E
2025-07-23 21:05:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x1E, 力矩=0xFF
2025-07-23 21:05:26 [INFO] Gripper_485.py:386 - 移动到位置 0x1D
2025-07-23 21:05:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x1D, 力矩=0xFF
2025-07-23 21:05:26 [INFO] Gripper_485.py:386 - 移动到位置 0x1D
2025-07-23 21:05:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x1D, 力矩=0xFF
2025-07-23 21:05:26 [INFO] Gripper_485.py:386 - 移动到位置 0x1D
2025-07-23 21:05:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x1D, 力矩=0xFF
2025-07-23 21:05:26 [INFO] Gripper_485.py:386 - 移动到位置 0x1C
2025-07-23 21:05:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x1C, 力矩=0xFF
2025-07-23 21:05:26 [INFO] Gripper_485.py:386 - 移动到位置 0x1B
2025-07-23 21:05:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x1B, 力矩=0xFF
2025-07-23 21:05:26 [INFO] Gripper_485.py:386 - 移动到位置 0x1B
2025-07-23 21:05:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x1B, 力矩=0xFF
2025-07-23 21:05:26 [INFO] Gripper_485.py:386 - 移动到位置 0x1B
2025-07-23 21:05:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x1B, 力矩=0xFF
2025-07-23 21:05:26 [INFO] Gripper_485.py:386 - 移动到位置 0x1B
2025-07-23 21:05:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x1B, 力矩=0xFF
2025-07-23 21:05:26 [INFO] Gripper_485.py:386 - 移动到位置 0x1B
2025-07-23 21:05:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x1B, 力矩=0xFF
2025-07-23 21:05:26 [INFO] Gripper_485.py:386 - 移动到位置 0x1A
2025-07-23 21:05:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x1A, 力矩=0xFF
2025-07-23 21:05:26 [INFO] Gripper_485.py:386 - 移动到位置 0x1A
2025-07-23 21:05:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x1A, 力矩=0xFF
2025-07-23 21:05:26 [INFO] Gripper_485.py:386 - 移动到位置 0x1A
2025-07-23 21:05:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x1A, 力矩=0xFF
2025-07-23 21:05:26 [INFO] Gripper_485.py:386 - 移动到位置 0x1A
2025-07-23 21:05:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x1A, 力矩=0xFF
2025-07-23 21:05:26 [INFO] Gripper_485.py:386 - 移动到位置 0x19
2025-07-23 21:05:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x19, 力矩=0xFF
2025-07-23 21:05:26 [INFO] Gripper_485.py:386 - 移动到位置 0x19
2025-07-23 21:05:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x19, 力矩=0xFF
2025-07-23 21:05:26 [INFO] Gripper_485.py:386 - 移动到位置 0x19
2025-07-23 21:05:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x19, 力矩=0xFF
2025-07-23 21:05:26 [INFO] Gripper_485.py:386 - 移动到位置 0x18
2025-07-23 21:05:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x18, 力矩=0xFF
2025-07-23 21:05:26 [INFO] Gripper_485.py:386 - 移动到位置 0x18
2025-07-23 21:05:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x18, 力矩=0xFF
2025-07-23 21:05:26 [INFO] Gripper_485.py:386 - 移动到位置 0x17
2025-07-23 21:05:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x17, 力矩=0xFF
2025-07-23 21:05:26 [INFO] Gripper_485.py:386 - 移动到位置 0x17
2025-07-23 21:05:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x17, 力矩=0xFF
2025-07-23 21:05:26 [INFO] Gripper_485.py:386 - 移动到位置 0x18
2025-07-23 21:05:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x18, 力矩=0xFF
2025-07-23 21:05:26 [INFO] Gripper_485.py:386 - 移动到位置 0x18
2025-07-23 21:05:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x18, 力矩=0xFF
2025-07-23 21:05:26 [INFO] Gripper_485.py:386 - 移动到位置 0x18
2025-07-23 21:05:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x18, 力矩=0xFF
2025-07-23 21:05:26 [INFO] Gripper_485.py:386 - 移动到位置 0x18
2025-07-23 21:05:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x18, 力矩=0xFF
2025-07-23 21:05:26 [INFO] Gripper_485.py:386 - 移动到位置 0x18
2025-07-23 21:05:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x18, 力矩=0xFF
2025-07-23 21:05:26 [INFO] Gripper_485.py:386 - 移动到位置 0x17
2025-07-23 21:05:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x17, 力矩=0xFF
2025-07-23 21:05:26 [INFO] Gripper_485.py:386 - 移动到位置 0x18
2025-07-23 21:05:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x18, 力矩=0xFF
2025-07-23 21:05:26 [INFO] Gripper_485.py:386 - 移动到位置 0x18
2025-07-23 21:05:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x18, 力矩=0xFF
2025-07-23 21:05:26 [INFO] Gripper_485.py:386 - 移动到位置 0x18
2025-07-23 21:05:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x18, 力矩=0xFF
2025-07-23 21:05:26 [INFO] Gripper_485.py:386 - 移动到位置 0x19
2025-07-23 21:05:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x19, 力矩=0xFF
2025-07-23 21:05:26 [INFO] Gripper_485.py:386 - 移动到位置 0x19
2025-07-23 21:05:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x19, 力矩=0xFF
2025-07-23 21:05:26 [INFO] Gripper_485.py:386 - 移动到位置 0x18
2025-07-23 21:05:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x18, 力矩=0xFF
2025-07-23 21:05:26 [INFO] Gripper_485.py:386 - 移动到位置 0x18
2025-07-23 21:05:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x18, 力矩=0xFF
2025-07-23 21:05:26 [INFO] Gripper_485.py:386 - 移动到位置 0x19
2025-07-23 21:05:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x19, 力矩=0xFF
2025-07-23 21:05:26 [INFO] Gripper_485.py:386 - 移动到位置 0x18
2025-07-23 21:05:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x18, 力矩=0xFF
2025-07-23 21:05:26 [INFO] Gripper_485.py:386 - 移动到位置 0x19
2025-07-23 21:05:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x19, 力矩=0xFF
2025-07-23 21:05:26 [INFO] Gripper_485.py:386 - 移动到位置 0x18
2025-07-23 21:05:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x18, 力矩=0xFF
2025-07-23 21:05:26 [INFO] Gripper_485.py:386 - 移动到位置 0x18
2025-07-23 21:05:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x18, 力矩=0xFF
2025-07-23 21:05:26 [INFO] Gripper_485.py:386 - 移动到位置 0x18
2025-07-23 21:05:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x18, 力矩=0xFF
2025-07-23 21:05:26 [INFO] Gripper_485.py:386 - 移动到位置 0x18
2025-07-23 21:05:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x18, 力矩=0xFF
2025-07-23 21:05:26 [INFO] Gripper_485.py:386 - 移动到位置 0x19
2025-07-23 21:05:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x19, 力矩=0xFF
2025-07-23 21:05:26 [INFO] Gripper_485.py:386 - 移动到位置 0x18
2025-07-23 21:05:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x18, 力矩=0xFF
2025-07-23 21:05:26 [INFO] Gripper_485.py:386 - 移动到位置 0x18
2025-07-23 21:05:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x18, 力矩=0xFF
2025-07-23 21:05:26 [INFO] Gripper_485.py:386 - 移动到位置 0x19
2025-07-23 21:05:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x19, 力矩=0xFF
2025-07-23 21:05:26 [INFO] Gripper_485.py:386 - 移动到位置 0x19
2025-07-23 21:05:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x19, 力矩=0xFF
2025-07-23 21:05:26 [INFO] Gripper_485.py:386 - 移动到位置 0x19
2025-07-23 21:05:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x19, 力矩=0xFF
2025-07-23 21:05:26 [INFO] Gripper_485.py:386 - 移动到位置 0x18
2025-07-23 21:05:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x18, 力矩=0xFF
2025-07-23 21:05:26 [INFO] Gripper_485.py:386 - 移动到位置 0x19
2025-07-23 21:05:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x19, 力矩=0xFF
2025-07-23 21:05:26 [INFO] Gripper_485.py:386 - 移动到位置 0x19
2025-07-23 21:05:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x19, 力矩=0xFF
2025-07-23 21:05:26 [INFO] Gripper_485.py:386 - 移动到位置 0x19
2025-07-23 21:05:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x19, 力矩=0xFF
2025-07-23 21:05:26 [INFO] Gripper_485.py:386 - 移动到位置 0x19
2025-07-23 21:05:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x19, 力矩=0xFF
2025-07-23 21:05:26 [INFO] Gripper_485.py:386 - 移动到位置 0x19
2025-07-23 21:05:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x19, 力矩=0xFF
2025-07-23 21:05:26 [INFO] Gripper_485.py:386 - 移动到位置 0x18
2025-07-23 21:05:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x18, 力矩=0xFF
2025-07-23 21:05:26 [INFO] Gripper_485.py:386 - 移动到位置 0x19
2025-07-23 21:05:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x19, 力矩=0xFF
2025-07-23 21:05:26 [INFO] Gripper_485.py:386 - 移动到位置 0x19
2025-07-23 21:05:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x19, 力矩=0xFF
2025-07-23 21:05:26 [INFO] Gripper_485.py:386 - 移动到位置 0x1A
2025-07-23 21:05:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x1A, 力矩=0xFF
2025-07-23 21:05:26 [INFO] Gripper_485.py:386 - 移动到位置 0x1A
2025-07-23 21:05:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x1A, 力矩=0xFF
2025-07-23 21:05:26 [INFO] Gripper_485.py:386 - 移动到位置 0x1B
2025-07-23 21:05:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x1B, 力矩=0xFF
2025-07-23 21:05:26 [INFO] Gripper_485.py:386 - 移动到位置 0x1B
2025-07-23 21:05:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x1B, 力矩=0xFF
2025-07-23 21:05:26 [INFO] Gripper_485.py:386 - 移动到位置 0x1E
2025-07-23 21:05:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x1E, 力矩=0xFF
2025-07-23 21:05:26 [INFO] Gripper_485.py:386 - 移动到位置 0x1D
2025-07-23 21:05:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x1D, 力矩=0xFF
2025-07-23 21:05:26 [INFO] Gripper_485.py:386 - 移动到位置 0x1E
2025-07-23 21:05:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x1E, 力矩=0xFF
2025-07-23 21:05:26 [INFO] Gripper_485.py:386 - 移动到位置 0x1F
2025-07-23 21:05:26 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x1F, 力矩=0xFF
2025-07-23 21:05:27 [INFO] Gripper_485.py:386 - 移动到位置 0x20
2025-07-23 21:05:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x20, 力矩=0xFF
2025-07-23 21:05:27 [INFO] Gripper_485.py:386 - 移动到位置 0x22
2025-07-23 21:05:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x22, 力矩=0xFF
2025-07-23 21:05:27 [INFO] Gripper_485.py:386 - 移动到位置 0x22
2025-07-23 21:05:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x22, 力矩=0xFF
2025-07-23 21:05:27 [INFO] Gripper_485.py:386 - 移动到位置 0x22
2025-07-23 21:05:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x22, 力矩=0xFF
2025-07-23 21:05:27 [INFO] Gripper_485.py:386 - 移动到位置 0x24
2025-07-23 21:05:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x24, 力矩=0xFF
2025-07-23 21:05:27 [INFO] Gripper_485.py:386 - 移动到位置 0x25
2025-07-23 21:05:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x25, 力矩=0xFF
2025-07-23 21:05:27 [INFO] Gripper_485.py:386 - 移动到位置 0x24
2025-07-23 21:05:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x24, 力矩=0xFF
2025-07-23 21:05:27 [INFO] Gripper_485.py:386 - 移动到位置 0x24
2025-07-23 21:05:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x24, 力矩=0xFF
2025-07-23 21:05:27 [INFO] Gripper_485.py:386 - 移动到位置 0x24
2025-07-23 21:05:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x24, 力矩=0xFF
2025-07-23 21:05:27 [INFO] Gripper_485.py:386 - 移动到位置 0x24
2025-07-23 21:05:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x24, 力矩=0xFF
2025-07-23 21:05:27 [INFO] Gripper_485.py:386 - 移动到位置 0x25
2025-07-23 21:05:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x25, 力矩=0xFF
2025-07-23 21:05:27 [INFO] Gripper_485.py:386 - 移动到位置 0x26
2025-07-23 21:05:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x26, 力矩=0xFF
2025-07-23 21:05:27 [INFO] Gripper_485.py:386 - 移动到位置 0x26
2025-07-23 21:05:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x26, 力矩=0xFF
2025-07-23 21:05:27 [INFO] Gripper_485.py:386 - 移动到位置 0x26
2025-07-23 21:05:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x26, 力矩=0xFF
2025-07-23 21:05:27 [INFO] Gripper_485.py:386 - 移动到位置 0x26
2025-07-23 21:05:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x26, 力矩=0xFF
2025-07-23 21:05:27 [INFO] Gripper_485.py:386 - 移动到位置 0x27
2025-07-23 21:05:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x27, 力矩=0xFF
2025-07-23 21:05:27 [INFO] Gripper_485.py:386 - 移动到位置 0x27
2025-07-23 21:05:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x27, 力矩=0xFF
2025-07-23 21:05:27 [INFO] Gripper_485.py:386 - 移动到位置 0x27
2025-07-23 21:05:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x27, 力矩=0xFF
2025-07-23 21:05:27 [INFO] Gripper_485.py:386 - 移动到位置 0x28
2025-07-23 21:05:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x28, 力矩=0xFF
2025-07-23 21:05:27 [INFO] Gripper_485.py:386 - 移动到位置 0x28
2025-07-23 21:05:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x28, 力矩=0xFF
2025-07-23 21:05:27 [INFO] Gripper_485.py:386 - 移动到位置 0x28
2025-07-23 21:05:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x28, 力矩=0xFF
2025-07-23 21:05:27 [INFO] Gripper_485.py:386 - 移动到位置 0x28
2025-07-23 21:05:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x28, 力矩=0xFF
2025-07-23 21:05:27 [INFO] Gripper_485.py:386 - 移动到位置 0x28
2025-07-23 21:05:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x28, 力矩=0xFF
2025-07-23 21:05:27 [INFO] Gripper_485.py:386 - 移动到位置 0x29
2025-07-23 21:05:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x29, 力矩=0xFF
2025-07-23 21:05:27 [INFO] Gripper_485.py:386 - 移动到位置 0x29
2025-07-23 21:05:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x29, 力矩=0xFF
2025-07-23 21:05:27 [INFO] Gripper_485.py:386 - 移动到位置 0x29
2025-07-23 21:05:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x29, 力矩=0xFF
2025-07-23 21:05:27 [INFO] Gripper_485.py:386 - 移动到位置 0x2A
2025-07-23 21:05:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x2A, 力矩=0xFF
2025-07-23 21:05:27 [INFO] Gripper_485.py:386 - 移动到位置 0x2A
2025-07-23 21:05:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x2A, 力矩=0xFF
2025-07-23 21:05:27 [INFO] Gripper_485.py:386 - 移动到位置 0x29
2025-07-23 21:05:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x29, 力矩=0xFF
2025-07-23 21:05:27 [INFO] Gripper_485.py:386 - 移动到位置 0x29
2025-07-23 21:05:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x29, 力矩=0xFF
2025-07-23 21:05:27 [INFO] Gripper_485.py:386 - 移动到位置 0x2A
2025-07-23 21:05:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x2A, 力矩=0xFF
2025-07-23 21:05:27 [INFO] Gripper_485.py:386 - 移动到位置 0x2A
2025-07-23 21:05:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x2A, 力矩=0xFF
2025-07-23 21:05:27 [INFO] Gripper_485.py:386 - 移动到位置 0x2A
2025-07-23 21:05:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x2A, 力矩=0xFF
2025-07-23 21:05:27 [INFO] Gripper_485.py:386 - 移动到位置 0x2A
2025-07-23 21:05:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x2A, 力矩=0xFF
2025-07-23 21:05:27 [INFO] Gripper_485.py:386 - 移动到位置 0x2A
2025-07-23 21:05:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x2A, 力矩=0xFF
2025-07-23 21:05:27 [INFO] Gripper_485.py:386 - 移动到位置 0x2B
2025-07-23 21:05:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x2B, 力矩=0xFF
2025-07-23 21:05:27 [INFO] Gripper_485.py:386 - 移动到位置 0x2A
2025-07-23 21:05:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x2A, 力矩=0xFF
2025-07-23 21:05:27 [INFO] Gripper_485.py:386 - 移动到位置 0x2A
2025-07-23 21:05:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x2A, 力矩=0xFF
2025-07-23 21:05:27 [INFO] Gripper_485.py:386 - 移动到位置 0x2A
2025-07-23 21:05:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x2A, 力矩=0xFF
2025-07-23 21:05:27 [INFO] Gripper_485.py:386 - 移动到位置 0x29
2025-07-23 21:05:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x29, 力矩=0xFF
2025-07-23 21:05:27 [INFO] Gripper_485.py:386 - 移动到位置 0x2A
2025-07-23 21:05:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x2A, 力矩=0xFF
2025-07-23 21:05:27 [INFO] Gripper_485.py:386 - 移动到位置 0x29
2025-07-23 21:05:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x29, 力矩=0xFF
2025-07-23 21:05:27 [INFO] Gripper_485.py:386 - 移动到位置 0x29
2025-07-23 21:05:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x29, 力矩=0xFF
2025-07-23 21:05:27 [INFO] Gripper_485.py:386 - 移动到位置 0x28
2025-07-23 21:05:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x28, 力矩=0xFF
2025-07-23 21:05:27 [INFO] Gripper_485.py:386 - 移动到位置 0x28
2025-07-23 21:05:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x28, 力矩=0xFF
2025-07-23 21:05:27 [INFO] Gripper_485.py:386 - 移动到位置 0x28
2025-07-23 21:05:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x28, 力矩=0xFF
2025-07-23 21:05:27 [INFO] Gripper_485.py:386 - 移动到位置 0x28
2025-07-23 21:05:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x28, 力矩=0xFF
2025-07-23 21:05:27 [INFO] Gripper_485.py:386 - 移动到位置 0x28
2025-07-23 21:05:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x28, 力矩=0xFF
2025-07-23 21:05:27 [INFO] Gripper_485.py:386 - 移动到位置 0x27
2025-07-23 21:05:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x27, 力矩=0xFF
2025-07-23 21:05:27 [INFO] Gripper_485.py:386 - 移动到位置 0x27
2025-07-23 21:05:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x27, 力矩=0xFF
2025-07-23 21:05:27 [INFO] Gripper_485.py:386 - 移动到位置 0x26
2025-07-23 21:05:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x26, 力矩=0xFF
2025-07-23 21:05:27 [INFO] Gripper_485.py:386 - 移动到位置 0x27
2025-07-23 21:05:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x27, 力矩=0xFF
2025-07-23 21:05:27 [INFO] Gripper_485.py:386 - 移动到位置 0x27
2025-07-23 21:05:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x27, 力矩=0xFF
2025-07-23 21:05:27 [INFO] Gripper_485.py:386 - 移动到位置 0x27
2025-07-23 21:05:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x27, 力矩=0xFF
2025-07-23 21:05:27 [INFO] Gripper_485.py:386 - 移动到位置 0x27
2025-07-23 21:05:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x27, 力矩=0xFF
2025-07-23 21:05:27 [INFO] Gripper_485.py:386 - 移动到位置 0x27
2025-07-23 21:05:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x27, 力矩=0xFF
2025-07-23 21:05:27 [INFO] Gripper_485.py:386 - 移动到位置 0x28
2025-07-23 21:05:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x28, 力矩=0xFF
2025-07-23 21:05:27 [INFO] Gripper_485.py:386 - 移动到位置 0x28
2025-07-23 21:05:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x28, 力矩=0xFF
2025-07-23 21:05:27 [INFO] Gripper_485.py:386 - 移动到位置 0x28
2025-07-23 21:05:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x28, 力矩=0xFF
2025-07-23 21:05:27 [INFO] Gripper_485.py:386 - 移动到位置 0x29
2025-07-23 21:05:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x29, 力矩=0xFF
2025-07-23 21:05:27 [INFO] Gripper_485.py:386 - 移动到位置 0x29
2025-07-23 21:05:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x29, 力矩=0xFF
2025-07-23 21:05:27 [INFO] Gripper_485.py:386 - 移动到位置 0x29
2025-07-23 21:05:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x29, 力矩=0xFF
2025-07-23 21:05:27 [INFO] Gripper_485.py:386 - 移动到位置 0x2A
2025-07-23 21:05:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x2A, 力矩=0xFF
2025-07-23 21:05:27 [INFO] Gripper_485.py:386 - 移动到位置 0x2A
2025-07-23 21:05:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x2A, 力矩=0xFF
2025-07-23 21:05:27 [INFO] Gripper_485.py:386 - 移动到位置 0x2A
2025-07-23 21:05:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x2A, 力矩=0xFF
2025-07-23 21:05:27 [INFO] Gripper_485.py:386 - 移动到位置 0x2A
2025-07-23 21:05:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x2A, 力矩=0xFF
2025-07-23 21:05:27 [INFO] Gripper_485.py:386 - 移动到位置 0x2A
2025-07-23 21:05:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x2A, 力矩=0xFF
2025-07-23 21:05:27 [INFO] Gripper_485.py:386 - 移动到位置 0x2B
2025-07-23 21:05:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x2B, 力矩=0xFF
2025-07-23 21:05:27 [INFO] Gripper_485.py:386 - 移动到位置 0x2B
2025-07-23 21:05:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x2B, 力矩=0xFF
2025-07-23 21:05:27 [INFO] Gripper_485.py:386 - 移动到位置 0x2B
2025-07-23 21:05:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x2B, 力矩=0xFF
2025-07-23 21:05:27 [INFO] Gripper_485.py:386 - 移动到位置 0x2A
2025-07-23 21:05:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x2A, 力矩=0xFF
2025-07-23 21:05:27 [INFO] Gripper_485.py:386 - 移动到位置 0x2A
2025-07-23 21:05:27 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x2A, 力矩=0xFF
2025-07-23 21:05:28 [INFO] Gripper_485.py:386 - 移动到位置 0x2B
2025-07-23 21:05:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x2B, 力矩=0xFF
2025-07-23 21:05:28 [INFO] Gripper_485.py:386 - 移动到位置 0x2B
2025-07-23 21:05:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x2B, 力矩=0xFF
2025-07-23 21:05:28 [INFO] Gripper_485.py:386 - 移动到位置 0x2C
2025-07-23 21:05:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x2C, 力矩=0xFF
2025-07-23 21:05:28 [INFO] Gripper_485.py:386 - 移动到位置 0x2E
2025-07-23 21:05:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x2E, 力矩=0xFF
2025-07-23 21:05:28 [INFO] Gripper_485.py:386 - 移动到位置 0x2F
2025-07-23 21:05:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x2F, 力矩=0xFF
2025-07-23 21:05:28 [INFO] Gripper_485.py:386 - 移动到位置 0x32
2025-07-23 21:05:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x32, 力矩=0xFF
2025-07-23 21:05:28 [INFO] Gripper_485.py:386 - 移动到位置 0x39
2025-07-23 21:05:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x39, 力矩=0xFF
2025-07-23 21:05:28 [INFO] Gripper_485.py:386 - 移动到位置 0x3E
2025-07-23 21:05:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x3E, 力矩=0xFF
2025-07-23 21:05:28 [INFO] Gripper_485.py:386 - 移动到位置 0x44
2025-07-23 21:05:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x44, 力矩=0xFF
2025-07-23 21:05:28 [INFO] Gripper_485.py:386 - 移动到位置 0x46
2025-07-23 21:05:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x46, 力矩=0xFF
2025-07-23 21:05:28 [INFO] Gripper_485.py:386 - 移动到位置 0x46
2025-07-23 21:05:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x46, 力矩=0xFF
2025-07-23 21:05:28 [INFO] Gripper_485.py:386 - 移动到位置 0x47
2025-07-23 21:05:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x47, 力矩=0xFF
2025-07-23 21:05:28 [INFO] Gripper_485.py:386 - 移动到位置 0x47
2025-07-23 21:05:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x47, 力矩=0xFF
2025-07-23 21:05:28 [INFO] Gripper_485.py:386 - 移动到位置 0x46
2025-07-23 21:05:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x46, 力矩=0xFF
2025-07-23 21:05:28 [INFO] Gripper_485.py:386 - 移动到位置 0x44
2025-07-23 21:05:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x44, 力矩=0xFF
2025-07-23 21:05:28 [INFO] Gripper_485.py:386 - 移动到位置 0x41
2025-07-23 21:05:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x41, 力矩=0xFF
2025-07-23 21:05:28 [INFO] Gripper_485.py:386 - 移动到位置 0x39
2025-07-23 21:05:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x39, 力矩=0xFF
2025-07-23 21:05:28 [INFO] Gripper_485.py:386 - 移动到位置 0x36
2025-07-23 21:05:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x36, 力矩=0xFF
2025-07-23 21:05:28 [INFO] Gripper_485.py:386 - 移动到位置 0x33
2025-07-23 21:05:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x33, 力矩=0xFF
2025-07-23 21:05:28 [INFO] Gripper_485.py:386 - 移动到位置 0x33
2025-07-23 21:05:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x33, 力矩=0xFF
2025-07-23 21:05:28 [INFO] Gripper_485.py:386 - 移动到位置 0x33
2025-07-23 21:05:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x33, 力矩=0xFF
2025-07-23 21:05:28 [INFO] Gripper_485.py:386 - 移动到位置 0x36
2025-07-23 21:05:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x36, 力矩=0xFF
2025-07-23 21:05:28 [INFO] Gripper_485.py:386 - 移动到位置 0x39
2025-07-23 21:05:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x39, 力矩=0xFF
2025-07-23 21:05:28 [INFO] Gripper_485.py:386 - 移动到位置 0x3B
2025-07-23 21:05:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x3B, 力矩=0xFF
2025-07-23 21:05:28 [INFO] Gripper_485.py:386 - 移动到位置 0x3C
2025-07-23 21:05:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x3C, 力矩=0xFF
2025-07-23 21:05:28 [INFO] Gripper_485.py:386 - 移动到位置 0x3D
2025-07-23 21:05:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x3D, 力矩=0xFF
2025-07-23 21:05:28 [INFO] Gripper_485.py:386 - 移动到位置 0x3F
2025-07-23 21:05:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x3F, 力矩=0xFF
2025-07-23 21:05:28 [INFO] Gripper_485.py:386 - 移动到位置 0x3F
2025-07-23 21:05:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x3F, 力矩=0xFF
2025-07-23 21:05:28 [INFO] Gripper_485.py:386 - 移动到位置 0x3F
2025-07-23 21:05:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x3F, 力矩=0xFF
2025-07-23 21:05:28 [INFO] Gripper_485.py:386 - 移动到位置 0x40
2025-07-23 21:05:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x40, 力矩=0xFF
2025-07-23 21:05:28 [INFO] Gripper_485.py:386 - 移动到位置 0x40
2025-07-23 21:05:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x40, 力矩=0xFF
2025-07-23 21:05:28 [INFO] Gripper_485.py:386 - 移动到位置 0x3F
2025-07-23 21:05:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x3F, 力矩=0xFF
2025-07-23 21:05:28 [INFO] Gripper_485.py:386 - 移动到位置 0x3E
2025-07-23 21:05:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x3E, 力矩=0xFF
2025-07-23 21:05:28 [INFO] Gripper_485.py:386 - 移动到位置 0x3E
2025-07-23 21:05:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x3E, 力矩=0xFF
2025-07-23 21:05:28 [INFO] Gripper_485.py:386 - 移动到位置 0x3C
2025-07-23 21:05:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x3C, 力矩=0xFF
2025-07-23 21:05:28 [INFO] Gripper_485.py:386 - 移动到位置 0x3B
2025-07-23 21:05:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x3B, 力矩=0xFF
2025-07-23 21:05:28 [INFO] Gripper_485.py:386 - 移动到位置 0x3C
2025-07-23 21:05:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x3C, 力矩=0xFF
2025-07-23 21:05:28 [INFO] Gripper_485.py:386 - 移动到位置 0x3B
2025-07-23 21:05:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x3B, 力矩=0xFF
2025-07-23 21:05:28 [INFO] Gripper_485.py:386 - 移动到位置 0x3B
2025-07-23 21:05:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x3B, 力矩=0xFF
2025-07-23 21:05:28 [INFO] Gripper_485.py:386 - 移动到位置 0x3A
2025-07-23 21:05:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x3A, 力矩=0xFF
2025-07-23 21:05:28 [INFO] Gripper_485.py:386 - 移动到位置 0x3A
2025-07-23 21:05:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x3A, 力矩=0xFF
2025-07-23 21:05:28 [INFO] Gripper_485.py:386 - 移动到位置 0x39
2025-07-23 21:05:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x39, 力矩=0xFF
2025-07-23 21:05:28 [INFO] Gripper_485.py:386 - 移动到位置 0x39
2025-07-23 21:05:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x39, 力矩=0xFF
2025-07-23 21:05:28 [INFO] Gripper_485.py:386 - 移动到位置 0x37
2025-07-23 21:05:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x37, 力矩=0xFF
2025-07-23 21:05:28 [INFO] Gripper_485.py:386 - 移动到位置 0x38
2025-07-23 21:05:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x38, 力矩=0xFF
2025-07-23 21:05:28 [INFO] Gripper_485.py:386 - 移动到位置 0x38
2025-07-23 21:05:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x38, 力矩=0xFF
2025-07-23 21:05:28 [INFO] Gripper_485.py:386 - 移动到位置 0x37
2025-07-23 21:05:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x37, 力矩=0xFF
2025-07-23 21:05:28 [INFO] Gripper_485.py:386 - 移动到位置 0x37
2025-07-23 21:05:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x37, 力矩=0xFF
2025-07-23 21:05:28 [INFO] Gripper_485.py:386 - 移动到位置 0x37
2025-07-23 21:05:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x37, 力矩=0xFF
2025-07-23 21:05:28 [INFO] Gripper_485.py:386 - 移动到位置 0x38
2025-07-23 21:05:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x38, 力矩=0xFF
2025-07-23 21:05:28 [INFO] Gripper_485.py:386 - 移动到位置 0x38
2025-07-23 21:05:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x38, 力矩=0xFF
2025-07-23 21:05:28 [INFO] Gripper_485.py:386 - 移动到位置 0x37
2025-07-23 21:05:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x37, 力矩=0xFF
2025-07-23 21:05:28 [INFO] Gripper_485.py:386 - 移动到位置 0x37
2025-07-23 21:05:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x37, 力矩=0xFF
2025-07-23 21:05:28 [INFO] Gripper_485.py:386 - 移动到位置 0x37
2025-07-23 21:05:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x37, 力矩=0xFF
2025-07-23 21:05:28 [INFO] Gripper_485.py:386 - 移动到位置 0x38
2025-07-23 21:05:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x38, 力矩=0xFF
2025-07-23 21:05:28 [INFO] Gripper_485.py:386 - 移动到位置 0x38
2025-07-23 21:05:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x38, 力矩=0xFF
2025-07-23 21:05:28 [INFO] Gripper_485.py:386 - 移动到位置 0x39
2025-07-23 21:05:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x39, 力矩=0xFF
2025-07-23 21:05:28 [INFO] Gripper_485.py:386 - 移动到位置 0x39
2025-07-23 21:05:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x39, 力矩=0xFF
2025-07-23 21:05:28 [INFO] Gripper_485.py:386 - 移动到位置 0x39
2025-07-23 21:05:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x39, 力矩=0xFF
2025-07-23 21:05:28 [INFO] Gripper_485.py:386 - 移动到位置 0x3A
2025-07-23 21:05:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x3A, 力矩=0xFF
2025-07-23 21:05:28 [INFO] Gripper_485.py:386 - 移动到位置 0x39
2025-07-23 21:05:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x39, 力矩=0xFF
2025-07-23 21:05:28 [INFO] Gripper_485.py:386 - 移动到位置 0x3A
2025-07-23 21:05:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x3A, 力矩=0xFF
2025-07-23 21:05:28 [INFO] Gripper_485.py:386 - 移动到位置 0x3A
2025-07-23 21:05:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x3A, 力矩=0xFF
2025-07-23 21:05:28 [INFO] Gripper_485.py:386 - 移动到位置 0x39
2025-07-23 21:05:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x39, 力矩=0xFF
2025-07-23 21:05:28 [INFO] Gripper_485.py:386 - 移动到位置 0x3A
2025-07-23 21:05:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x3A, 力矩=0xFF
2025-07-23 21:05:28 [INFO] Gripper_485.py:386 - 移动到位置 0x3A
2025-07-23 21:05:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x3A, 力矩=0xFF
2025-07-23 21:05:28 [INFO] Gripper_485.py:386 - 移动到位置 0x3A
2025-07-23 21:05:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x3A, 力矩=0xFF
2025-07-23 21:05:28 [INFO] Gripper_485.py:386 - 移动到位置 0x3C
2025-07-23 21:05:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x3C, 力矩=0xFF
2025-07-23 21:05:28 [INFO] Gripper_485.py:386 - 移动到位置 0x3E
2025-07-23 21:05:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x3E, 力矩=0xFF
2025-07-23 21:05:28 [INFO] Gripper_485.py:386 - 移动到位置 0x3F
2025-07-23 21:05:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x3F, 力矩=0xFF
2025-07-23 21:05:28 [INFO] Gripper_485.py:386 - 移动到位置 0x3F
2025-07-23 21:05:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x3F, 力矩=0xFF
2025-07-23 21:05:28 [INFO] Gripper_485.py:386 - 移动到位置 0x41
2025-07-23 21:05:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x41, 力矩=0xFF
2025-07-23 21:05:28 [INFO] Gripper_485.py:386 - 移动到位置 0x41
2025-07-23 21:05:28 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x41, 力矩=0xFF
2025-07-23 21:05:29 [INFO] Gripper_485.py:386 - 移动到位置 0x43
2025-07-23 21:05:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x43, 力矩=0xFF
2025-07-23 21:05:29 [INFO] Gripper_485.py:386 - 移动到位置 0x44
2025-07-23 21:05:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x44, 力矩=0xFF
2025-07-23 21:05:29 [INFO] Gripper_485.py:386 - 移动到位置 0x44
2025-07-23 21:05:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x44, 力矩=0xFF
2025-07-23 21:05:29 [INFO] Gripper_485.py:386 - 移动到位置 0x46
2025-07-23 21:05:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x46, 力矩=0xFF
2025-07-23 21:05:29 [INFO] Gripper_485.py:386 - 移动到位置 0x47
2025-07-23 21:05:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x47, 力矩=0xFF
2025-07-23 21:05:29 [INFO] Gripper_485.py:386 - 移动到位置 0x47
2025-07-23 21:05:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x47, 力矩=0xFF
2025-07-23 21:05:29 [INFO] Gripper_485.py:386 - 移动到位置 0x47
2025-07-23 21:05:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x47, 力矩=0xFF
2025-07-23 21:05:29 [INFO] Gripper_485.py:386 - 移动到位置 0x47
2025-07-23 21:05:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x47, 力矩=0xFF
2025-07-23 21:05:29 [INFO] Gripper_485.py:386 - 移动到位置 0x47
2025-07-23 21:05:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x47, 力矩=0xFF
2025-07-23 21:05:29 [INFO] Gripper_485.py:386 - 移动到位置 0x47
2025-07-23 21:05:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x47, 力矩=0xFF
2025-07-23 21:05:29 [INFO] Gripper_485.py:386 - 移动到位置 0x47
2025-07-23 21:05:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x47, 力矩=0xFF
2025-07-23 21:05:29 [INFO] Gripper_485.py:386 - 移动到位置 0x47
2025-07-23 21:05:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x47, 力矩=0xFF
2025-07-23 21:05:29 [INFO] Gripper_485.py:386 - 移动到位置 0x47
2025-07-23 21:05:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x47, 力矩=0xFF
2025-07-23 21:05:29 [INFO] Gripper_485.py:386 - 移动到位置 0x47
2025-07-23 21:05:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x47, 力矩=0xFF
2025-07-23 21:05:29 [INFO] Gripper_485.py:386 - 移动到位置 0x47
2025-07-23 21:05:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x47, 力矩=0xFF
2025-07-23 21:05:29 [INFO] Gripper_485.py:386 - 移动到位置 0x47
2025-07-23 21:05:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x47, 力矩=0xFF
2025-07-23 21:05:29 [INFO] Gripper_485.py:386 - 移动到位置 0x47
2025-07-23 21:05:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x47, 力矩=0xFF
2025-07-23 21:05:29 [INFO] Gripper_485.py:386 - 移动到位置 0x47
2025-07-23 21:05:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x47, 力矩=0xFF
2025-07-23 21:05:29 [INFO] Gripper_485.py:386 - 移动到位置 0x47
2025-07-23 21:05:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x47, 力矩=0xFF
2025-07-23 21:05:29 [INFO] Gripper_485.py:386 - 移动到位置 0x46
2025-07-23 21:05:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x46, 力矩=0xFF
2025-07-23 21:05:29 [INFO] Gripper_485.py:386 - 移动到位置 0x47
2025-07-23 21:05:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x47, 力矩=0xFF
2025-07-23 21:05:29 [INFO] Gripper_485.py:386 - 移动到位置 0x46
2025-07-23 21:05:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x46, 力矩=0xFF
2025-07-23 21:05:29 [INFO] Gripper_485.py:386 - 移动到位置 0x45
2025-07-23 21:05:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x45, 力矩=0xFF
2025-07-23 21:05:29 [INFO] Gripper_485.py:386 - 移动到位置 0x45
2025-07-23 21:05:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x45, 力矩=0xFF
2025-07-23 21:05:29 [INFO] Gripper_485.py:386 - 移动到位置 0x45
2025-07-23 21:05:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x45, 力矩=0xFF
2025-07-23 21:05:29 [INFO] Gripper_485.py:386 - 移动到位置 0x46
2025-07-23 21:05:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x46, 力矩=0xFF
2025-07-23 21:05:29 [INFO] Gripper_485.py:386 - 移动到位置 0x47
2025-07-23 21:05:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x47, 力矩=0xFF
2025-07-23 21:05:29 [INFO] Gripper_485.py:386 - 移动到位置 0x46
2025-07-23 21:05:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x46, 力矩=0xFF
2025-07-23 21:05:29 [INFO] Gripper_485.py:386 - 移动到位置 0x47
2025-07-23 21:05:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x47, 力矩=0xFF
2025-07-23 21:05:29 [INFO] Gripper_485.py:386 - 移动到位置 0x48
2025-07-23 21:05:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x48, 力矩=0xFF
2025-07-23 21:05:29 [INFO] Gripper_485.py:386 - 移动到位置 0x47
2025-07-23 21:05:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x47, 力矩=0xFF
2025-07-23 21:05:29 [INFO] Gripper_485.py:386 - 移动到位置 0x48
2025-07-23 21:05:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x48, 力矩=0xFF
2025-07-23 21:05:29 [INFO] Gripper_485.py:386 - 移动到位置 0x48
2025-07-23 21:05:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x48, 力矩=0xFF
2025-07-23 21:05:29 [INFO] Gripper_485.py:386 - 移动到位置 0x48
2025-07-23 21:05:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x48, 力矩=0xFF
2025-07-23 21:05:29 [INFO] Gripper_485.py:386 - 移动到位置 0x48
2025-07-23 21:05:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x48, 力矩=0xFF
2025-07-23 21:05:29 [INFO] Gripper_485.py:386 - 移动到位置 0x47
2025-07-23 21:05:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x47, 力矩=0xFF
2025-07-23 21:05:29 [INFO] Gripper_485.py:386 - 移动到位置 0x48
2025-07-23 21:05:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x48, 力矩=0xFF
2025-07-23 21:05:29 [INFO] Gripper_485.py:386 - 移动到位置 0x48
2025-07-23 21:05:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x48, 力矩=0xFF
2025-07-23 21:05:29 [INFO] Gripper_485.py:386 - 移动到位置 0x47
2025-07-23 21:05:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x47, 力矩=0xFF
2025-07-23 21:05:29 [INFO] Gripper_485.py:386 - 移动到位置 0x47
2025-07-23 21:05:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x47, 力矩=0xFF
2025-07-23 21:05:29 [INFO] Gripper_485.py:386 - 移动到位置 0x48
2025-07-23 21:05:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x48, 力矩=0xFF
2025-07-23 21:05:29 [INFO] Gripper_485.py:386 - 移动到位置 0x48
2025-07-23 21:05:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x48, 力矩=0xFF
2025-07-23 21:05:29 [INFO] Gripper_485.py:386 - 移动到位置 0x48
2025-07-23 21:05:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x48, 力矩=0xFF
2025-07-23 21:05:29 [INFO] Gripper_485.py:386 - 移动到位置 0x49
2025-07-23 21:05:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x49, 力矩=0xFF
2025-07-23 21:05:29 [INFO] Gripper_485.py:386 - 移动到位置 0x48
2025-07-23 21:05:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x48, 力矩=0xFF
2025-07-23 21:05:29 [INFO] Gripper_485.py:386 - 移动到位置 0x49
2025-07-23 21:05:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x49, 力矩=0xFF
2025-07-23 21:05:29 [INFO] Gripper_485.py:386 - 移动到位置 0x48
2025-07-23 21:05:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x48, 力矩=0xFF
2025-07-23 21:05:29 [INFO] Gripper_485.py:386 - 移动到位置 0x48
2025-07-23 21:05:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x48, 力矩=0xFF
2025-07-23 21:05:29 [INFO] Gripper_485.py:386 - 移动到位置 0x48
2025-07-23 21:05:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x48, 力矩=0xFF
2025-07-23 21:05:29 [INFO] Gripper_485.py:386 - 移动到位置 0x48
2025-07-23 21:05:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x48, 力矩=0xFF
2025-07-23 21:05:29 [INFO] Gripper_485.py:386 - 移动到位置 0x48
2025-07-23 21:05:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x48, 力矩=0xFF
2025-07-23 21:05:29 [INFO] Gripper_485.py:386 - 移动到位置 0x49
2025-07-23 21:05:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x49, 力矩=0xFF
2025-07-23 21:05:29 [INFO] Gripper_485.py:386 - 移动到位置 0x49
2025-07-23 21:05:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x49, 力矩=0xFF
2025-07-23 21:05:29 [INFO] Gripper_485.py:386 - 移动到位置 0x4A
2025-07-23 21:05:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x4A, 力矩=0xFF
2025-07-23 21:05:29 [INFO] Gripper_485.py:386 - 移动到位置 0x4A
2025-07-23 21:05:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x4A, 力矩=0xFF
2025-07-23 21:05:29 [INFO] Gripper_485.py:386 - 移动到位置 0x4A
2025-07-23 21:05:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x4A, 力矩=0xFF
2025-07-23 21:05:29 [INFO] Gripper_485.py:386 - 移动到位置 0x4B
2025-07-23 21:05:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x4B, 力矩=0xFF
2025-07-23 21:05:29 [INFO] Gripper_485.py:386 - 移动到位置 0x4B
2025-07-23 21:05:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x4B, 力矩=0xFF
2025-07-23 21:05:29 [INFO] Gripper_485.py:386 - 移动到位置 0x4B
2025-07-23 21:05:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x4B, 力矩=0xFF
2025-07-23 21:05:29 [INFO] Gripper_485.py:386 - 移动到位置 0x4A
2025-07-23 21:05:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x4A, 力矩=0xFF
2025-07-23 21:05:29 [INFO] Gripper_485.py:386 - 移动到位置 0x4A
2025-07-23 21:05:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x4A, 力矩=0xFF
2025-07-23 21:05:29 [INFO] Gripper_485.py:386 - 移动到位置 0x4A
2025-07-23 21:05:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x4A, 力矩=0xFF
2025-07-23 21:05:29 [INFO] Gripper_485.py:386 - 移动到位置 0x4A
2025-07-23 21:05:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x4A, 力矩=0xFF
2025-07-23 21:05:29 [INFO] Gripper_485.py:386 - 移动到位置 0x4A
2025-07-23 21:05:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x4A, 力矩=0xFF
2025-07-23 21:05:29 [INFO] Gripper_485.py:386 - 移动到位置 0x4B
2025-07-23 21:05:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x4B, 力矩=0xFF
2025-07-23 21:05:29 [INFO] Gripper_485.py:386 - 移动到位置 0x4B
2025-07-23 21:05:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x4B, 力矩=0xFF
2025-07-23 21:05:29 [INFO] Gripper_485.py:386 - 移动到位置 0x4B
2025-07-23 21:05:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x4B, 力矩=0xFF
2025-07-23 21:05:29 [INFO] Gripper_485.py:386 - 移动到位置 0x4B
2025-07-23 21:05:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x4B, 力矩=0xFF
2025-07-23 21:05:29 [INFO] Gripper_485.py:386 - 移动到位置 0x4B
2025-07-23 21:05:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x4B, 力矩=0xFF
2025-07-23 21:05:29 [INFO] Gripper_485.py:386 - 移动到位置 0x4B
2025-07-23 21:05:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x4B, 力矩=0xFF
2025-07-23 21:05:29 [INFO] Gripper_485.py:386 - 移动到位置 0x4B
2025-07-23 21:05:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x4B, 力矩=0xFF
2025-07-23 21:05:29 [INFO] Gripper_485.py:386 - 移动到位置 0x4B
2025-07-23 21:05:29 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x4B, 力矩=0xFF
2025-07-23 21:05:30 [INFO] Gripper_485.py:386 - 移动到位置 0x4B
2025-07-23 21:05:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x4B, 力矩=0xFF
2025-07-23 21:05:30 [INFO] Gripper_485.py:386 - 移动到位置 0x4B
2025-07-23 21:05:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x4B, 力矩=0xFF
2025-07-23 21:05:30 [INFO] Gripper_485.py:386 - 移动到位置 0x4B
2025-07-23 21:05:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x4B, 力矩=0xFF
2025-07-23 21:05:30 [INFO] Gripper_485.py:386 - 移动到位置 0x4B
2025-07-23 21:05:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x4B, 力矩=0xFF
2025-07-23 21:05:30 [INFO] Gripper_485.py:386 - 移动到位置 0x4C
2025-07-23 21:05:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x4C, 力矩=0xFF
2025-07-23 21:05:30 [INFO] Gripper_485.py:386 - 移动到位置 0x4B
2025-07-23 21:05:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x4B, 力矩=0xFF
2025-07-23 21:05:30 [INFO] Gripper_485.py:386 - 移动到位置 0x4B
2025-07-23 21:05:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x4B, 力矩=0xFF
2025-07-23 21:05:30 [INFO] Gripper_485.py:386 - 移动到位置 0x4C
2025-07-23 21:05:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x4C, 力矩=0xFF
2025-07-23 21:05:30 [INFO] Gripper_485.py:386 - 移动到位置 0x4D
2025-07-23 21:05:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x4D, 力矩=0xFF
2025-07-23 21:05:30 [INFO] Gripper_485.py:386 - 移动到位置 0x4F
2025-07-23 21:05:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x4F, 力矩=0xFF
2025-07-23 21:05:30 [INFO] Gripper_485.py:386 - 移动到位置 0x4F
2025-07-23 21:05:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x4F, 力矩=0xFF
2025-07-23 21:05:30 [INFO] Gripper_485.py:386 - 移动到位置 0x4F
2025-07-23 21:05:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x4F, 力矩=0xFF
2025-07-23 21:05:30 [INFO] Gripper_485.py:386 - 移动到位置 0x4F
2025-07-23 21:05:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x4F, 力矩=0xFF
2025-07-23 21:05:30 [INFO] Gripper_485.py:386 - 移动到位置 0x50
2025-07-23 21:05:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x50, 力矩=0xFF
2025-07-23 21:05:30 [INFO] Gripper_485.py:386 - 移动到位置 0x4F
2025-07-23 21:05:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x4F, 力矩=0xFF
2025-07-23 21:05:30 [INFO] Gripper_485.py:386 - 移动到位置 0x4F
2025-07-23 21:05:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x4F, 力矩=0xFF
2025-07-23 21:05:30 [INFO] Gripper_485.py:386 - 移动到位置 0x4E
2025-07-23 21:05:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x4E, 力矩=0xFF
2025-07-23 21:05:30 [INFO] Gripper_485.py:386 - 移动到位置 0x4F
2025-07-23 21:05:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x4F, 力矩=0xFF
2025-07-23 21:05:30 [INFO] Gripper_485.py:386 - 移动到位置 0x4E
2025-07-23 21:05:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x4E, 力矩=0xFF
2025-07-23 21:05:30 [INFO] Gripper_485.py:386 - 移动到位置 0x4E
2025-07-23 21:05:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x4E, 力矩=0xFF
2025-07-23 21:05:30 [INFO] Gripper_485.py:386 - 移动到位置 0x4D
2025-07-23 21:05:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x4D, 力矩=0xFF
2025-07-23 21:05:30 [INFO] Gripper_485.py:386 - 移动到位置 0x4D
2025-07-23 21:05:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x4D, 力矩=0xFF
2025-07-23 21:05:30 [INFO] Gripper_485.py:386 - 移动到位置 0x4E
2025-07-23 21:05:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x4E, 力矩=0xFF
2025-07-23 21:05:30 [INFO] Gripper_485.py:386 - 移动到位置 0x4E
2025-07-23 21:05:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x4E, 力矩=0xFF
2025-07-23 21:05:30 [INFO] Gripper_485.py:386 - 移动到位置 0x4F
2025-07-23 21:05:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x4F, 力矩=0xFF
2025-07-23 21:05:30 [INFO] Gripper_485.py:386 - 移动到位置 0x4F
2025-07-23 21:05:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x4F, 力矩=0xFF
2025-07-23 21:05:30 [INFO] Gripper_485.py:386 - 移动到位置 0x50
2025-07-23 21:05:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x50, 力矩=0xFF
2025-07-23 21:05:30 [INFO] Gripper_485.py:386 - 移动到位置 0x4F
2025-07-23 21:05:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x4F, 力矩=0xFF
2025-07-23 21:05:30 [INFO] Gripper_485.py:386 - 移动到位置 0x51
2025-07-23 21:05:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x51, 力矩=0xFF
2025-07-23 21:05:30 [INFO] Gripper_485.py:386 - 移动到位置 0x52
2025-07-23 21:05:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x52, 力矩=0xFF
2025-07-23 21:05:30 [INFO] Gripper_485.py:386 - 移动到位置 0x53
2025-07-23 21:05:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x53, 力矩=0xFF
2025-07-23 21:05:30 [INFO] Gripper_485.py:386 - 移动到位置 0x55
2025-07-23 21:05:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x55, 力矩=0xFF
2025-07-23 21:05:30 [INFO] Gripper_485.py:386 - 移动到位置 0x57
2025-07-23 21:05:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x57, 力矩=0xFF
2025-07-23 21:05:30 [INFO] Gripper_485.py:386 - 移动到位置 0x59
2025-07-23 21:05:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x59, 力矩=0xFF
2025-07-23 21:05:30 [INFO] Gripper_485.py:386 - 移动到位置 0x59
2025-07-23 21:05:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x59, 力矩=0xFF
2025-07-23 21:05:30 [INFO] Gripper_485.py:386 - 移动到位置 0x5C
2025-07-23 21:05:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x5C, 力矩=0xFF
2025-07-23 21:05:30 [INFO] Gripper_485.py:386 - 移动到位置 0x5D
2025-07-23 21:05:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x5D, 力矩=0xFF
2025-07-23 21:05:30 [INFO] Gripper_485.py:386 - 移动到位置 0x5E
2025-07-23 21:05:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x5E, 力矩=0xFF
2025-07-23 21:05:30 [INFO] Gripper_485.py:386 - 移动到位置 0x5F
2025-07-23 21:05:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x5F, 力矩=0xFF
2025-07-23 21:05:30 [INFO] Gripper_485.py:386 - 移动到位置 0x60
2025-07-23 21:05:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x60, 力矩=0xFF
2025-07-23 21:05:30 [INFO] Gripper_485.py:386 - 移动到位置 0x60
2025-07-23 21:05:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x60, 力矩=0xFF
2025-07-23 21:05:30 [INFO] Gripper_485.py:386 - 移动到位置 0x5F
2025-07-23 21:05:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x5F, 力矩=0xFF
2025-07-23 21:05:30 [INFO] Gripper_485.py:386 - 移动到位置 0x5F
2025-07-23 21:05:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x5F, 力矩=0xFF
2025-07-23 21:05:30 [INFO] Gripper_485.py:386 - 移动到位置 0x60
2025-07-23 21:05:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x60, 力矩=0xFF
2025-07-23 21:05:30 [INFO] Gripper_485.py:386 - 移动到位置 0x60
2025-07-23 21:05:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x60, 力矩=0xFF
2025-07-23 21:05:30 [INFO] Gripper_485.py:386 - 移动到位置 0x61
2025-07-23 21:05:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x61, 力矩=0xFF
2025-07-23 21:05:30 [INFO] Gripper_485.py:386 - 移动到位置 0x63
2025-07-23 21:05:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x63, 力矩=0xFF
2025-07-23 21:05:30 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-23 21:05:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-23 21:05:30 [INFO] Gripper_485.py:386 - 移动到位置 0x63
2025-07-23 21:05:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x63, 力矩=0xFF
2025-07-23 21:05:30 [INFO] Gripper_485.py:386 - 移动到位置 0x63
2025-07-23 21:05:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x63, 力矩=0xFF
2025-07-23 21:05:30 [INFO] Gripper_485.py:386 - 移动到位置 0x63
2025-07-23 21:05:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x63, 力矩=0xFF
2025-07-23 21:05:30 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-23 21:05:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-23 21:05:30 [INFO] Gripper_485.py:386 - 移动到位置 0x63
2025-07-23 21:05:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x63, 力矩=0xFF
2025-07-23 21:05:30 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-23 21:05:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-23 21:05:30 [INFO] Gripper_485.py:386 - 移动到位置 0x63
2025-07-23 21:05:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x63, 力矩=0xFF
2025-07-23 21:05:30 [INFO] Gripper_485.py:386 - 移动到位置 0x63
2025-07-23 21:05:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x63, 力矩=0xFF
2025-07-23 21:05:30 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-23 21:05:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-23 21:05:30 [INFO] Gripper_485.py:386 - 移动到位置 0x63
2025-07-23 21:05:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x63, 力矩=0xFF
2025-07-23 21:05:30 [INFO] Gripper_485.py:386 - 移动到位置 0x63
2025-07-23 21:05:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x63, 力矩=0xFF
2025-07-23 21:05:30 [INFO] Gripper_485.py:386 - 移动到位置 0x63
2025-07-23 21:05:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x63, 力矩=0xFF
2025-07-23 21:05:30 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-23 21:05:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-23 21:05:30 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-23 21:05:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-23 21:05:30 [INFO] Gripper_485.py:386 - 移动到位置 0x67
2025-07-23 21:05:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x67, 力矩=0xFF
2025-07-23 21:05:30 [INFO] Gripper_485.py:386 - 移动到位置 0x68
2025-07-23 21:05:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x68, 力矩=0xFF
2025-07-23 21:05:30 [INFO] Gripper_485.py:386 - 移动到位置 0x69
2025-07-23 21:05:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x69, 力矩=0xFF
2025-07-23 21:05:30 [INFO] Gripper_485.py:386 - 移动到位置 0x6A
2025-07-23 21:05:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x6A, 力矩=0xFF
2025-07-23 21:05:30 [INFO] Gripper_485.py:386 - 移动到位置 0x6A
2025-07-23 21:05:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x6A, 力矩=0xFF
2025-07-23 21:05:30 [INFO] Gripper_485.py:386 - 移动到位置 0x6B
2025-07-23 21:05:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x6B, 力矩=0xFF
2025-07-23 21:05:30 [INFO] Gripper_485.py:386 - 移动到位置 0x6B
2025-07-23 21:05:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x6B, 力矩=0xFF
2025-07-23 21:05:30 [INFO] Gripper_485.py:386 - 移动到位置 0x6B
2025-07-23 21:05:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x6B, 力矩=0xFF
2025-07-23 21:05:30 [INFO] Gripper_485.py:386 - 移动到位置 0x6A
2025-07-23 21:05:30 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x6A, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x6A
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x6A, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x69
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x69, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x69
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x69, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x69
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x69, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x69
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x69, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x68
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x68, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x68
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x68, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x68
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x68, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x67
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x67, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x67
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x67, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x67
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x67, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x67
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x67, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x67
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x67, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x66
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x66, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x67
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x67, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x66
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x66, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x67
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x67, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x67
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x67, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x66
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x66, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x65
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x65, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x65
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x65, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x65
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x65, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0xEE
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xEE, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0xE3
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xE3, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0xD6
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xD6, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0xBD
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xBD, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0xB4
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xB4, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0xAA
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xAA, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x95
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x95, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x65
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x65, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x85
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x85, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x63
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x63, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x4C
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x4C, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x2E
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x2E, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x63
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x63, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x0C
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0C, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x63
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x63, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x0A
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0A, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x63
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x63, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x07
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x07, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x61
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x61, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x06
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x06, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x60
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x60, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x06
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x06, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x60
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x60, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x06
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x06, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x60
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x60, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x06
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x06, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x61
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x61, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x03
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x03, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x60
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x60, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x02
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x02, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x61
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x61, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x02
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x02, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x61
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x61, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x03
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x03, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x61
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x61, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x02
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x02, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x61
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x61, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x02
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x02, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x60
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x60, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x03
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x03, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x61
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x61, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x03
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x03, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x61
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x61, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x02
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x02, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x61
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x61, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x01
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x01, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x61
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x61, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x02
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x02, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x61
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x61, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x01
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x01, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x60
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x60, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x60
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x60, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x60
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x60, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x60
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x60, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x01
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x01, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x5F
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x5F, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x5F
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x5F, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x01
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x01, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x60
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x60, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x5F
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x5F, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x60
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x60, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x60
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x60, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x5F
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x5F, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x5F
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x5F, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x5F
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x5F, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x5E
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x5E, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x5E
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x5E, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x5E
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x5E, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x5E
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x5E, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x5E
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x5E, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x5D
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x5D, 力矩=0xFF
2025-07-23 21:05:31 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:31 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0x5D
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x5D, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0x5D
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x5D, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0x5D
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x5D, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0x5C
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x5C, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0x02
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x02, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0x5C
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x5C, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0x03
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x03, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0x5B
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x5B, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0x05
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x05, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0x5C
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x5C, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0x08
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x08, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0x5C
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x5C, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0x08
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x08, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0x5B
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x5B, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0x0A
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0A, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0x5B
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x5B, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0x0E
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0E, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0x5A
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x5A, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0x0F
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0F, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0x5C
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x5C, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0x19
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x19, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0x5B
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x5B, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0x1B
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x1B, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0x5B
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x5B, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0x1E
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x1E, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0x5B
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x5B, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0x27
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x27, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0x5B
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x5B, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0x2D
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x2D, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0x5B
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x5B, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0x40
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x40, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0x5B
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x5B, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0x4C
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x4C, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0x5B
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x5B, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0x5B
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x5B, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0x5C
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x5C, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0x83
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x83, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0x5B
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x5B, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0x9D
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x9D, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0x5C
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x5C, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0xD2
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xD2, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0x5B
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x5B, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0xE8
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xE8, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0x5B
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x5B, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0xF8
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF8, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0x5A
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x5A, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0x5A
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x5A, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0x5B
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x5B, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0x5B
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x5B, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0x5C
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x5C, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0x5D
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x5D, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0x5F
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x5F, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0x60
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x60, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0x62
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x62, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0x64
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x64, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0x65
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x65, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0x69
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x69, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0x6B
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x6B, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0x6D
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x6D, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0x72
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x72, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0x75
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x75, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0x79
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x79, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0x7E
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x7E, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0x7F
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x7F, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0x80
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x80, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0x80
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x80, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0x80
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x80, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0x81
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x81, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0x80
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x80, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0x81
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x81, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0x82
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x82, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0x83
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x83, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0x84
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x84, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0x88
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x88, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0x9B
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x9B, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0xB0
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xB0, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0xC9
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xC9, 力矩=0xFF
2025-07-23 21:05:32 [INFO] Gripper_485.py:386 - 移动到位置 0xE6
2025-07-23 21:05:32 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xE6, 力矩=0xFF
2025-07-23 21:05:33 [INFO] Gripper_485.py:386 - 移动到位置 0xFA
2025-07-23 21:05:33 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFA, 力矩=0xFF
2025-07-23 21:05:33 [INFO] Gripper_485.py:386 - 移动到位置 0xD2
2025-07-23 21:05:33 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xD2, 力矩=0xFF
2025-07-23 21:05:33 [INFO] Gripper_485.py:386 - 移动到位置 0xB1
2025-07-23 21:05:33 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xB1, 力矩=0xFF
2025-07-23 21:05:33 [INFO] Gripper_485.py:386 - 移动到位置 0xEF
2025-07-23 21:05:33 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xEF, 力矩=0xFF
2025-07-23 21:05:33 [INFO] Gripper_485.py:386 - 移动到位置 0x42
2025-07-23 21:05:33 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x42, 力矩=0xFF
2025-07-23 21:05:33 [INFO] Gripper_485.py:386 - 移动到位置 0xE2
2025-07-23 21:05:33 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xE2, 力矩=0xFF
2025-07-23 21:05:33 [INFO] Gripper_485.py:386 - 移动到位置 0x1A
2025-07-23 21:05:33 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x1A, 力矩=0xFF
2025-07-23 21:05:33 [INFO] Gripper_485.py:386 - 移动到位置 0xC4
2025-07-23 21:05:33 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xC4, 力矩=0xFF
2025-07-23 21:05:33 [INFO] Gripper_485.py:386 - 移动到位置 0x04
2025-07-23 21:05:33 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x04, 力矩=0xFF
2025-07-23 21:05:33 [INFO] Gripper_485.py:386 - 移动到位置 0xAE
2025-07-23 21:05:33 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xAE, 力矩=0xFF
2025-07-23 21:05:33 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:33 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:33 [INFO] Gripper_485.py:386 - 移动到位置 0x68
2025-07-23 21:05:33 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x68, 力矩=0xFF
2025-07-23 21:05:33 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:33 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:33 [INFO] Gripper_485.py:386 - 移动到位置 0x40
2025-07-23 21:05:33 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x40, 力矩=0xFF
2025-07-23 21:05:33 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:33 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:33 [INFO] Gripper_485.py:386 - 移动到位置 0x1A
2025-07-23 21:05:33 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x1A, 力矩=0xFF
2025-07-23 21:05:33 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:33 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:33 [INFO] Gripper_485.py:386 - 移动到位置 0x0B
2025-07-23 21:05:33 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0B, 力矩=0xFF
2025-07-23 21:05:33 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:33 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:33 [INFO] Gripper_485.py:386 - 移动到位置 0x0C
2025-07-23 21:05:33 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0C, 力矩=0xFF
2025-07-23 21:05:33 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:33 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:33 [INFO] Gripper_485.py:386 - 移动到位置 0x0B
2025-07-23 21:05:33 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0B, 力矩=0xFF
2025-07-23 21:05:33 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:33 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:33 [INFO] Gripper_485.py:386 - 移动到位置 0x06
2025-07-23 21:05:33 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x06, 力矩=0xFF
2025-07-23 21:05:33 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:33 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:33 [INFO] Gripper_485.py:386 - 移动到位置 0x03
2025-07-23 21:05:33 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x03, 力矩=0xFF
2025-07-23 21:05:33 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:33 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:33 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:33 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:33 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:33 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:33 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:33 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:33 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:33 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:33 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:33 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:33 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:33 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:33 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:33 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:33 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:33 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:33 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:33 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:33 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:33 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:33 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:33 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:33 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:33 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:33 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:33 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:33 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:33 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:33 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:33 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:33 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:33 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:33 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:33 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:33 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:33 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:33 [INFO] Gripper_485.py:386 - 移动到位置 0x02
2025-07-23 21:05:33 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x02, 力矩=0xFF
2025-07-23 21:05:34 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:34 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:34 [INFO] Gripper_485.py:386 - 移动到位置 0x03
2025-07-23 21:05:34 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x03, 力矩=0xFF
2025-07-23 21:05:34 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:34 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:34 [INFO] Gripper_485.py:386 - 移动到位置 0x03
2025-07-23 21:05:34 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x03, 力矩=0xFF
2025-07-23 21:05:34 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:34 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:34 [INFO] Gripper_485.py:386 - 移动到位置 0x03
2025-07-23 21:05:34 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x03, 力矩=0xFF
2025-07-23 21:05:34 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:34 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:34 [INFO] Gripper_485.py:386 - 移动到位置 0x02
2025-07-23 21:05:34 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x02, 力矩=0xFF
2025-07-23 21:05:34 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:34 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:34 [INFO] Gripper_485.py:386 - 移动到位置 0x02
2025-07-23 21:05:34 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x02, 力矩=0xFF
2025-07-23 21:05:34 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:34 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:34 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:34 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:34 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:34 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:34 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:34 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:34 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:34 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:34 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:34 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:34 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:34 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:34 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:34 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:34 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:34 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:34 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:34 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:34 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:34 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:34 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:34 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:34 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:34 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:34 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:34 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:34 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:34 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:34 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:34 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:34 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:34 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:34 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:34 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:34 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:34 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:34 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:34 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:34 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:34 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:34 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:34 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:34 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:34 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:34 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:34 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:34 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:34 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:34 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:34 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:34 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:34 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:34 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:34 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:34 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:34 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:34 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:34 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:34 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:34 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:34 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:34 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:34 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:34 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:34 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:34 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:34 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:34 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:34 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:34 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:34 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:34 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:34 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:34 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:34 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:34 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:34 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:34 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:34 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:34 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:34 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:34 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:34 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:34 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:34 [INFO] Gripper_485.py:386 - 移动到位置 0x03
2025-07-23 21:05:34 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x03, 力矩=0xFF
2025-07-23 21:05:34 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:34 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:34 [INFO] Gripper_485.py:386 - 移动到位置 0x05
2025-07-23 21:05:34 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x05, 力矩=0xFF
2025-07-23 21:05:34 [INFO] Gripper_485.py:386 - 移动到位置 0x04
2025-07-23 21:05:34 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x04, 力矩=0xFF
2025-07-23 21:05:34 [INFO] Gripper_485.py:386 - 移动到位置 0x0A
2025-07-23 21:05:34 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0A, 力矩=0xFF
2025-07-23 21:05:34 [INFO] Gripper_485.py:386 - 移动到位置 0x21
2025-07-23 21:05:34 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x21, 力矩=0xFF
2025-07-23 21:05:34 [INFO] Gripper_485.py:386 - 移动到位置 0x38
2025-07-23 21:05:34 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x38, 力矩=0xFF
2025-07-23 21:05:34 [INFO] Gripper_485.py:386 - 移动到位置 0x3C
2025-07-23 21:05:34 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x3C, 力矩=0xFF
2025-07-23 21:05:34 [INFO] Gripper_485.py:386 - 移动到位置 0x6D
2025-07-23 21:05:34 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x6D, 力矩=0xFF
2025-07-23 21:05:34 [INFO] Gripper_485.py:386 - 移动到位置 0x66
2025-07-23 21:05:34 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x66, 力矩=0xFF
2025-07-23 21:05:34 [INFO] Gripper_485.py:386 - 移动到位置 0xDE
2025-07-23 21:05:34 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xDE, 力矩=0xFF
2025-07-23 21:05:34 [INFO] Gripper_485.py:386 - 移动到位置 0xAA
2025-07-23 21:05:34 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xAA, 力矩=0xFF
2025-07-23 21:05:34 [INFO] Gripper_485.py:386 - 移动到位置 0xC2
2025-07-23 21:05:34 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xC2, 力矩=0xFF
2025-07-23 21:05:34 [INFO] Gripper_485.py:386 - 移动到位置 0xE4
2025-07-23 21:05:34 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xE4, 力矩=0xFF
2025-07-23 21:05:34 [INFO] Gripper_485.py:386 - 移动到位置 0xF0
2025-07-23 21:05:34 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF0, 力矩=0xFF
2025-07-23 21:05:34 [INFO] Gripper_485.py:386 - 移动到位置 0xFA
2025-07-23 21:05:34 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFA, 力矩=0xFF
2025-07-23 21:05:42 [INFO] Gripper_485.py:386 - 移动到位置 0xFA
2025-07-23 21:05:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFA, 力矩=0xFF
2025-07-23 21:05:42 [INFO] Gripper_485.py:386 - 移动到位置 0xF4
2025-07-23 21:05:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF4, 力矩=0xFF
2025-07-23 21:05:42 [INFO] Gripper_485.py:386 - 移动到位置 0xE6
2025-07-23 21:05:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xE6, 力矩=0xFF
2025-07-23 21:05:42 [INFO] Gripper_485.py:386 - 移动到位置 0xD7
2025-07-23 21:05:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xD7, 力矩=0xFF
2025-07-23 21:05:42 [INFO] Gripper_485.py:386 - 移动到位置 0xC0
2025-07-23 21:05:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xC0, 力矩=0xFF
2025-07-23 21:05:42 [INFO] Gripper_485.py:386 - 移动到位置 0x6F
2025-07-23 21:05:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x6F, 力矩=0xFF
2025-07-23 21:05:42 [INFO] Gripper_485.py:386 - 移动到位置 0x33
2025-07-23 21:05:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x33, 力矩=0xFF
2025-07-23 21:05:42 [INFO] Gripper_485.py:386 - 移动到位置 0x04
2025-07-23 21:05:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x04, 力矩=0xFF
2025-07-23 21:05:42 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:42 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:42 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:42 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:42 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:42 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:42 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:42 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:42 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:42 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:42 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:43 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:43 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:43 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:43 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:43 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:43 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:43 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:43 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:43 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:43 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:43 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:43 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:43 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:43 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:43 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:43 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:43 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:43 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-23 21:05:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-23 21:05:43 [INFO] Gripper_485.py:386 - 移动到位置 0x0A
2025-07-23 21:05:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x0A, 力矩=0xFF
2025-07-23 21:05:43 [INFO] Gripper_485.py:386 - 移动到位置 0x24
2025-07-23 21:05:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x24, 力矩=0xFF
2025-07-23 21:05:43 [INFO] Gripper_485.py:386 - 移动到位置 0x5E
2025-07-23 21:05:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x5E, 力矩=0xFF
2025-07-23 21:05:43 [INFO] Gripper_485.py:386 - 移动到位置 0xC2
2025-07-23 21:05:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xC2, 力矩=0xFF
2025-07-23 21:05:43 [INFO] Gripper_485.py:386 - 移动到位置 0xE1
2025-07-23 21:05:43 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xE1, 力矩=0xFF
2025-07-23 21:06:01 [INFO] Gripper_485.py:386 - 移动到位置 0xF7
2025-07-23 21:06:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF7, 力矩=0xFF
2025-07-23 21:06:01 [INFO] Gripper_485.py:386 - 移动到位置 0xF2
2025-07-23 21:06:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF2, 力矩=0xFF
2025-07-23 21:06:01 [INFO] Gripper_485.py:386 - 移动到位置 0xEB
2025-07-23 21:06:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xEB, 力矩=0xFF
2025-07-23 21:06:01 [INFO] Gripper_485.py:386 - 移动到位置 0xE1
2025-07-23 21:06:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xE1, 力矩=0xFF
2025-07-23 21:06:01 [INFO] Gripper_485.py:386 - 移动到位置 0xDC
2025-07-23 21:06:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xDC, 力矩=0xFF
2025-07-23 21:06:01 [INFO] Gripper_485.py:386 - 移动到位置 0xD2
2025-07-23 21:06:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xD2, 力矩=0xFF
2025-07-23 21:06:01 [INFO] Gripper_485.py:386 - 移动到位置 0xCE
2025-07-23 21:06:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xCE, 力矩=0xFF
2025-07-23 21:06:01 [INFO] Gripper_485.py:386 - 移动到位置 0xC9
2025-07-23 21:06:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xC9, 力矩=0xFF
2025-07-23 21:06:01 [INFO] Gripper_485.py:386 - 移动到位置 0xC4
2025-07-23 21:06:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xC4, 力矩=0xFF
2025-07-23 21:06:01 [INFO] Gripper_485.py:386 - 移动到位置 0xC2
2025-07-23 21:06:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xC2, 力矩=0xFF
2025-07-23 21:06:01 [INFO] Gripper_485.py:386 - 移动到位置 0xC1
2025-07-23 21:06:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xC1, 力矩=0xFF
2025-07-23 21:06:01 [INFO] Gripper_485.py:386 - 移动到位置 0xC2
2025-07-23 21:06:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xC2, 力矩=0xFF
2025-07-23 21:06:01 [INFO] Gripper_485.py:386 - 移动到位置 0xC1
2025-07-23 21:06:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xC1, 力矩=0xFF
2025-07-23 21:06:01 [INFO] Gripper_485.py:386 - 移动到位置 0xD7
2025-07-23 21:06:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xD7, 力矩=0xFF
2025-07-23 21:06:01 [INFO] Gripper_485.py:386 - 移动到位置 0xEE
2025-07-23 21:06:01 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xEE, 力矩=0xFF
