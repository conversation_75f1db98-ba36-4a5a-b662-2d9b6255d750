2025-07-22 21:46:42 [INFO] logger_config.py:102 - 日志系统初始化完成，日志文件: /home/<USER>/Spatial_AI/logs/gripper_485/gripper_485_1_20250722_214642.log
2025-07-22 21:46:42 [INFO] Gripper_485.py:53 - 初始化RS485夹爪 - 端口: /dev/ttyUSB1, 节点ID: 1, 波特率: 115200
2025-07-22 21:46:42 [DEBUG] Gripper_485.py:92 - 启动RS485消息接收线程
2025-07-22 21:46:42 [INFO] Gripper_485.py:96 - RS485消息接收线程已启动
2025-07-22 21:46:42 [INFO] Gripper_485.py:53 - 初始化RS485夹爪 - 端口: /dev/ttyUSB2, 节点ID: 1, 波特率: 115200
2025-07-22 21:46:42 [DEBUG] Gripper_485.py:92 - 启动RS485消息接收线程
2025-07-22 21:46:42 [INFO] Gripper_485.py:96 - RS485消息接收线程已启动
2025-07-22 21:46:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:46:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:46:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFD
2025-07-22 21:46:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFD
2025-07-22 21:46:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFC
2025-07-22 21:46:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFC
2025-07-22 21:46:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xFA
2025-07-22 21:46:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFA
2025-07-22 21:46:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xF7
2025-07-22 21:46:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xF7
2025-07-22 21:46:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xF4
2025-07-22 21:46:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xF4
2025-07-22 21:46:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xF0
2025-07-22 21:46:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xF0
2025-07-22 21:46:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xEC
2025-07-22 21:46:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xEC
2025-07-22 21:46:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xE7
2025-07-22 21:46:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xE7
2025-07-22 21:46:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xE1
2025-07-22 21:46:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xE1
2025-07-22 21:46:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xDB
2025-07-22 21:46:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xDB
2025-07-22 21:46:42 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xD5
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xD5
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xCE
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xCE
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xC7
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xC7
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xC0
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xC0
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xB9
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xB9
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xB2
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xB2
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xAB
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xAB
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xA4
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xA4
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x9E
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x9E
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x98
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x99
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x93
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x93
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x8F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x8F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x8B
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x8B
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x88
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x88
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x85
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x85
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x83
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x83
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x81
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x81
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:43 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 5, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 11, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-22 21:46:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 4, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 12, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-22 21:46:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 128, 'velocity': 2, 'force': 108, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 128, 'velocity': 3, 'force': 130, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 128, 'velocity': 2, 'force': 108, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 128, 'velocity': 3, 'force': 130, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 128, 'velocity': 2, 'force': 108, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 128, 'velocity': 3, 'force': 130, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 128, 'velocity': 2, 'force': 108, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 128, 'velocity': 3, 'force': 130, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 128, 'velocity': 2, 'force': 108, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 128, 'velocity': 3, 'force': 130, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 128, 'velocity': 2, 'force': 108, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:44 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:44 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:44 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:44 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 128, 'velocity': 3, 'force': 130, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 128, 'velocity': 2, 'force': 108, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 128, 'velocity': 3, 'force': 130, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 128, 'velocity': 2, 'force': 108, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 128, 'velocity': 3, 'force': 130, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 128, 'velocity': 2, 'force': 108, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 128, 'velocity': 3, 'force': 130, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 128, 'velocity': 2, 'force': 108, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 128, 'velocity': 3, 'force': 130, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xF9
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xFB
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xFC
2025-07-22 21:46:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 252, 'velocity': 63, 'force': 52, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 21:46:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 128, 'velocity': 3, 'force': 130, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xFD
2025-07-22 21:46:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 253, 'velocity': 48, 'force': 53, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 21:46:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 128, 'velocity': 3, 'force': 130, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xFE
2025-07-22 21:46:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 254, 'velocity': 34, 'force': 57, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 21:46:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 128, 'velocity': 3, 'force': 130, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 254, 'velocity': 34, 'force': 57, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 21:46:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 128, 'velocity': 3, 'force': 130, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 254, 'velocity': 34, 'force': 57, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 21:46:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 128, 'velocity': 3, 'force': 130, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 254, 'velocity': 34, 'force': 57, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 21:46:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 128, 'velocity': 3, 'force': 130, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 254, 'velocity': 34, 'force': 57, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 21:46:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 128, 'velocity': 3, 'force': 130, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 254, 'velocity': 34, 'force': 57, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 21:46:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 128, 'velocity': 3, 'force': 130, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xFF
2025-07-22 21:46:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 21:46:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 128, 'velocity': 3, 'force': 130, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xFE
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xFF
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xFF
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xFE
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xFF
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xFF
2025-07-22 21:46:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 21:46:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 128, 'velocity': 3, 'force': 130, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:46:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 128, 'velocity': 3, 'force': 130, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 128, 'velocity': 3, 'force': 130, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 128, 'velocity': 3, 'force': 130, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:46:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 128, 'velocity': 3, 'force': 130, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 128, 'velocity': 3, 'force': 130, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 128, 'velocity': 3, 'force': 130, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 22, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 22, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 22, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 22, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 22, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 22, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 22, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 22, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:45 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:45 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:45 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:45 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 22, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 22, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 22, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 22, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 22, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 22, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 22, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 22, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 30, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 30, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:46 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 30, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 30, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 30, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 30, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 30, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 30, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 30, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 30, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 30, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 30, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 30, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 30, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 30, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 30, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 30, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 30, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:46 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:46 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:46 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:46 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 30, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:47 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:47 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:47 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:47 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 30, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:47 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:47 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:47 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:47 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 30, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:47 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:47 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:47 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:47 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 30, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:47 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:47 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:47 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:47 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 30, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:47 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:47 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:47 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:47 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 30, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:47 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:47 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:47 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:47 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 35, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:47 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:47 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:47 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:47 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 37, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:47 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:47 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:47 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:47 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 38, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:47 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:47 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:47 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:47 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 35, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:47 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:47 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:47 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:47 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:47 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:47 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:47 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:47 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:47 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:47 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:47 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:47 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:47 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:47 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:47 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:47 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:47 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:47 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:47 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:47 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:47 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:47 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:47 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:47 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:47 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:47 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:47 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:47 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:47 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:47 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:47 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:47 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:47 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:47 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:47 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:47 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:47 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:47 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:47 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:47 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:47 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:47 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:47 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:47 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:47 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:47 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:47 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:47 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:47 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:47 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:47 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:47 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:47 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:47 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:47 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:47 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:47 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:47 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:47 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:47 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:47 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:47 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:47 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:47 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:46:47 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:47 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:47 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:47 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:47 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:47 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:47 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:47 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:47 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:47 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:47 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:47 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:47 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:47 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:48 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:48 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:48 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:48 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:48 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:48 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:48 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:48 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:48 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:48 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:48 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:48 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:48 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:48 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:48 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:48 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:48 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:48 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:48 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:48 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:48 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:48 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:48 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:48 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:48 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:48 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:48 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:48 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:48 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:48 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:48 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:48 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:48 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:48 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:48 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:48 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:48 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:48 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:48 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:48 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:48 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:48 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:48 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:48 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:48 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:48 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:48 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:48 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:48 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:48 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:48 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:48 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:48 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:48 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:48 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:48 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:48 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:48 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:48 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:48 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:48 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:48 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:48 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:48 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:48 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:48 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:48 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:48 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:48 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:48 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:48 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:48 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:48 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:48 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:48 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:48 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:48 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:48 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:48 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:48 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:48 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:48 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:48 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:48 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:48 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:48 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:48 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:48 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:48 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:48 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:48 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:48 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:48 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:48 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:48 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:48 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:48 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:48 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:48 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:48 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:48 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:48 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:48 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:48 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:48 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:48 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:48 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:48 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:48 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:48 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:48 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:48 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:48 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:48 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:48 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:49 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:49 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:49 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:49 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:49 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:49 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:49 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:49 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:49 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:49 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:49 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:49 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:49 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:49 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:49 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:49 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:49 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:49 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:49 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:49 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:49 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:49 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:49 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:49 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:49 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:49 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:49 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:49 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 37, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:49 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:49 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:49 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:49 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 37, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:49 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:49 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:49 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:49 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 37, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:49 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:49 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:49 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:49 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 37, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:49 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:49 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:49 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:49 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 37, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:49 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:49 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:49 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:49 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 37, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:49 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:49 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:49 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:49 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 37, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:49 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:49 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:49 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:49 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 37, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:46:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:46:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:49 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:49 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 5, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:49 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:49 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 39, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:49 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:49 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:49 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:49 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 38, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:46:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:49 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:49 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:49 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:49 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:49 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 38, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:49 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:49 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:49 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:49 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 38, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:49 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:49 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:49 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:49 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 38, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:49 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:49 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:49 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:49 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 38, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:49 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:49 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:49 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:49 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 38, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:49 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:49 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:49 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:49 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 38, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:49 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:49 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:49 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:49 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 38, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:49 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:49 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:49 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:49 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 38, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:49 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:49 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:49 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:49 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 38, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:49 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:49 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:49 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:49 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 38, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:49 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:49 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:49 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:49 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:49 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:49 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 38, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:50 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:50 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:50 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:50 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 38, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:50 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:50 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:50 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:50 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 38, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:50 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:50 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:50 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:50 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 38, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:50 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:50 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:50 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:50 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 38, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:50 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:46:50 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:50 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:50 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:50 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:50 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:50 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:50 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:50 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:50 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:50 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:50 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:50 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:50 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:50 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:50 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:50 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:50 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:50 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:50 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:50 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:50 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:50 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:50 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:50 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:50 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:50 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:50 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:50 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:50 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:50 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:50 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:50 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:50 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:50 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:50 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:50 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:50 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:50 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:50 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:50 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:50 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:50 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:50 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:50 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:50 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:50 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:50 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:50 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:50 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:50 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:50 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:50 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:50 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:50 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:50 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:50 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:50 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:50 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:50 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:50 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:50 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:50 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:50 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:50 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:50 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:50 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:50 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:50 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:51 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:51 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:51 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:51 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:51 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:51 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:51 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:51 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:51 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:51 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:51 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:51 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:51 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:51 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:51 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:51 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:51 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:51 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:51 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:51 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:51 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:51 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:51 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:51 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:51 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:51 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:51 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:51 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:51 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:51 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:51 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:51 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:51 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:51 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:51 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:51 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:51 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:51 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:51 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:51 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:46:51 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:51 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:51 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:51 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:46:51 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:51 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:51 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:51 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:46:51 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:51 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 11, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:51 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:51 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:51 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:51 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 11, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:51 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:51 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:51 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:51 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 11, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:51 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:51 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:51 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:51 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 11, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:51 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:51 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:51 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:51 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 11, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:51 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:51 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:51 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:51 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 11, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:51 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:51 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:51 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:51 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 11, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:51 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:51 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:51 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:51 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 11, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:51 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:51 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:51 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:51 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 11, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:51 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:51 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:51 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:51 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 11, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:51 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:51 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:51 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:51 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 11, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:51 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:51 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:51 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:51 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 11, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:51 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:51 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:51 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:51 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 11, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:51 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:51 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:51 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:51 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 11, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:51 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:51 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:51 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:51 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:51 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:51 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:51 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:51 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:51 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:51 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:51 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:51 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:51 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:51 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:51 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:51 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:51 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:51 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:52 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:52 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:52 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:52 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:52 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:52 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:52 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:52 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:52 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:52 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:52 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:52 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:52 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:52 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:52 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:52 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:52 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:52 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:52 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:52 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:52 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:52 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:52 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:52 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:46:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:46:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:46:52 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:52 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:52 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:52 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:46:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:52 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:52 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:52 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:52 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:52 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:52 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:52 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:52 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:52 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:52 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:52 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:52 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:52 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:52 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:52 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:52 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:46:52 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:52 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:52 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:52 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:46:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:46:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:46:52 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:52 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:52 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:52 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:52 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:52 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:52 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:52 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:46:52 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:52 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 8, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:52 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:52 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:46:52 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:52 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:52 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:52 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:46:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:46:52 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:52 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:52 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:52 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:52 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:52 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:52 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:52 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:52 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:52 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:52 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:52 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:52 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:52 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:52 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:52 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:52 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:52 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:52 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:52 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:52 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:52 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:52 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:52 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:52 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:52 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:52 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:52 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:52 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:52 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:52 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:52 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:52 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:52 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:52 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:52 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:52 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:52 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:52 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:52 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:52 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:52 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:52 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:52 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:52 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:52 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:52 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:52 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:52 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:53 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:53 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:53 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:53 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:53 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:53 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:53 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:53 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:53 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:53 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:53 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:53 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:53 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:53 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:53 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:53 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:53 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:53 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:53 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:53 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:53 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:53 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:53 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:53 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:46:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:46:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:46:53 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:53 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:53 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:53 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 35, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:53 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:53 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:53 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:53 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 35, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:53 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:53 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:53 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:53 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 35, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:53 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:53 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:53 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:53 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 35, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:46:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:46:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:46:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:46:53 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:53 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:53 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:53 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:53 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:53 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:53 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:53 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:46:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:53 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:53 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:53 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:53 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:53 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:53 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:53 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:53 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:53 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:53 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:53 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:53 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:53 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:53 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:53 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:53 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:53 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:53 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:53 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:53 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:53 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:53 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:53 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:53 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:53 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:53 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:53 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:53 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:53 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:53 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:53 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:53 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:53 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:53 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:53 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:53 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:53 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:54 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:54 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:54 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:54 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:54 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:54 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:54 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:54 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:54 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:54 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:54 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:54 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:54 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:54 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:54 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:54 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:54 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:54 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:54 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:54 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:54 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:54 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:54 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:54 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:54 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:54 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:54 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:54 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:54 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:54 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:54 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:54 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:54 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:54 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:54 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:54 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:46:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:46:54 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:54 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:54 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:54 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:54 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:54 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:54 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:54 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:54 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:54 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:54 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:54 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:46:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:46:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:46:54 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:54 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:54 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:54 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:54 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:54 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:54 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:54 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:54 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:54 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:54 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:54 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:54 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:54 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:54 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:54 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:54 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:54 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:54 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:54 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:54 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:54 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:54 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:54 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:54 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:54 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:54 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:54 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:54 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:54 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:54 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:54 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:54 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:54 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:54 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:54 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:54 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:54 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:54 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:54 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:46:54 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:54 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:54 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:54 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 35, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:46:54 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:46:55 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:55 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:55 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:55 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 35, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:46:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:55 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:55 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:55 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:55 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:46:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:55 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:55 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:55 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:55 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:46:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:46:55 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:55 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:55 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:55 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:55 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:55 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:55 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:55 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:55 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:55 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:55 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:55 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:55 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:55 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 6, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:55 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:55 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:46:55 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:55 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:55 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:55 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:55 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:55 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 255, 'velocity': 0, 'force': 5, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:55 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:55 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-22 21:46:55 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-22 21:46:55 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:55 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:55 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:55 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:55 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:55 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:55 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:55 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:55 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:55 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:55 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:55 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:55 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:55 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:55 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:55 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:55 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:55 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:55 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:55 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:55 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:55 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:55 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:55 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:55 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:55 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:55 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:55 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:55 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:55 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:55 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:55 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:55 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:55 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:55 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:55 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:55 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:55 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:55 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:55 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:55 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:55 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:55 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:55 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:55 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:55 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:55 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:55 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:55 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:55 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:55 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:55 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:55 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:55 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:55 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:55 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:55 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:55 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 9, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 21:46:55 [INFO] Gripper_485.py:386 - 移动到位置 0xFF
2025-07-22 21:46:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-22 21:46:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 21:46:55 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 254, 'velocity': 0, 'force': 36, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
