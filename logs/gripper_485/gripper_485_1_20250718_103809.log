2025-07-18 10:38:09 [INFO] logger_config.py:102 - 日志系统初始化完成，日志文件: /home/<USER>/Spatial_AI/logs/gripper_485/gripper_485_1_20250718_103809.log
2025-07-18 10:38:09 [INFO] Gripper_485.py:53 - 初始化RS485夹爪 - 端口: /dev/ttyUSB1, 节点ID: 1, 波特率: 115200
2025-07-18 10:38:09 [DEBUG] Gripper_485.py:92 - 启动RS485消息接收线程
2025-07-18 10:38:09 [INFO] Gripper_485.py:96 - RS485消息接收线程已启动
2025-07-18 10:38:09 [INFO] Gripper_485.py:53 - 初始化RS485夹爪 - 端口: /dev/ttyUSB2, 节点ID: 1, 波特率: 115200
2025-07-18 10:38:09 [DEBUG] Gripper_485.py:92 - 启动RS485消息接收线程
2025-07-18 10:38:09 [INFO] Gripper_485.py:96 - RS485消息接收线程已启动
2025-07-18 10:38:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-18 10:38:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-18 10:38:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFD
2025-07-18 10:38:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFD
2025-07-18 10:38:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFC
2025-07-18 10:38:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFC
2025-07-18 10:38:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xF9
2025-07-18 10:38:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xF9
2025-07-18 10:38:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xF7
2025-07-18 10:38:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xF6
2025-07-18 10:38:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xF3
2025-07-18 10:38:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xF3
2025-07-18 10:38:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xEF
2025-07-18 10:38:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xEF
2025-07-18 10:38:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xEA
2025-07-18 10:38:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xEA
2025-07-18 10:38:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xE4
2025-07-18 10:38:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xE4
2025-07-18 10:38:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xDE
2025-07-18 10:38:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xDE
2025-07-18 10:38:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xD8
2025-07-18 10:38:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xD7
2025-07-18 10:38:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xD1
2025-07-18 10:38:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xD0
2025-07-18 10:38:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xCA
2025-07-18 10:38:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xC9
2025-07-18 10:38:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xC4
2025-07-18 10:38:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xC2
2025-07-18 10:38:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xBD
2025-07-18 10:38:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xBB
2025-07-18 10:38:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xB6
2025-07-18 10:38:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xB4
2025-07-18 10:38:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xB0
2025-07-18 10:38:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xAD
2025-07-18 10:38:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xA9
2025-07-18 10:38:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xA5
2025-07-18 10:38:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xA2
2025-07-18 10:38:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x9E
2025-07-18 10:38:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x9C
2025-07-18 10:38:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x98
2025-07-18 10:38:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x95
2025-07-18 10:38:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x92
2025-07-18 10:38:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x90
2025-07-18 10:38:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x8E
2025-07-18 10:38:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x8D
2025-07-18 10:38:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x8A
2025-07-18 10:38:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x89
2025-07-18 10:38:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x87
2025-07-18 10:38:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x86
2025-07-18 10:38:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x84
2025-07-18 10:38:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x83
2025-07-18 10:38:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x82
2025-07-18 10:38:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x81
2025-07-18 10:38:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x81
2025-07-18 10:38:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:38:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:38:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:38:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:38:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:38:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:38:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:38:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:38:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:38:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:38:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:38:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:38:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:38:09 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:10 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:11 [INFO] Gripper_485.py:372 - 执行关闭夹爪指令
2025-07-18 10:38:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7E
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7E
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7C
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7B
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x79
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x77
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x75
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x72
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x6F
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x6C
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x69
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x65
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x61
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x5D
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x59
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x54
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x50
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x4B
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x47
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x42
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x3E
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x39
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x35
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x30
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x2C
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x27
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x22
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x1E
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x19
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x15
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x11
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x0F
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x0C
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x09
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x07
2025-07-18 10:38:11 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x00, 实际=0x07, 误差=7
2025-07-18 10:38:11 [INFO] Gripper_485.py:372 - 执行关闭夹爪指令
2025-07-18 10:38:11 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x05
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x04
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x02
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x01
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x01
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7E
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7D
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7B
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7A
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x77
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x75
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x73
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x70
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x6C
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x69
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x65
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x61
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x5D
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x59
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x54
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x50
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x4B
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x46
2025-07-18 10:38:11 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x42
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x3D
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x38
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x33
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x2E
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x2A
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x25
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x20
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x1B
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x17
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x13
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x10
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x0D
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x0B
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x08
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x00, 实际=0x08, 误差=8
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x06
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x04
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x03
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x02
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x01
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:12 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [INFO] Gripper_485.py:358 - 执行打开夹爪指令
2025-07-18 10:38:13 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x01
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x01
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x02
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x03
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x04
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x06
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x08
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x0A
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x0D
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x10
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x13
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x16
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x1A
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x1E
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x22
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x26
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x2B
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x2F
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x34
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x39
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x3D
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x42
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x46
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x4B
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x50
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x54
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x59
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x5D
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x62
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x67
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x6C
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x70
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x75
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7A
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7E
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x83
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x87
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x8C
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x91
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x95
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x9A
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x9F
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xA4
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xA8
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xAD
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xB2
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xB7
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xBC
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xC0
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xC5
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xCA
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xCE
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xD3
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xD8
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xDC
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xE1
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xE5
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xEA
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xEF
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xF2
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xF5
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xF8
2025-07-18 10:38:13 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0xFF, 实际=0xF8, 误差=7
2025-07-18 10:38:13 [INFO] Gripper_485.py:358 - 执行打开夹爪指令
2025-07-18 10:38:13 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xF9
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xFA
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xFC
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xFD
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xFE
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xFE
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xFE
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xFE
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xFE
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xFE
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xFE
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xFE
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xFE
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xFE
2025-07-18 10:38:13 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:14 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:15 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:16 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:17 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:38:18 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-18 10:38:18 [WARNING] Gripper_485.py:255 - 等待回复超时，节点ID: 1
2025-07-18 10:38:18 [WARNING] Gripper_485.py:257 - 最后状态: 目标=0xFF, 实际=0x00
