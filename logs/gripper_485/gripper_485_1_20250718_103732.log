2025-07-18 10:37:32 [INFO] logger_config.py:102 - 日志系统初始化完成，日志文件: /home/<USER>/Spatial_AI/logs/gripper_485/gripper_485_1_20250718_103732.log
2025-07-18 10:37:32 [INFO] Gripper_485.py:53 - 初始化RS485夹爪 - 端口: /dev/ttyUSB1, 节点ID: 1, 波特率: 115200
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:92 - 启动RS485消息接收线程
2025-07-18 10:37:32 [INFO] Gripper_485.py:96 - RS485消息接收线程已启动
2025-07-18 10:37:32 [INFO] Gripper_485.py:53 - 初始化RS485夹爪 - 端口: /dev/ttyUSB2, 节点ID: 1, 波特率: 115200
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:92 - 启动RS485消息接收线程
2025-07-18 10:37:32 [INFO] Gripper_485.py:96 - RS485消息接收线程已启动
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFF
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0xFE
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xFD
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xFD
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xFC
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xFC
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xF9
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xF9
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xF6
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xF7
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xF3
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xF3
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xEF
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xEE
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xEA
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xEA
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xE5
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xE4
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xDF
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xDE
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xD7
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xD8
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xD0
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xD1
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xC9
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xCA
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xC2
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xC3
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xBB
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xBC
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xB4
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xB5
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xAD
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xAD
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xA6
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xA6
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x9F
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x9F
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x98
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x98
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x92
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x92
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x8E
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x8E
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x8A
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x8A
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x87
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x87
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x84
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x84
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x82
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x82
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x81
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x81
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x80
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:32 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:33 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:34 [INFO] Gripper_485.py:372 - 执行关闭夹爪指令
2025-07-18 10:37:34 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7E
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7D
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7B
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x79
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x77
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x75
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x73
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x70
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x6D
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x69
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x66
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x62
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x5E
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x59
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x54
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x4F
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x4B
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x46
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x41
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x3C
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x38
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x33
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x2E
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x2A
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x25
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x20
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x1B
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x17
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x13
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x10
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x0D
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x0A
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x08
2025-07-18 10:37:34 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x00, 实际=0x08, 误差=8
2025-07-18 10:37:34 [INFO] Gripper_485.py:372 - 执行关闭夹爪指令
2025-07-18 10:37:34 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x06
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x04
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x80
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x03
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x7F
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x02
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7F
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x01
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7E
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7D
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7B
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7A
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x78
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x75
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x73
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x70
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x6D
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x6A
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x66
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x62
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x5E
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x5A
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x55
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x50
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x4C
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x47
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x43
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x3E
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x3A
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x35
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x31
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x2C
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x28
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x23
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x1E
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x1A
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x15
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x12
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x0F
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x0D
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x0A
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x08
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:34 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0x00, 实际=0x08, 误差=8
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x06
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x04
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x03
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x01
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x01
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:34 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [INFO] Gripper_485.py:358 - 执行打开夹爪指令
2025-07-18 10:37:35 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:35 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:36 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:37 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:38 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:39 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:244 - 移动中: 目标=0xFF, 当前=0x00, 误差=255
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [WARNING] Gripper_485.py:255 - 等待回复超时，节点ID: 1
2025-07-18 10:37:40 [WARNING] Gripper_485.py:257 - 最后状态: 目标=0xFF, 实际=0x00
2025-07-18 10:37:40 [INFO] Gripper_485.py:358 - 执行打开夹爪指令
2025-07-18 10:37:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFF, 力矩=0xFF
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x00
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x01
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x02
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x03
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x06
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x08
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=0, 位置=0x0A
2025-07-18 10:37:40 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x0D
2025-07-18 10:37:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x10
2025-07-18 10:37:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x13
2025-07-18 10:37:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x16
2025-07-18 10:37:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x1A
2025-07-18 10:37:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x1E
2025-07-18 10:37:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x22
2025-07-18 10:37:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x27
2025-07-18 10:37:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x2C
2025-07-18 10:37:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x30
2025-07-18 10:37:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x35
2025-07-18 10:37:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x3A
2025-07-18 10:37:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x3F
2025-07-18 10:37:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x44
2025-07-18 10:37:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x48
2025-07-18 10:37:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x4D
2025-07-18 10:37:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x52
2025-07-18 10:37:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x57
2025-07-18 10:37:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x5B
2025-07-18 10:37:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x60
2025-07-18 10:37:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x65
2025-07-18 10:37:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x6A
2025-07-18 10:37:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x6E
2025-07-18 10:37:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x73
2025-07-18 10:37:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x77
2025-07-18 10:37:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x7C
2025-07-18 10:37:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x81
2025-07-18 10:37:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x85
2025-07-18 10:37:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x8A
2025-07-18 10:37:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x8F
2025-07-18 10:37:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x94
2025-07-18 10:37:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x99
2025-07-18 10:37:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0x9D
2025-07-18 10:37:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xA2
2025-07-18 10:37:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xA7
2025-07-18 10:37:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xAC
2025-07-18 10:37:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xB1
2025-07-18 10:37:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xB6
2025-07-18 10:37:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xBA
2025-07-18 10:37:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xBF
2025-07-18 10:37:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xC4
2025-07-18 10:37:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xC8
2025-07-18 10:37:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xCD
2025-07-18 10:37:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xD2
2025-07-18 10:37:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xD6
2025-07-18 10:37:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xDB
2025-07-18 10:37:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xE0
2025-07-18 10:37:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xE4
2025-07-18 10:37:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xE9
2025-07-18 10:37:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xED
2025-07-18 10:37:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xF1
2025-07-18 10:37:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xF3
2025-07-18 10:37:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xF5
2025-07-18 10:37:41 [DEBUG] Gripper_485.py:114 - 状态更新: 故障码=0, 状态=1, 位置=0xF8
2025-07-18 10:37:41 [INFO] Gripper_485.py:238 - 夹爪已到达目标位置: 目标=0xFF, 实际=0xF8, 误差=7
