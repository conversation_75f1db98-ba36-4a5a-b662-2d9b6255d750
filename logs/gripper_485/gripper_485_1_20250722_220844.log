2025-07-22 22:08:44 [INFO] logger_config.py:102 - 日志系统初始化完成，日志文件: /home/<USER>/Spatial_AI/logs/gripper_485/gripper_485_1_20250722_220844.log
2025-07-22 22:08:44 [INFO] Gripper_485.py:53 - 初始化RS485夹爪 - 端口: /dev/ttyUSB1, 节点ID: 1, 波特率: 115200
2025-07-22 22:08:44 [DEBUG] Gripper_485.py:92 - 启动RS485消息接收线程
2025-07-22 22:08:44 [INFO] Gripper_485.py:96 - RS485消息接收线程已启动
2025-07-22 22:08:44 [INFO] Gripper_485.py:53 - 初始化RS485夹爪 - 端口: /dev/ttyUSB2, 节点ID: 1, 波特率: 115200
2025-07-22 22:08:44 [DEBUG] Gripper_485.py:92 - 启动RS485消息接收线程
2025-07-22 22:08:44 [INFO] Gripper_485.py:96 - RS485消息接收线程已启动
2025-07-22 22:08:50 [INFO] Gripper_485.py:386 - 移动到位置 0x05
2025-07-22 22:08:50 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x05, 力矩=0xFF
2025-07-22 22:08:50 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:50 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 127, 'velocity': 0, 'force': 7, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
2025-07-22 22:08:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:08:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:08:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:51 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 94, 'velocity': 227, 'force': 103, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:08:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:08:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:51 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 94, 'velocity': 227, 'force': 103, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:08:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:08:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:51 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 94, 'velocity': 227, 'force': 103, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:08:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:08:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:51 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 94, 'velocity': 227, 'force': 103, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:51 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:08:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:08:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:51 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 94, 'velocity': 227, 'force': 103, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:51 [INFO] Gripper_485.py:386 - 移动到位置 0x88
2025-07-22 22:08:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x88, 力矩=0xFF
2025-07-22 22:08:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:51 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 94, 'velocity': 227, 'force': 103, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:51 [INFO] Gripper_485.py:386 - 移动到位置 0xC6
2025-07-22 22:08:51 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xC6, 力矩=0xFF
2025-07-22 22:08:51 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:51 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 94, 'velocity': 227, 'force': 103, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:52 [INFO] Gripper_485.py:386 - 移动到位置 0xFC
2025-07-22 22:08:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFC, 力矩=0xFF
2025-07-22 22:08:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:52 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 94, 'velocity': 227, 'force': 103, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:52 [INFO] Gripper_485.py:386 - 移动到位置 0xED
2025-07-22 22:08:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xED, 力矩=0xFF
2025-07-22 22:08:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:52 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 94, 'velocity': 227, 'force': 103, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:52 [INFO] Gripper_485.py:386 - 移动到位置 0xD4
2025-07-22 22:08:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xD4, 力矩=0xFF
2025-07-22 22:08:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:52 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 94, 'velocity': 227, 'force': 103, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:52 [INFO] Gripper_485.py:386 - 移动到位置 0xCE
2025-07-22 22:08:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xCE, 力矩=0xFF
2025-07-22 22:08:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:52 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 94, 'velocity': 227, 'force': 103, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:52 [INFO] Gripper_485.py:386 - 移动到位置 0xD1
2025-07-22 22:08:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xD1, 力矩=0xFF
2025-07-22 22:08:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:52 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 94, 'velocity': 227, 'force': 103, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:52 [INFO] Gripper_485.py:386 - 移动到位置 0xD5
2025-07-22 22:08:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xD5, 力矩=0xFF
2025-07-22 22:08:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:52 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 94, 'velocity': 227, 'force': 103, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:52 [INFO] Gripper_485.py:386 - 移动到位置 0xE2
2025-07-22 22:08:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xE2, 力矩=0xFF
2025-07-22 22:08:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:52 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 94, 'velocity': 227, 'force': 103, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:52 [INFO] Gripper_485.py:386 - 移动到位置 0xF3
2025-07-22 22:08:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF3, 力矩=0xFF
2025-07-22 22:08:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:52 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 94, 'velocity': 227, 'force': 103, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:52 [INFO] Gripper_485.py:386 - 移动到位置 0xF8
2025-07-22 22:08:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF8, 力矩=0xFF
2025-07-22 22:08:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:52 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 94, 'velocity': 227, 'force': 103, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:52 [INFO] Gripper_485.py:386 - 移动到位置 0xF9
2025-07-22 22:08:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF9, 力矩=0xFF
2025-07-22 22:08:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:52 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 94, 'velocity': 227, 'force': 103, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:52 [INFO] Gripper_485.py:386 - 移动到位置 0xF9
2025-07-22 22:08:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF9, 力矩=0xFF
2025-07-22 22:08:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:52 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 94, 'velocity': 227, 'force': 103, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:52 [INFO] Gripper_485.py:386 - 移动到位置 0xFB
2025-07-22 22:08:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFB, 力矩=0xFF
2025-07-22 22:08:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:52 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 94, 'velocity': 227, 'force': 103, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:52 [INFO] Gripper_485.py:386 - 移动到位置 0xFA
2025-07-22 22:08:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFA, 力矩=0xFF
2025-07-22 22:08:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:52 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 94, 'velocity': 227, 'force': 103, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:52 [INFO] Gripper_485.py:386 - 移动到位置 0xFB
2025-07-22 22:08:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFB, 力矩=0xFF
2025-07-22 22:08:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:52 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 94, 'velocity': 227, 'force': 103, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:52 [INFO] Gripper_485.py:386 - 移动到位置 0xFB
2025-07-22 22:08:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFB, 力矩=0xFF
2025-07-22 22:08:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:52 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 94, 'velocity': 227, 'force': 103, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:52 [INFO] Gripper_485.py:386 - 移动到位置 0xFC
2025-07-22 22:08:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFC, 力矩=0xFF
2025-07-22 22:08:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:52 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 94, 'velocity': 227, 'force': 103, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:52 [INFO] Gripper_485.py:386 - 移动到位置 0xFB
2025-07-22 22:08:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFB, 力矩=0xFF
2025-07-22 22:08:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:52 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 94, 'velocity': 227, 'force': 103, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:52 [INFO] Gripper_485.py:386 - 移动到位置 0xFC
2025-07-22 22:08:52 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFC, 力矩=0xFF
2025-07-22 22:08:52 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:52 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 94, 'velocity': 227, 'force': 103, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:53 [INFO] Gripper_485.py:386 - 移动到位置 0xFE
2025-07-22 22:08:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFE, 力矩=0xFF
2025-07-22 22:08:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:53 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 251, 'velocity': 0, 'force': 5, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:53 [INFO] Gripper_485.py:386 - 移动到位置 0xFE
2025-07-22 22:08:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFE, 力矩=0xFF
2025-07-22 22:08:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:53 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 251, 'velocity': 0, 'force': 5, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:53 [INFO] Gripper_485.py:386 - 移动到位置 0xFB
2025-07-22 22:08:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFB, 力矩=0xFF
2025-07-22 22:08:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:53 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 251, 'velocity': 0, 'force': 5, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:53 [INFO] Gripper_485.py:386 - 移动到位置 0xFA
2025-07-22 22:08:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFA, 力矩=0xFF
2025-07-22 22:08:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:53 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 251, 'velocity': 0, 'force': 5, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:53 [INFO] Gripper_485.py:386 - 移动到位置 0xFB
2025-07-22 22:08:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFB, 力矩=0xFF
2025-07-22 22:08:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:53 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 251, 'velocity': 0, 'force': 5, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:53 [INFO] Gripper_485.py:386 - 移动到位置 0xFC
2025-07-22 22:08:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFC, 力矩=0xFF
2025-07-22 22:08:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:53 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 251, 'velocity': 0, 'force': 5, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:53 [INFO] Gripper_485.py:386 - 移动到位置 0xFB
2025-07-22 22:08:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFB, 力矩=0xFF
2025-07-22 22:08:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:53 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 251, 'velocity': 0, 'force': 5, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:53 [INFO] Gripper_485.py:386 - 移动到位置 0xFA
2025-07-22 22:08:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFA, 力矩=0xFF
2025-07-22 22:08:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:53 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 251, 'velocity': 0, 'force': 5, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:53 [INFO] Gripper_485.py:386 - 移动到位置 0xFA
2025-07-22 22:08:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFA, 力矩=0xFF
2025-07-22 22:08:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:53 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 251, 'velocity': 0, 'force': 5, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:53 [INFO] Gripper_485.py:386 - 移动到位置 0xFA
2025-07-22 22:08:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFA, 力矩=0xFF
2025-07-22 22:08:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:53 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 251, 'velocity': 0, 'force': 5, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:53 [INFO] Gripper_485.py:386 - 移动到位置 0xF9
2025-07-22 22:08:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF9, 力矩=0xFF
2025-07-22 22:08:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:53 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 251, 'velocity': 0, 'force': 5, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:53 [INFO] Gripper_485.py:386 - 移动到位置 0xFA
2025-07-22 22:08:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFA, 力矩=0xFF
2025-07-22 22:08:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:53 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 251, 'velocity': 0, 'force': 5, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:53 [INFO] Gripper_485.py:386 - 移动到位置 0xFA
2025-07-22 22:08:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFA, 力矩=0xFF
2025-07-22 22:08:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:53 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 251, 'velocity': 0, 'force': 5, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:53 [INFO] Gripper_485.py:386 - 移动到位置 0xFA
2025-07-22 22:08:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFA, 力矩=0xFF
2025-07-22 22:08:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:53 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 251, 'velocity': 0, 'force': 5, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:53 [INFO] Gripper_485.py:386 - 移动到位置 0xFA
2025-07-22 22:08:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFA, 力矩=0xFF
2025-07-22 22:08:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:53 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 250, 'velocity': 0, 'force': 2, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:53 [INFO] Gripper_485.py:386 - 移动到位置 0xFA
2025-07-22 22:08:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFA, 力矩=0xFF
2025-07-22 22:08:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:53 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:53 [INFO] Gripper_485.py:386 - 移动到位置 0xFB
2025-07-22 22:08:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFB, 力矩=0xFF
2025-07-22 22:08:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:53 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:53 [INFO] Gripper_485.py:386 - 移动到位置 0xFB
2025-07-22 22:08:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFB, 力矩=0xFF
2025-07-22 22:08:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:53 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:53 [INFO] Gripper_485.py:386 - 移动到位置 0xFC
2025-07-22 22:08:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFC, 力矩=0xFF
2025-07-22 22:08:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:53 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:53 [INFO] Gripper_485.py:386 - 移动到位置 0xFC
2025-07-22 22:08:53 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFC, 力矩=0xFF
2025-07-22 22:08:53 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:53 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:54 [INFO] Gripper_485.py:386 - 移动到位置 0xFC
2025-07-22 22:08:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFC, 力矩=0xFF
2025-07-22 22:08:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:54 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:54 [INFO] Gripper_485.py:386 - 移动到位置 0xFD
2025-07-22 22:08:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFD, 力矩=0xFF
2025-07-22 22:08:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:54 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:54 [INFO] Gripper_485.py:386 - 移动到位置 0xFD
2025-07-22 22:08:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFD, 力矩=0xFF
2025-07-22 22:08:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:54 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:54 [INFO] Gripper_485.py:386 - 移动到位置 0xFD
2025-07-22 22:08:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFD, 力矩=0xFF
2025-07-22 22:08:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:54 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:54 [INFO] Gripper_485.py:386 - 移动到位置 0xFE
2025-07-22 22:08:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFE, 力矩=0xFF
2025-07-22 22:08:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:54 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:54 [INFO] Gripper_485.py:386 - 移动到位置 0xFE
2025-07-22 22:08:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFE, 力矩=0xFF
2025-07-22 22:08:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:54 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:54 [INFO] Gripper_485.py:386 - 移动到位置 0xFE
2025-07-22 22:08:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFE, 力矩=0xFF
2025-07-22 22:08:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:54 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:54 [INFO] Gripper_485.py:386 - 移动到位置 0xFC
2025-07-22 22:08:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFC, 力矩=0xFF
2025-07-22 22:08:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:54 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:54 [INFO] Gripper_485.py:386 - 移动到位置 0xF5
2025-07-22 22:08:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF5, 力矩=0xFF
2025-07-22 22:08:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:54 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:54 [INFO] Gripper_485.py:386 - 移动到位置 0xF1
2025-07-22 22:08:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF1, 力矩=0xFF
2025-07-22 22:08:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:54 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:54 [INFO] Gripper_485.py:386 - 移动到位置 0xF2
2025-07-22 22:08:54 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF2, 力矩=0xFF
2025-07-22 22:08:54 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:54 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:55 [INFO] Gripper_485.py:386 - 移动到位置 0xF2
2025-07-22 22:08:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF2, 力矩=0xFF
2025-07-22 22:08:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:55 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:55 [INFO] Gripper_485.py:386 - 移动到位置 0xF3
2025-07-22 22:08:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF3, 力矩=0xFF
2025-07-22 22:08:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:55 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:55 [INFO] Gripper_485.py:386 - 移动到位置 0xF2
2025-07-22 22:08:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF2, 力矩=0xFF
2025-07-22 22:08:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:55 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:55 [INFO] Gripper_485.py:386 - 移动到位置 0xF4
2025-07-22 22:08:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF4, 力矩=0xFF
2025-07-22 22:08:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:55 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:55 [INFO] Gripper_485.py:386 - 移动到位置 0xF5
2025-07-22 22:08:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF5, 力矩=0xFF
2025-07-22 22:08:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:55 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:55 [INFO] Gripper_485.py:386 - 移动到位置 0xF5
2025-07-22 22:08:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF5, 力矩=0xFF
2025-07-22 22:08:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:55 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:55 [INFO] Gripper_485.py:386 - 移动到位置 0xF5
2025-07-22 22:08:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF5, 力矩=0xFF
2025-07-22 22:08:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:55 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:55 [INFO] Gripper_485.py:386 - 移动到位置 0xF6
2025-07-22 22:08:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF6, 力矩=0xFF
2025-07-22 22:08:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:55 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:55 [INFO] Gripper_485.py:386 - 移动到位置 0xF5
2025-07-22 22:08:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF5, 力矩=0xFF
2025-07-22 22:08:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:55 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:55 [INFO] Gripper_485.py:386 - 移动到位置 0xF6
2025-07-22 22:08:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF6, 力矩=0xFF
2025-07-22 22:08:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:55 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:55 [INFO] Gripper_485.py:386 - 移动到位置 0xF6
2025-07-22 22:08:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF6, 力矩=0xFF
2025-07-22 22:08:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:55 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:55 [INFO] Gripper_485.py:386 - 移动到位置 0xF6
2025-07-22 22:08:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF6, 力矩=0xFF
2025-07-22 22:08:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:55 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:55 [INFO] Gripper_485.py:386 - 移动到位置 0xF6
2025-07-22 22:08:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF6, 力矩=0xFF
2025-07-22 22:08:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:55 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:55 [INFO] Gripper_485.py:386 - 移动到位置 0xF7
2025-07-22 22:08:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF7, 力矩=0xFF
2025-07-22 22:08:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:55 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:55 [INFO] Gripper_485.py:386 - 移动到位置 0xF7
2025-07-22 22:08:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF7, 力矩=0xFF
2025-07-22 22:08:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:55 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:55 [INFO] Gripper_485.py:386 - 移动到位置 0xF6
2025-07-22 22:08:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF6, 力矩=0xFF
2025-07-22 22:08:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:55 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:55 [INFO] Gripper_485.py:386 - 移动到位置 0xF7
2025-07-22 22:08:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF7, 力矩=0xFF
2025-07-22 22:08:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:55 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:55 [INFO] Gripper_485.py:386 - 移动到位置 0xF6
2025-07-22 22:08:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF6, 力矩=0xFF
2025-07-22 22:08:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:55 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:55 [INFO] Gripper_485.py:386 - 移动到位置 0xF6
2025-07-22 22:08:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF6, 力矩=0xFF
2025-07-22 22:08:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:55 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:55 [INFO] Gripper_485.py:386 - 移动到位置 0xF7
2025-07-22 22:08:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF7, 力矩=0xFF
2025-07-22 22:08:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:55 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:55 [INFO] Gripper_485.py:386 - 移动到位置 0xF7
2025-07-22 22:08:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF7, 力矩=0xFF
2025-07-22 22:08:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:55 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:55 [INFO] Gripper_485.py:386 - 移动到位置 0xF7
2025-07-22 22:08:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF7, 力矩=0xFF
2025-07-22 22:08:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:55 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:55 [INFO] Gripper_485.py:386 - 移动到位置 0xF7
2025-07-22 22:08:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF7, 力矩=0xFF
2025-07-22 22:08:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:55 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:55 [INFO] Gripper_485.py:386 - 移动到位置 0xF7
2025-07-22 22:08:55 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF7, 力矩=0xFF
2025-07-22 22:08:55 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:55 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:56 [INFO] Gripper_485.py:386 - 移动到位置 0xF7
2025-07-22 22:08:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF7, 力矩=0xFF
2025-07-22 22:08:56 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:56 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:56 [INFO] Gripper_485.py:386 - 移动到位置 0xF8
2025-07-22 22:08:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF8, 力矩=0xFF
2025-07-22 22:08:56 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:56 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:56 [INFO] Gripper_485.py:386 - 移动到位置 0xF8
2025-07-22 22:08:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF8, 力矩=0xFF
2025-07-22 22:08:56 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:56 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:56 [INFO] Gripper_485.py:386 - 移动到位置 0xF8
2025-07-22 22:08:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF8, 力矩=0xFF
2025-07-22 22:08:56 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:56 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:56 [INFO] Gripper_485.py:386 - 移动到位置 0xF8
2025-07-22 22:08:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF8, 力矩=0xFF
2025-07-22 22:08:56 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:56 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:56 [INFO] Gripper_485.py:386 - 移动到位置 0xF8
2025-07-22 22:08:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF8, 力矩=0xFF
2025-07-22 22:08:56 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:56 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:56 [INFO] Gripper_485.py:386 - 移动到位置 0xF8
2025-07-22 22:08:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF8, 力矩=0xFF
2025-07-22 22:08:56 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:56 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:56 [INFO] Gripper_485.py:386 - 移动到位置 0xF9
2025-07-22 22:08:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF9, 力矩=0xFF
2025-07-22 22:08:56 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:56 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:56 [INFO] Gripper_485.py:386 - 移动到位置 0xF8
2025-07-22 22:08:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF8, 力矩=0xFF
2025-07-22 22:08:56 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:56 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:56 [INFO] Gripper_485.py:386 - 移动到位置 0xFA
2025-07-22 22:08:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFA, 力矩=0xFF
2025-07-22 22:08:56 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:56 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:56 [INFO] Gripper_485.py:386 - 移动到位置 0xF9
2025-07-22 22:08:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF9, 力矩=0xFF
2025-07-22 22:08:56 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:56 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:56 [INFO] Gripper_485.py:386 - 移动到位置 0xFA
2025-07-22 22:08:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFA, 力矩=0xFF
2025-07-22 22:08:56 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:56 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:56 [INFO] Gripper_485.py:386 - 移动到位置 0xFB
2025-07-22 22:08:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFB, 力矩=0xFF
2025-07-22 22:08:56 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:56 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:56 [INFO] Gripper_485.py:386 - 移动到位置 0xFB
2025-07-22 22:08:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFB, 力矩=0xFF
2025-07-22 22:08:56 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:56 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:56 [INFO] Gripper_485.py:386 - 移动到位置 0xFC
2025-07-22 22:08:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFC, 力矩=0xFF
2025-07-22 22:08:56 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:56 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:56 [INFO] Gripper_485.py:386 - 移动到位置 0xFC
2025-07-22 22:08:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFC, 力矩=0xFF
2025-07-22 22:08:56 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:56 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:56 [INFO] Gripper_485.py:386 - 移动到位置 0xFB
2025-07-22 22:08:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFB, 力矩=0xFF
2025-07-22 22:08:56 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:56 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:56 [INFO] Gripper_485.py:386 - 移动到位置 0xFC
2025-07-22 22:08:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFC, 力矩=0xFF
2025-07-22 22:08:56 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:56 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:56 [INFO] Gripper_485.py:386 - 移动到位置 0xFC
2025-07-22 22:08:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFC, 力矩=0xFF
2025-07-22 22:08:56 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:56 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:56 [INFO] Gripper_485.py:386 - 移动到位置 0xFC
2025-07-22 22:08:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFC, 力矩=0xFF
2025-07-22 22:08:56 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:56 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:56 [INFO] Gripper_485.py:386 - 移动到位置 0xFC
2025-07-22 22:08:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFC, 力矩=0xFF
2025-07-22 22:08:56 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:56 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:56 [INFO] Gripper_485.py:386 - 移动到位置 0xFC
2025-07-22 22:08:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFC, 力矩=0xFF
2025-07-22 22:08:56 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:56 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:56 [INFO] Gripper_485.py:386 - 移动到位置 0xFD
2025-07-22 22:08:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFD, 力矩=0xFF
2025-07-22 22:08:56 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:56 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:56 [INFO] Gripper_485.py:386 - 移动到位置 0xFC
2025-07-22 22:08:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFC, 力矩=0xFF
2025-07-22 22:08:56 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:56 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:56 [INFO] Gripper_485.py:386 - 移动到位置 0xFE
2025-07-22 22:08:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFE, 力矩=0xFF
2025-07-22 22:08:56 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:56 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:56 [INFO] Gripper_485.py:386 - 移动到位置 0xFD
2025-07-22 22:08:56 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFD, 力矩=0xFF
2025-07-22 22:08:56 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:56 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:57 [INFO] Gripper_485.py:386 - 移动到位置 0xFD
2025-07-22 22:08:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFD, 力矩=0xFF
2025-07-22 22:08:57 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:57 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:57 [INFO] Gripper_485.py:386 - 移动到位置 0xFE
2025-07-22 22:08:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFE, 力矩=0xFF
2025-07-22 22:08:57 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:57 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:57 [INFO] Gripper_485.py:386 - 移动到位置 0xFE
2025-07-22 22:08:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFE, 力矩=0xFF
2025-07-22 22:08:57 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:57 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:57 [INFO] Gripper_485.py:386 - 移动到位置 0xFE
2025-07-22 22:08:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFE, 力矩=0xFF
2025-07-22 22:08:57 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:57 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:57 [INFO] Gripper_485.py:386 - 移动到位置 0xFE
2025-07-22 22:08:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFE, 力矩=0xFF
2025-07-22 22:08:57 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:57 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:57 [INFO] Gripper_485.py:386 - 移动到位置 0xFE
2025-07-22 22:08:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFE, 力矩=0xFF
2025-07-22 22:08:57 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:57 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:57 [INFO] Gripper_485.py:386 - 移动到位置 0xFE
2025-07-22 22:08:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFE, 力矩=0xFF
2025-07-22 22:08:57 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:57 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:57 [INFO] Gripper_485.py:386 - 移动到位置 0xFE
2025-07-22 22:08:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFE, 力矩=0xFF
2025-07-22 22:08:57 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:57 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:57 [INFO] Gripper_485.py:386 - 移动到位置 0xFE
2025-07-22 22:08:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFE, 力矩=0xFF
2025-07-22 22:08:57 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:57 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:57 [INFO] Gripper_485.py:386 - 移动到位置 0xFE
2025-07-22 22:08:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFE, 力矩=0xFF
2025-07-22 22:08:57 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:57 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:57 [INFO] Gripper_485.py:386 - 移动到位置 0xFE
2025-07-22 22:08:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFE, 力矩=0xFF
2025-07-22 22:08:57 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:57 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:57 [INFO] Gripper_485.py:386 - 移动到位置 0xFD
2025-07-22 22:08:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFD, 力矩=0xFF
2025-07-22 22:08:57 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:57 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:57 [INFO] Gripper_485.py:386 - 移动到位置 0xFD
2025-07-22 22:08:57 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFD, 力矩=0xFF
2025-07-22 22:08:57 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:57 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:58 [INFO] Gripper_485.py:386 - 移动到位置 0xEE
2025-07-22 22:08:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xEE, 力矩=0xFF
2025-07-22 22:08:58 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:58 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:58 [INFO] Gripper_485.py:386 - 移动到位置 0xE6
2025-07-22 22:08:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xE6, 力矩=0xFF
2025-07-22 22:08:58 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:58 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:58 [INFO] Gripper_485.py:386 - 移动到位置 0xE4
2025-07-22 22:08:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xE4, 力矩=0xFF
2025-07-22 22:08:58 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:58 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:58 [INFO] Gripper_485.py:386 - 移动到位置 0xE5
2025-07-22 22:08:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xE5, 力矩=0xFF
2025-07-22 22:08:58 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:58 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:58 [INFO] Gripper_485.py:386 - 移动到位置 0xE7
2025-07-22 22:08:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xE7, 力矩=0xFF
2025-07-22 22:08:58 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:58 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:58 [INFO] Gripper_485.py:386 - 移动到位置 0xEA
2025-07-22 22:08:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xEA, 力矩=0xFF
2025-07-22 22:08:58 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:58 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:58 [INFO] Gripper_485.py:386 - 移动到位置 0xEF
2025-07-22 22:08:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xEF, 力矩=0xFF
2025-07-22 22:08:58 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:58 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:58 [INFO] Gripper_485.py:386 - 移动到位置 0xEF
2025-07-22 22:08:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xEF, 力矩=0xFF
2025-07-22 22:08:58 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:58 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:58 [INFO] Gripper_485.py:386 - 移动到位置 0xF0
2025-07-22 22:08:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF0, 力矩=0xFF
2025-07-22 22:08:58 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:58 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:58 [INFO] Gripper_485.py:386 - 移动到位置 0xF2
2025-07-22 22:08:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF2, 力矩=0xFF
2025-07-22 22:08:58 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:58 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:58 [INFO] Gripper_485.py:386 - 移动到位置 0xF3
2025-07-22 22:08:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF3, 力矩=0xFF
2025-07-22 22:08:58 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:58 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:58 [INFO] Gripper_485.py:386 - 移动到位置 0xF2
2025-07-22 22:08:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF2, 力矩=0xFF
2025-07-22 22:08:58 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:58 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:58 [INFO] Gripper_485.py:386 - 移动到位置 0xF3
2025-07-22 22:08:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF3, 力矩=0xFF
2025-07-22 22:08:58 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:58 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:58 [INFO] Gripper_485.py:386 - 移动到位置 0xF3
2025-07-22 22:08:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF3, 力矩=0xFF
2025-07-22 22:08:58 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:58 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:58 [INFO] Gripper_485.py:386 - 移动到位置 0xF3
2025-07-22 22:08:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF3, 力矩=0xFF
2025-07-22 22:08:58 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:58 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 249, 'velocity': 0, 'force': 14, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:58 [INFO] Gripper_485.py:386 - 移动到位置 0xF3
2025-07-22 22:08:58 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF3, 力矩=0xFF
2025-07-22 22:08:58 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:58 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 233, 'velocity': 78, 'force': 92, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:59 [INFO] Gripper_485.py:386 - 移动到位置 0xF4
2025-07-22 22:08:59 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF4, 力矩=0xFF
2025-07-22 22:08:59 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:59 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 233, 'velocity': 78, 'force': 92, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:59 [INFO] Gripper_485.py:386 - 移动到位置 0xF5
2025-07-22 22:08:59 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF5, 力矩=0xFF
2025-07-22 22:08:59 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:59 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 233, 'velocity': 78, 'force': 92, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:59 [INFO] Gripper_485.py:386 - 移动到位置 0xF5
2025-07-22 22:08:59 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF5, 力矩=0xFF
2025-07-22 22:08:59 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:59 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 233, 'velocity': 78, 'force': 92, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:59 [INFO] Gripper_485.py:386 - 移动到位置 0xF4
2025-07-22 22:08:59 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF4, 力矩=0xFF
2025-07-22 22:08:59 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:59 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 233, 'velocity': 78, 'force': 92, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:59 [INFO] Gripper_485.py:386 - 移动到位置 0xF4
2025-07-22 22:08:59 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF4, 力矩=0xFF
2025-07-22 22:08:59 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:59 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 233, 'velocity': 78, 'force': 92, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:59 [INFO] Gripper_485.py:386 - 移动到位置 0xF3
2025-07-22 22:08:59 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF3, 力矩=0xFF
2025-07-22 22:08:59 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:59 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 233, 'velocity': 78, 'force': 92, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:59 [INFO] Gripper_485.py:386 - 移动到位置 0xF4
2025-07-22 22:08:59 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF4, 力矩=0xFF
2025-07-22 22:08:59 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:59 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 233, 'velocity': 78, 'force': 92, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:59 [INFO] Gripper_485.py:386 - 移动到位置 0xF3
2025-07-22 22:08:59 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF3, 力矩=0xFF
2025-07-22 22:08:59 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:59 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 233, 'velocity': 78, 'force': 92, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:59 [INFO] Gripper_485.py:386 - 移动到位置 0xF4
2025-07-22 22:08:59 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF4, 力矩=0xFF
2025-07-22 22:08:59 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:59 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 233, 'velocity': 78, 'force': 92, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:59 [INFO] Gripper_485.py:386 - 移动到位置 0xF5
2025-07-22 22:08:59 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF5, 力矩=0xFF
2025-07-22 22:08:59 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:59 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 233, 'velocity': 78, 'force': 92, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:59 [INFO] Gripper_485.py:386 - 移动到位置 0xF5
2025-07-22 22:08:59 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF5, 力矩=0xFF
2025-07-22 22:08:59 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:59 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 233, 'velocity': 78, 'force': 92, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:59 [INFO] Gripper_485.py:386 - 移动到位置 0xF6
2025-07-22 22:08:59 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF6, 力矩=0xFF
2025-07-22 22:08:59 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:59 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 233, 'velocity': 78, 'force': 92, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:59 [INFO] Gripper_485.py:386 - 移动到位置 0xF6
2025-07-22 22:08:59 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF6, 力矩=0xFF
2025-07-22 22:08:59 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:59 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 233, 'velocity': 78, 'force': 92, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:59 [INFO] Gripper_485.py:386 - 移动到位置 0xF8
2025-07-22 22:08:59 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xF8, 力矩=0xFF
2025-07-22 22:08:59 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:59 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 233, 'velocity': 78, 'force': 92, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:59 [INFO] Gripper_485.py:386 - 移动到位置 0xFC
2025-07-22 22:08:59 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFC, 力矩=0xFF
2025-07-22 22:08:59 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:59 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 1, 'position': 233, 'velocity': 78, 'force': 92, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '夹爪移动中'}
2025-07-22 22:08:59 [INFO] Gripper_485.py:386 - 移动到位置 0xFE
2025-07-22 22:08:59 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xFE, 力矩=0xFF
2025-07-22 22:08:59 [DEBUG] Gripper_485.py:209 - 指令已发送，等待回复...
2025-07-22 22:08:59 [INFO] Gripper_485.py:223 - 收到状态回复: {'id': 1, 'fault_code': 0, 'state': 0, 'position': 243, 'velocity': 11, 'force': 115, 'reserved1': 255, 'reserved2': 0, 'reserved3': 0, 'fault_description': '无故障', 'state_description': '已达到目标位置'}
