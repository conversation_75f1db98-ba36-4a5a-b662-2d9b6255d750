2025-07-22 22:27:34 [INFO] logger_config.py:102 - 日志系统初始化完成，日志文件: /home/<USER>/Spatial_AI/logs/gripper_485/gripper_485_1_20250722_222734.log
2025-07-22 22:27:34 [INFO] Gripper_485.py:53 - 初始化RS485夹爪 - 端口: /dev/ttyUSB1, 节点ID: 1, 波特率: 115200
2025-07-22 22:27:34 [DEBUG] Gripper_485.py:92 - 启动RS485消息接收线程
2025-07-22 22:27:34 [INFO] Gripper_485.py:96 - RS485消息接收线程已启动
2025-07-22 22:27:34 [INFO] Gripper_485.py:53 - 初始化RS485夹爪 - 端口: /dev/ttyUSB2, 节点ID: 1, 波特率: 115200
2025-07-22 22:27:34 [DEBUG] Gripper_485.py:92 - 启动RS485消息接收线程
2025-07-22 22:27:34 [INFO] Gripper_485.py:96 - RS485消息接收线程已启动
2025-07-22 22:27:36 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:36 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:36 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:36 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:36 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:36 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:36 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:36 [INFO] Gripper_485.py:386 - 移动到位置 0xED
2025-07-22 22:27:36 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xED, 力矩=0xFF
2025-07-22 22:27:37 [INFO] Gripper_485.py:386 - 移动到位置 0xE7
2025-07-22 22:27:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0xE7, 力矩=0xFF
2025-07-22 22:27:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:37 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:37 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:38 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:38 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:39 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:39 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:40 [INFO] Gripper_485.py:386 - 移动到位置 0x00
2025-07-22 22:27:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x00, 力矩=0xFF
2025-07-22 22:27:40 [INFO] Gripper_485.py:386 - 移动到位置 0x98
2025-07-22 22:27:40 [INFO] Gripper_485.py:178 - 发送指令: 节点ID=1, 位置=0x98, 力矩=0xFF
