#!/usr/bin/env python3
import math
from collections import deque

class JointSmoother:
    """关节数据平滑处理器"""
    def __init__(self, window_size=5, alpha=0.3):
        """
        初始化平滑处理器
        :param window_size: 移动平均窗口大小
        :param alpha: 指数平滑系数 (0-1)
        """
        self.window_size = window_size
        self.alpha = alpha
        self.joint_buffers = [deque(maxlen=window_size) for _ in range(7)]  # 7个关节
        self.last_smoothed = [0.0] * 7  # 上次平滑后的值
    
    def smooth_joints(self, joint_values):
        """
        平滑处理关节数据
        :param joint_values: 7个关节的弧度值列表
        :return: 平滑后的关节值列表
        """
        smoothed_values = []
        
        for i, value in enumerate(joint_values):
            # 添加到缓冲区
            self.joint_buffers[i].append(value)
            
            if len(self.joint_buffers[i]) < self.window_size:
                # 缓冲区未满，使用当前值
                smoothed_value = value
            else:
                # 计算移动平均
                moving_avg = sum(self.joint_buffers[i]) / len(self.joint_buffers[i])
                
                # 结合指数平滑
                smoothed_value = self.alpha * value + (1 - self.alpha) * self.last_smoothed[i]
                
                # 进一步平滑：结合移动平均和指数平滑
                smoothed_value = 0.7 * moving_avg + 0.3 * smoothed_value
            
            self.last_smoothed[i] = smoothed_value
            smoothed_values.append(smoothed_value)
        
        return smoothed_values

def test_smoothing():
    """测试平滑处理器"""
    smoother = JointSmoother(window_size=5, alpha=0.3)
    
    # 模拟关节1的运动数据（从0度到30度）
    test_angles = [0, 5, 10, 15, 20, 25, 30]  # 角度值
    
    print("测试平滑处理器对关节1的影响：")
    print("原始角度 -> 弧度 -> 平滑后弧度 -> 平滑后角度")
    print("-" * 60)
    
    for i, angle in enumerate(test_angles):
        # 转换为弧度
        rad = math.radians(angle)
        
        # 创建测试数据（关节1有变化，其他关节保持0）
        joint_values = [rad, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]
        
        # 应用平滑
        smoothed = smoother.smooth_joints(joint_values)
        
        # 转换回角度
        smoothed_angle = math.degrees(smoothed[0])
        
        print(f"步骤{i+1}: {angle:6.1f}° -> {rad:6.3f}rad -> {smoothed[0]:6.3f}rad -> {smoothed_angle:6.1f}°")
        
        # 检查是否有显著差异
        if abs(smoothed_angle - angle) > 1.0:
            print(f"  ⚠️  警告：平滑后角度差异较大！差异: {abs(smoothed_angle - angle):.1f}°")

if __name__ == "__main__":
    test_smoothing() 