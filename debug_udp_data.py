#!/usr/bin/env python3
import socket
import struct
import time
from datetime import datetime

def debug_udp_data():
    """调试UDP数据结构"""
    print("=== UDP数据调试 ===")
    
    # 创建UDP接收器
    sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
    sock.bind(('0.0.0.0', 8080))
    sock.settimeout(5.0)
    
    print("等待UDP数据...")
    
    try:
        while True:
            try:
                data, addr = sock.recvfrom(1024)
                print(f"\n收到来自 {addr} 的数据包，长度: {len(data)}")
                
                # 检查数据包结构
                if len(data) >= 85:
                    # 检查帧头
                    if data[0] == 0xAA and data[1] == 0xBB:
                        print("✓ 帧头正确 (0xAA 0xBB)")
                        
                        # 解析数据长度
                        data_length = data[2]
                        print(f"数据长度: {data_length}")
                        
                        # 解析扭矩值
                        torque_raw = struct.unpack('<h', data[3:5])[0]
                        torque_value = torque_raw / 10.0
                        print(f"扭矩值: {torque_value:.1f}")
                        
                        # 解析所有角度数据
                        angles = []
                        offset = 5
                        for i in range(16):
                            angle_bytes = data[offset + i*4:offset + (i+1)*4]
                            angle_value = struct.unpack('<f', angle_bytes)[0]
                            angles.append(angle_value)
                        
                        print("所有角度数据:")
                        for i, angle in enumerate(angles):
                            print(f"  索引{i}: {angle:8.2f}°")
                        
                        # 分析右手关节数据
                        right_angles = angles[8:16]
                        print("\n右手关节数据 (索引8-15):")
                        for i, angle in enumerate(right_angles):
                            print(f"  关节{i+1}: {angle:8.2f}°")
                        
                        # 检查哪个关节有显著变化
                        print("\n角度变化分析:")
                        for i, angle in enumerate(angles):
                            if abs(angle) > 5.0:  # 大于5度的关节
                                print(f"  索引{i}: {angle:8.2f}° (可能有变化)")
                        
                        break  # 收到一个有效数据包后退出
                    else:
                        print("✗ 帧头错误")
                else:
                    print(f"✗ 数据包长度不足: {len(data)} < 85")
                    
            except socket.timeout:
                print("等待超时，未收到数据")
                break
                
    except KeyboardInterrupt:
        print("\n用户中断")
    finally:
        sock.close()

if __name__ == "__main__":
    debug_udp_data() 