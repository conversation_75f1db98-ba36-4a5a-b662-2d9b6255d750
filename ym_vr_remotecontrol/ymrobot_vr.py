#!/usr/bin/env python3
import rclpy
from rclpy.node import Node
from geometry_msgs.msg import Po<PERSON>, Point, Quaternion
from ymrobot import YMrobot
import time

class YMrobotNode(Node):
    def __init__(self):
        super().__init__('ymrobot_node')
        self.robot = YMrobot()

        # Define two angle positions for the right arm
        self.angle_position_1 = [-0.17453, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]
        self.angle_position_2 = [0.17453, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]
        
        # Create a timer that calls the angle switching function every 3 seconds
        self.current_position = 1  # Start with position 1
        self.timer = self.create_timer(3.0, self.switch_angle_position)
        
        # Initial position
        self.robot.control_right_arm_joint_position(self.angle_position_1)
        self.get_logger().info(f'Initial position set to: {self.angle_position_1}')

        #初始位置
        # right_arm_joints = [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]
        # self.robot.control_right_arm_joint_position(right_arm_joints)

    def switch_angle_position(self):
        if self.current_position == 1:
            # Switch to position 2
            self.robot.control_right_arm_joint_position(self.angle_position_2)
            self.current_position = 2
            self.get_logger().info(f'Switched to position 2: {self.angle_position_2}')
        else:
            # Switch to position 1
            self.robot.control_right_arm_joint_position(self.angle_position_1)
            self.current_position = 1
            self.get_logger().info(f'Switched to position 1: {self.angle_position_1}')
    
    def destroy_node(self):
        self.robot.shutdown()
        super().destroy_node()

def main(args=None):
    rclpy.init(args=args)
    node = YMrobotNode()
    rclpy.spin(node)
    node.destroy_node()
    rclpy.shutdown()

if __name__ == '__main__':
    main()
