#!/usr/bin/env python3
import rclpy
from rclpy.node import Node
from geometry_msgs.msg import Pose, Point, Quaternion
from ymrobot import YMrobot
from sensor_msgs.msg import JointState
import socket
import struct
import time
import math
import numpy as np
from datetime import datetime
import threading

class UDPRobotController:
    def __init__(self, host='0.0.0.0', port=8080):
        self.host = host
        self.port = port
        self.socket = None
        self.running = False
        self.robot = None
        self.rclpy_initialized = False
        
    def initialize_robot(self):
        """初始化机器人"""
        try:
            if not self.rclpy_initialized:
                rclpy.init()
                self.rclpy_initialized = True
            
            self.robot = YMrobot()
            time.sleep(2.0)  # 等待动作完成
            print("机器人初始化成功")
            return True
        except Exception as e:
            print(f"机器人初始化失败: {e}")
            return False
    
    def shutdown_robot(self):
        """关闭机器人"""
        if self.robot:
            self.robot.shutdown()
        if self.rclpy_initialized:
            rclpy.shutdown()
        print("机器人已关闭")
    
    def start_udp(self):
        """启动UDP接收器"""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            self.socket.bind((self.host, self.port))
            self.socket.settimeout(1.0)  # 设置超时时间
            self.running = True
            print(f"UDP接收器已启动，监听地址: {self.host}:{self.port}")
            print("等待数据...")
            return True
        except Exception as e:
            print(f"启动UDP接收器失败: {e}")
            return False
    
    def stop_udp(self):
        """停止UDP接收器"""
        self.running = False
        if self.socket:
            self.socket.close()
            print("UDP接收器已停止")
    
    def parse_data_packet(self, data):
        """解析数据包"""
        try:
            if len(data) < 85:  # 最小数据包长度检查
                return None
            
            # 检查帧头
            if data[0] != 0xAA or data[1] != 0xBB:
                return None
            
            # 解析数据长度
            data_length = data[2]
            if data_length != 16:
                return None
            
            # 解析扭矩值 (2字节)
            torque_raw = struct.unpack('<h', data[3:5])[0]
            torque_value = torque_raw / 10.0  # 转换为实际扭矩值
            
            # 解析角度数据 (16个float，每个4字节)
            angles = []
            offset = 5
            for i in range(16):
                angle_bytes = data[offset + i*4:offset + (i+1)*4]
                angle_value = struct.unpack('<f', angle_bytes)[0]
                angles.append(angle_value)
            
            return {
                'torque': torque_value,
                'angles': angles,
                'timestamp': datetime.now()
            }
            
        except Exception as e:
            return None
    
    def control_right_arm(self, angles):
        """控制右臂关节"""
        if not self.robot:
            print("机器人未初始化")
            return
        
        try:
            # 获取右手关节角度 (索引8-15，对应右手关节1-8)
            right_angles = angles[8:16]
            
            # 使用关节1和关节2，其他关节设为0.0
            # 将角度值转换为弧度值
            joint1_rad = math.radians(right_angles[0])  # 关节1角度转弧度
            joint2_rad = math.radians(right_angles[1])  # 关节2角度转弧度
            joint3_rad = math.radians(right_angles[2])  # 关节3角度转弧度
            joint4_rad = math.radians(right_angles[3])  # 关节4角度转弧度
            joint5_rad = math.radians(right_angles[4])  # 关节5角度转弧度
            joint6_rad = math.radians(right_angles[5])  # 关节6角度转弧度
            joint7_rad = math.radians(right_angles[6])  # 关节7角度转弧度

            # 构建控制数组 [关节1, 关节2, 关节3, 关节4, 关节5, 关节6, 关节7]
            control_values = [joint1_rad, joint2_rad, joint3_rad, joint4_rad, joint5_rad, joint6_rad, joint7_rad]
            
            # 控制右臂
            self.robot.control_right_arm_joint_position_ros2controller(control_values)
            
            print(f"控制右臂 - 1: ({joint1_rad:.3f}rad), 2: ({joint2_rad:.3f}rad), 3: ({joint3_rad:.3f}rad), 关节4: {right_angles[3]:.1f}° ({joint4_rad:.3f}rad), 关节5: {right_angles[4]:.1f}° ({joint5_rad:.3f}rad), 关节6: {right_angles[5]:.1f}° ({joint6_rad:.3f}rad), 关节7: {right_angles[6]:.1f}° ({joint7_rad:.3f}rad)")
            
        except Exception as e:
            print(f"控制右臂时出错: {e}")
    
    def control_gripper(self, gripper_value):
        """控制夹爪"""
        if not self.robot:
            print("机器人未初始化")
            return
        
        try:
            # UDP接收的夹爪数值是0-100，需要翻转转换为100-0
            # 翻转数值范围：100 - 原始值
            flipped_value = int(100.0 - gripper_value)
            
            # 控制夹爪
            self.robot.move_gripper_to_position("right", flipped_value)
            
            print(f"控制夹爪 - 原始值: {gripper_value:.1f}, 翻转值: {flipped_value}")
            
        except Exception as e:
            print(f"控制夹爪时出错: {e}")
    
    def run(self):
        """运行主循环"""
        # 初始化机器人
        if not self.initialize_robot():
            return
        
        # 启动UDP接收器
        if not self.start_udp():
            return
        
        packet_count = 0
        start_time = time.time()
        
        try:
            while self.running:
                try:
                    # 接收数据
                    data, addr = self.socket.recvfrom(1024)
                    packet_count += 1
                    
                    # 解析数据包
                    parsed_data = self.parse_data_packet(data)
                    
                    if parsed_data:
                        # 控制右臂
                        self.control_right_arm(parsed_data['angles'])
                        
                        # 控制夹爪 - 从右手关节8获取夹爪数值
                        gripper_value = parsed_data['angles'][15]  # 右手关节8 (索引15)
                        #self.control_gripper(gripper_value)
                        print(f"夹爪值: {gripper_value:.1f}")
                        
                        # 每秒显示统计信息
                        elapsed_time = time.time() - start_time
                        if elapsed_time >= 1.0:
                            fps = packet_count / elapsed_time
                            print(f"FPS: {fps:.1f}")
                            packet_count = 0
                            start_time = time.time()
                        
                except socket.timeout:
                    # 超时，继续循环
                    continue
                except Exception as e:
                    print(f"接收数据时出错: {e}")
                    
        except KeyboardInterrupt:
            print("\n用户中断，正在停止...")
        finally:
            self.stop_udp()
            self.shutdown_robot()

def main():
    """主函数"""
    print("UDP机器人控制器")
    print("根据UDP接收的角度数据控制机器人右臂和夹爪")
    print("当前使用关节1和关节2，其他关节设为0.0")
    print("夹爪数值范围：UDP接收0-100，控制时翻转为100-0")
    print("按 Ctrl+C 停止程序")
    print()
    
    # 创建控制器
    controller = UDPRobotController(host='0.0.0.0', port=8080)
    
    try:
        controller.run()
    except Exception as e:
        print(f"程序运行出错: {e}")

if __name__ == "__main__":
    main()
