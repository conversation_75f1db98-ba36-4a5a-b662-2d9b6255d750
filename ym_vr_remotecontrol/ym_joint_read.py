#!/usr/bin/env python3
import rclpy
from rclpy.node import Node
from geometry_msgs.msg import Pose, Point, Quaternion
from ymrobot import YMrobot
from sensor_msgs.msg import JointState
import math

class YMrobotNode(Node):
    def __init__(self):
        super().__init__('ymrobot_node')
        self.robot = YMrobot()

        # 定义关节顺序（固定）
        self.joint_order = [
            'Left_Arm_Joint1', 'Left_Arm_Joint2', 'Left_Arm_Joint3', 'Left_Arm_Joint4',
            'Left_Arm_Joint5', 'Left_Arm_Joint6', 'Left_Arm_Joint7',
            'Right_Arm_Joint1', 'Right_Arm_Joint2', 'Right_Arm_Joint3', 'Right_Arm_Joint4',
            'Right_Arm_Joint5', 'Right_Arm_Joint6', 'Right_Arm_Joint7',
            'Body_Joint1', 'Body_Joint2', 'Body_Joint3', 'Body_Joint4',
            'Neck_Joint1', 'Neck_Joint2'
        ]

        # 初始化关节数组（nan 表示尚未收到数据）
        self.joint_positions = [float('nan')] * len(self.joint_order)

        self.subscription = self.create_subscription(
            JointState,
            '/joint_states',
            self.listener_callback,
            10
        )

    def listener_callback(self, msg: JointState):
        name_to_pos = dict(zip(msg.name, msg.position))

        # 重新排序并更新 joint_positions
        for i, joint_name in enumerate(self.joint_order):
            self.joint_positions[i] = name_to_pos.get(joint_name, float('nan'))
            
        # 只显示前7个角度（左臂），并转换为角度制，保留1位小数
        left_arm_angles = []
        for i in range(7):  # 只取前7个关节（左臂）
            if not math.isnan(self.joint_positions[i]):
                # 将弧度转换为角度并四舍五入到1位小数
                angle_deg = round(math.degrees(self.joint_positions[i]), 3)
                left_arm_angles.append(angle_deg)
            else:
                left_arm_angles.append(float('nan'))
                
        # 打印左臂关节角度（角度制）
        self.get_logger().info(str(left_arm_angles))


        # Example: Control right arm joints
        # right_arm_joints = [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]
        # self.robot.control_right_arm_joint_position(right_arm_joints)

        # # Example: Control both arms' end-effectors
        # left_pose = Pose(position=Point(x=0.40, y=0.28, z=0.85), orientation=Quaternion(x=-0.0343, y=0.1710, z=-0.0670, w=0.9819)) # 末端斜向下 20°，偏右 15°
        # right_pose = Pose(position=Point(x=0.32, y=-0.28, z=0.83), orientation=Quaternion(x=0.0, y=0.2588, z=0.0, w=0.9659))
        # self.robot.control_both_arms_end_effector(left_pose, right_pose)

        # # Example: Get joint states
        # joint_states = self.robot.get_full_joint_state()
        # if joint_states:
        #     self.get_logger().info(f"Joint states: {joint_states}")

        # # Example: Get head RGB
        # head_rgb, head_rgb_stamp = self.robot.get_head_rgb()
        # if head_rgb is not None:
        #     self.get_logger().info(f"Head RGB shape: {head_rgb.shape}")
        # print(f"Head RGB timestamp: {head_rgb_stamp}")

        # # Example: Reset
        # self.robot.reset()

    def destroy_node(self):
        self.robot.shutdown()
        super().destroy_node()

def main(args=None):
    rclpy.init(args=args)
    node = YMrobotNode()
    rclpy.spin(node)
    node.destroy_node()
    rclpy.shutdown()

if __name__ == '__main__':
    main()