import socket
import struct
import time
from datetime import datetime

class UDPReceiver:
    def __init__(self, host='0.0.0.0', port=8080):
        self.host = host
        self.port = port
        self.socket = None
        self.running = False
        
    def start(self):
        """启动UDP接收器"""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            self.socket.bind((self.host, self.port))
            self.socket.settimeout(1.0)  # 设置超时时间
            self.running = True
            print(f"UDP接收器已启动，监听地址: {self.host}:{self.port}")
            print("等待数据...")

            
        except Exception as e:
            print(f"启动UDP接收器失败: {e}")
            return False
        return True
    
    def stop(self):
        """停止UDP接收器"""
        self.running = False
        if self.socket:
            self.socket.close()
            print("UDP接收器已停止")
    
    def parse_data_packet(self, data):
        """解析数据包"""
        try:
            if len(data) < 85:  # 最小数据包长度检查
                return None
            
            # 检查帧头
            if data[0] != 0xAA or data[1] != 0xBB:
                return None
            
            # 解析数据长度
            data_length = data[2]
            if data_length != 16:
                return None
            
            # 解析扭矩值 (2字节)
            torque_raw = struct.unpack('<h', data[3:5])[0]
            torque_value = torque_raw / 10.0  # 转换为实际扭矩值
            
            # 解析角度数据 (16个float，每个4字节)
            angles = []
            offset = 5
            for i in range(16):
                angle_bytes = data[offset + i*4:offset + (i+1)*4]
                angle_value = struct.unpack('<f', angle_bytes)[0]
                angles.append(angle_value)
            
            return {
                'torque': torque_value,
                'angles': angles,
                'timestamp': datetime.now()
            }
            
        except Exception as e:
            return None
    
    def print_angles_data(self, data):
        """打印角度数据（简化版）"""
        if not data:
            return
        
        # 左手数据 (索引0-7)
        left_angles = data['angles'][:8]
        # 右手数据 (索引8-15)
        right_angles = data['angles'][8:16]
        
        # 构建显示字符串
        display_str = f"左手: "
        for i, angle in enumerate(left_angles):
            if i == 7:  # 夹爪值
                display_str += f"夹爪:{angle:6.1f} "
            else:
                display_str += f"J{i+1}:{angle:6.1f} "
        
        display_str += "| 右手: "
        for i, angle in enumerate(right_angles):
            if i == 7:  # 夹爪值
                display_str += f"夹爪:{angle:6.1f} "
            else:
                display_str += f"J{i+1}:{angle:6.1f} "
        
        # 使用\r在同一行刷新显示
        print(f"\r{display_str}", end='', flush=True)
    
    def run(self):
        """运行接收器"""
        if not self.start():
            return
        
        packet_count = 0
        start_time = time.time()
        
        try:
            while self.running:
                try:
                    # 接收数据
                    data, addr = self.socket.recvfrom(1024)
                    packet_count += 1
                    
                    # 解析数据包
                    parsed_data = self.parse_data_packet(data)
                    
                    if parsed_data:
                        # 打印角度数据
                        self.print_angles_data(parsed_data)
                        
                        # 每秒显示统计信息
                        elapsed_time = time.time() - start_time
                        if elapsed_time >= 1.0:
                            fps = packet_count / elapsed_time
                            print(f" | FPS: {fps:.1f}")
                            packet_count = 0
                            start_time = time.time()
                        
                except socket.timeout:
                    # 超时，继续循环
                    continue
                except Exception as e:
                    print(f"\n接收数据时出错: {e}")
                    
        except KeyboardInterrupt:
            print("\n用户中断，正在停止...")
        finally:
            self.stop()

def main():
    """主函数"""
    print("UDP数据接收器 - 简化显示模式")
    print("显示格式: 左手关节角度 | 右手关节角度 | FPS")
    print("按 Ctrl+C 停止程序")
    print()
    
    # 创建接收器
    receiver = UDPReceiver(host='0.0.0.0', port=8080)
    
    try:
        receiver.run()
    except Exception as e:
        print(f"程序运行出错: {e}")

if __name__ == "__main__":
    main() 