#!/usr/bin/env python3
"""
测试夹爪状态发布功能
"""
import rclpy
from rclpy.node import Node
from std_msgs.msg import Float64MultiArray
import time

class GripperStateListener(Node):
    def __init__(self):
        super().__init__('gripper_state_listener')
        
        # 订阅夹爪状态
        self.subscription = self.create_subscription(
            Float64MultiArray,
            '/gripper_states',
            self.gripper_state_callback,
            10
        )
        self.get_logger().info("Gripper state listener started")
        
    def gripper_state_callback(self, msg):
        """处理夹爪状态消息"""
        if len(msg.data) >= 2:
            left_position = msg.data[0]
            right_position = msg.data[1]
            self.get_logger().info(
                f"Gripper states - Left: {left_position:.1f}, Right: {right_position:.1f}"
            )
        else:
            self.get_logger().warn(f"Invalid gripper state message: {msg.data}")

def main():
    rclpy.init()
    
    listener = GripperStateListener()
    
    try:
        rclpy.spin(listener)
    except KeyboardInterrupt:
        pass
    finally:
        listener.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
